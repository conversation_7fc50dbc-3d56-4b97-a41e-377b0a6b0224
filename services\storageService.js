import axios from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { SERVER_IP, STORAGE_KEYS } from '../config/constants';

/**
 * Ottieni i dettagli dello spazio di archiviazione
 * @returns {Promise<Object>} Dettagli dello spazio di archiviazione
 */
/**
 * Svuota la cache dei media
 * @returns {Promise<Object>} Risultato dell'operazione
 */
export const clearMediaCache = async () => {
  try {
    // Ottieni tutte le chiavi dallo storage
    const keys = await AsyncStorage.getAllKeys();

    // Filtra le chiavi relative ai media
    const mediaKeys = keys.filter(key =>
      key.startsWith('media_info_') ||
      key.startsWith('media_cache_') ||
      key.startsWith('image_cache_') ||
      key.startsWith('file_cache_')
    );

    console.log(`Trovati ${mediaKeys.length} elementi nella cache`);

    // Elimina le chiavi dei media
    if (mediaKeys.length > 0) {
      await AsyncStorage.multiRemove(mediaKeys);
      console.log(`Cache svuotata: ${mediaKeys.length} elementi rimossi`);
    } else {
      console.log('Nessun elemento nella cache da rimuovere');
    }

    // Pulisci anche la cache di FileSystem se disponibile
    try {
      const FileSystem = require('expo-file-system');
      if (FileSystem && FileSystem.cacheDirectory) {
        const cacheDir = FileSystem.cacheDirectory;
        console.log(`Pulizia della directory cache: ${cacheDir}`);

        // Ottieni la lista dei file nella cache
        const cacheFiles = await FileSystem.readDirectoryAsync(cacheDir);
        console.log(`Trovati ${cacheFiles.length} file nella directory cache`);

        // Elimina i file temporanei
        let deletedCount = 0;
        for (const file of cacheFiles) {
          if (file.startsWith('ImagePicker') || file.endsWith('.jpg') || file.endsWith('.png') || file.endsWith('.mp4')) {
            try {
              await FileSystem.deleteAsync(`${cacheDir}${file}`);
              deletedCount++;
            } catch (e) {
              console.warn(`Impossibile eliminare il file ${file}:`, e);
            }
          }
        }

        console.log(`Eliminati ${deletedCount} file dalla directory cache`);
      }
    } catch (fsError) {
      console.warn('Errore durante la pulizia della cache del filesystem:', fsError);
    }

    return {
      success: true,
      message: `Cache svuotata: ${mediaKeys.length} elementi rimossi`
    };
  } catch (error) {
    console.error('Errore durante la pulizia della cache:', error);
    // Non lanciare l'errore, restituisci un oggetto di errore
    return {
      success: false,
      message: `Errore: ${error.message}`,
      error: error
    };
  }
};

// Funzione per calcolare l'utilizzo dello storage localmente
const calculateLocalStorageUsage = async (userId) => {
  try {
    console.log('Calcolo locale dell\'utilizzo dello storage');

    // Ottieni tutte le chiavi dallo storage
    const keys = await AsyncStorage.getAllKeys();

    // Filtra le chiavi relative ai media
    const mediaKeys = keys.filter(key =>
      key.startsWith('media_info_') ||
      key.startsWith('media_cache_')
    );

    let totalSize = 0;
    let photoSize = 0;
    let videoSize = 0;
    let audioSize = 0;
    let documentSize = 0;
    let otherSize = 0;

    // Calcola lo spazio utilizzato per ogni tipo di file
    for (const key of mediaKeys) {
      try {
        const mediaInfoJson = await AsyncStorage.getItem(key);
        if (mediaInfoJson) {
          const mediaInfo = JSON.parse(mediaInfoJson);

          // Verifica che il media appartenga all'utente corrente
          if (mediaInfo.userId === userId) {
            const size = mediaInfo.size || 0;
            totalSize += size;

            // Classifica il file in base al tipo
            if (mediaInfo.type === 'image') {
              photoSize += size;
            } else if (mediaInfo.type === 'video') {
              videoSize += size;
            } else if (mediaInfo.type === 'audio') {
              audioSize += size;
            } else if (mediaInfo.type === 'document') {
              documentSize += size;
            } else {
              otherSize += size;
            }
          }
        }
      } catch (e) {
        console.warn('Errore nel parsing delle informazioni del media:', e);
      }
    }

    // Se non abbiamo trovato dati, restituisci valori minimi
    if (totalSize === 0) {
      // Ottieni la dimensione del profilo utente
      const userJson = await AsyncStorage.getItem('@trendychat:user');
      if (userJson) {
        const user = JSON.parse(userJson);
        if (user.photoBase64) {
          // Stima la dimensione dell'immagine del profilo
          const base64Size = user.photoBase64.length * 0.75; // Approssimazione
          totalSize = base64Size;
          photoSize = base64Size;
        }
      }
    }

    // Converti da bytes a MB per la visualizzazione
    const totalMB = totalSize / (1024 * 1024);
    const photoMB = photoSize / (1024 * 1024);
    const videoMB = videoSize / (1024 * 1024);
    const audioMB = audioSize / (1024 * 1024);
    const documentMB = documentSize / (1024 * 1024);
    const otherMB = otherSize / (1024 * 1024);

    // Limite di spazio (1GB)
    const storageLimit = 1073741824;

    // Spazio disponibile
    const availableSpace = storageLimit - totalSize;

    // Percentuale di utilizzo
    const percentUsed = (totalSize / storageLimit) * 100;

    return {
      quota: {
        used: totalSize,
        limit: storageLimit,
        available: availableSpace,
        percentUsed: parseFloat(percentUsed.toFixed(2))
      },
      details: {
        total: parseFloat(totalMB.toFixed(2)),
        photos: parseFloat(photoMB.toFixed(2)),
        videos: parseFloat(videoMB.toFixed(2)),
        audio: parseFloat(audioMB.toFixed(2)),
        documents: parseFloat(documentMB.toFixed(2)),
        other: parseFloat(otherMB.toFixed(2)),
        backup: 0,
        stories: 0
      }
    };
  } catch (error) {
    console.warn('Errore nel calcolo locale dello spazio utilizzato:', error);

    // In caso di errore, restituisci dati minimi
    return {
      quota: {
        used: 0,
        limit: 1073741824, // 1GB in bytes
        available: 1073741824
      },
      details: {
        total: 0,
        photos: 0,
        videos: 0,
        audio: 0,
        documents: 0,
        other: 0,
        backup: 0,
        stories: 0
      }
    };
  }
};

export const getStorageDetails = async () => {
  try {
    // Prova prima con la chiave utilizzata in authService.js
    let token = await AsyncStorage.getItem('@trendychat:token');

    // Se non trova il token, prova con la chiave standard
    if (!token) {
      token = await AsyncStorage.getItem(STORAGE_KEYS.AUTH_TOKEN);
    }

    console.log('Token trovato:', token ? 'Sì' : 'No');

    if (!token) {
      throw new Error('Token di autenticazione non trovato');
    }

    // Ottieni l'utente corrente
    const userJson = await AsyncStorage.getItem('@trendychat:user');
    let userId = '';

    if (userJson) {
      try {
        const user = JSON.parse(userJson);
        userId = user.id || user._id;
      } catch (e) {
        console.error('Errore nel parsing dei dati utente:', e);
      }
    }

    // Se non abbiamo trovato l'ID utente, proviamo a estrarlo dal token
    if (!userId) {
      const tokenParts = token.split('.');

      if (tokenParts.length > 1) {
        try {
          const payload = JSON.parse(atob(tokenParts[1]));
          userId = payload.id || payload.userId || payload._id || '';
        } catch (e) {
          console.error('Errore nel parsing del token:', e);
        }
      }
    }

    // Ultimo tentativo: prova a ottenere l'ID utente da authService
    if (!userId) {
      try {
        const authService = require('../services/authService').default;
        const currentUser = authService.getCurrentUser();
        if (currentUser && (currentUser.id || currentUser._id)) {
          userId = currentUser.id || currentUser._id;
        }
      } catch (e) {
        console.error('Errore nel recupero dell\'utente da authService:', e);
      }
    }

    if (!userId) {
      throw new Error('ID utente non trovato');
    }

    // Tenta di recuperare i dati reali dal server
    try {
      console.log('Recupero dati di archiviazione dal server per l\'utente:', userId);

      // Costruisci l'URL dell'API con l'ID utente
      const apiUrl = `http://${SERVER_IP}:3001/api/users/storage/${userId}`;
      console.log('URL API storage:', apiUrl);

      // Effettua la richiesta al server
      console.log('Invio richiesta al server con token:', token);
      const response = await fetch(apiUrl, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      // Se la richiesta ha successo, restituisci i dati dal server
      if (response.ok) {
        const data = await response.json();
        console.log('Dati di archiviazione ricevuti dal server:', data);

        return {
          quota: {
            used: data.used || 0,
            limit: data.limit || 1073741824, // 1GB in bytes
            available: data.available || 1073741824,
            percentUsed: data.percentUsed || 0
          },
          details: {
            total: data.details?.total || 0,
            photos: data.details?.photos || 0,
            videos: data.details?.videos || 0,
            audio: data.details?.audio || 0,
            documents: data.details?.documents || 0,
            other: data.details?.other || 0,
            backup: data.details?.backup || 0,
            stories: data.details?.stories || 0
          }
        };
      } else {
        console.warn('Errore nella risposta del server:', response.status);
        // Prova a leggere il messaggio di errore
        try {
          const errorData = await response.text();
          console.warn('Dettagli errore:', errorData);
        } catch (e) {
          console.warn('Impossibile leggere i dettagli dell\'errore');
        }
        // Se il server restituisce un errore, calcola i dati localmente
        return await calculateLocalStorageUsage(userId);
      }
    } catch (error) {
      console.warn('Errore nel recupero dei dati dal server:', error);
      console.warn('Dettagli errore:', error.message);
      // In caso di errore di connessione, calcola i dati localmente
      return await calculateLocalStorageUsage(userId);
    }
  } catch (error) {
    console.error('Errore nel recupero dei dettagli di archiviazione:', error);

    // In caso di errore, restituisci dati minimi
    return {
      quota: {
        used: 0,
        limit: 1073741824, // 1GB in bytes
        available: 1073741824
      },
      details: {
        total: 0,
        photos: 0,
        videos: 0,
        audio: 0,
        documents: 0,
        other: 0,
        backup: 0,
        stories: 0
      }
    };
  }
};
