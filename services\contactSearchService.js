import { API_URL } from '../config/api';
import apiService from './apiService';

/**
 * Servizio per la ricerca degli utenti tramite numero di telefono
 */
class ContactSearchService {
  constructor() {
    // Nessuna inizializzazione necessaria
  }

  /**
   * Normalizza un numero di telefono
   * @param {string} phoneNumber - Numero di telefono da normalizzare
   * @returns {string} - Numero di telefono normalizzato
   */
  normalizePhoneNumber(phoneNumber) {
    if (!phoneNumber) return '';

    // Rimuovi tutti i caratteri non numerici
    let normalized = phoneNumber.replace(/\\D/g, '');

    // Se il numero inizia con il prefisso internazionale dell'Italia (+39 o 0039)
    if (normalized.startsWith('39') && normalized.length > 10) {
      normalized = normalized.substring(2);
    }

    // Se il numero è più lungo di 10 cifre e non inizia con il prefisso internazionale
    // prendi le ultime 10 cifre (per l'Italia)
    if (normalized.length > 10) {
      normalized = normalized.substring(normalized.length - 10);
    }

    return normalized;
  }

  /**
   * Verifica se una stringa è un numero di telefono
   * @param {string} query - Stringa da verificare
   * @returns {boolean} - true se la stringa è un numero di telefono
   */
  isPhoneNumber(query) {
    // Rimuovi spazi e caratteri speciali
    const cleaned = query.replace(/[\\s\\-\\(\\)\\+]/g, '');

    // Verifica se la stringa contiene solo numeri
    return /^[0-9]+$/.test(cleaned) && cleaned.length >= 6;
  }

  /**
   * Cerca un utente per numero di telefono
   * @param {string} phoneNumber - Numero di telefono da cercare
   * @returns {Promise<Object>} - Utente trovato
   */
  async findUserByPhone(phoneNumber) {
    try {
      // Normalizza il numero di telefono
      const normalizedPhone = this.normalizePhoneNumber(phoneNumber);

      // Chiama l'API per cercare l'utente
      const response = await apiService.users.getByPhone(normalizedPhone);

      if (response && response.data && response.data.success) {
        console.log('✅ TrendyChat: Utente trovato nel contactSearchService:', response.data.user);
        return response.data.user;
      }

      console.log('Utente non trovato');
      return null;
    } catch (error) {
      console.error('Errore nella ricerca dell\'utente:', error);
      return null;
    }
  }

  /**
   * Cerca utenti per query (nome o numero di telefono)
   * @param {string} query - Query di ricerca
   * @returns {Promise<Array>} - Lista degli utenti trovati
   */
  async searchUsers(query) {
    try {
      // Se la query è un numero di telefono, cerca per numero
      if (this.isPhoneNumber(query)) {
        const user = await this.findUserByPhone(query);
        return user ? [user] : [];
      }

      // Altrimenti, cerca per nome
      const response = await apiService.users.searchUsers(query);

      if (response && response.data && response.data.success) {
        return response.data.data || [];
      }

      console.log('Errore nella ricerca degli utenti');
      return [];
    } catch (error) {
      console.error('Errore nella ricerca degli utenti:', error);
      return [];
    }
  }
}

// Esporta un'istanza singleton del servizio
export default new ContactSearchService();
