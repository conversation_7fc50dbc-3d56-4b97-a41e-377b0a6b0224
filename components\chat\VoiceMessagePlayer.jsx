import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Animated,
  Easing,
  Platform,
} from 'react-native';
import { Audio } from 'expo-av';
import { Ionicons } from '@expo/vector-icons';
import Slider from '@react-native-community/slider';
import * as Haptics from 'expo-haptics';
import { useTheme } from '../../hooks/useTheme';

// GESTIONE GLOBALE AUDIO - Solo un audio alla volta
let globalSound = null;
let globalPlayingId = null;

const VoiceMessagePlayer = ({ audioUri, duration, isOwnMessage }) => {
  let theme;
  try {
    const themeContext = useTheme();
    theme = themeContext?.theme || {
      colors: {
        primary: '#007AFF',
        card: '#FFFFFF',
        text: '#000000'
      }
    };
  } catch (error) {
    console.log('⚠️ VoicePlayer: Errore tema, uso fallback');
    theme = {
      colors: {
        primary: '#007AFF',
        card: '#FFFFFF',
        text: '#000000'
      }
    };
  }
  const [sound, setSound] = useState(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [playbackPosition, setPlaybackPosition] = useState(0);
  const [playbackDuration, setPlaybackDuration] = useState(duration || 0);
  const [isLoaded, setIsLoaded] = useState(false);
  const [isSeeking, setIsSeeking] = useState(false);

  const animatedWaveOpacity = useRef(new Animated.Value(0.3)).current;
  const positionUpdateInterval = useRef(null);
  const waveAnimation = useRef(null);
  const playerId = useRef(Math.random().toString(36).substr(2, 9)).current;

  useEffect(() => {
    loadSound();

    return () => {
      if (sound) {
        sound.unloadAsync();
      }
      if (positionUpdateInterval.current) {
        clearInterval(positionUpdateInterval.current);
      }
      if (waveAnimation.current) {
        waveAnimation.current.stop();
      }
    };
  }, [audioUri]);

  useEffect(() => {
    if (isPlaying) {
      startWaveAnimation();
      startPositionTracking();
    } else {
      stopWaveAnimation();
      stopPositionTracking();
    }
  }, [isPlaying]);

  // Effetto per fermare questo player se un altro inizia
  useEffect(() => {
    const checkGlobalState = () => {
      if (globalPlayingId && globalPlayingId !== playerId && isPlaying) {
        setIsPlaying(false);
        setPlaybackPosition(0);
      }
    };

    const interval = setInterval(checkGlobalState, 100);
    return () => clearInterval(interval);
  }, [playerId, isPlaying]);

  const loadSound = async () => {
    if (!audioUri) {
      console.log('⚠️ VoicePlayer: audioUri mancante');
      return;
    }

    try {
      console.log('🔊 VoicePlayer: Caricando audio:', audioUri);
      console.log('⏱️ VoicePlayer: Durata passata come prop:', duration);

      // RESET STATO INIZIALE
      setPlaybackPosition(0);
      setIsPlaying(false);

      const { sound: newSound } = await Audio.Sound.createAsync(
        { uri: audioUri },
        { shouldPlay: false },
        onPlaybackStatusUpdate
      );

      setSound(newSound);
      setIsLoaded(true);

      // USA LA DURATA PASSATA COME PROP SE DISPONIBILE
      if (duration && duration > 0) {
        setPlaybackDuration(duration);
        console.log('✅ VoicePlayer: Usando durata da prop:', duration);
      } else {
        // Fallback: ottieni la durata dal file
        const status = await newSound.getStatusAsync();
        if (status.isLoaded && status.durationMillis) {
          const fileDuration = status.durationMillis / 1000;
          setPlaybackDuration(fileDuration);
          console.log('✅ VoicePlayer: Durata dal file:', fileDuration);
        }
      }

      console.log('✅ VoicePlayer: Audio caricato con successo');
    } catch (error) {
      console.error('❌ VoicePlayer: Errore caricamento audio:', error);
      setIsLoaded(false);
      setSound(null);
    }
  };

  const onPlaybackStatusUpdate = (status) => {
    if (!status.isLoaded) return;

    if (status.isPlaying !== isPlaying) {
      setIsPlaying(status.isPlaying);
    }

    // AGGIORNA POSIZIONE SOLO SE STA RIPRODUCENDO E NON STIAMO FACENDO SEEK
    if (!isSeeking && status.isPlaying && status.positionMillis !== undefined) {
      const newPosition = status.positionMillis / 1000;
      console.log('🎵 VoicePlayer: Aggiornando posizione:', newPosition, 'di', playbackDuration);
      setPlaybackPosition(newPosition);
    }

    if (status.didJustFinish) {
      setIsPlaying(false);
      setPlaybackPosition(0);
      if (sound && sound.setPositionAsync) {
        sound.setPositionAsync(0);
      }
      console.log('🏁 VoicePlayer: Audio finito, reset a posizione 0');
    }
  };

  const startPositionTracking = () => {
    if (positionUpdateInterval.current) {
      clearInterval(positionUpdateInterval.current);
    }

    positionUpdateInterval.current = setInterval(async () => {
      if (sound && isLoaded && !isSeeking && isPlaying) {
        const status = await sound.getStatusAsync();
        if (status.isLoaded && status.isPlaying) {
          setPlaybackPosition(status.positionMillis / 1000);
        }
      }
    }, 100);
  };

  const stopPositionTracking = () => {
    if (positionUpdateInterval.current) {
      clearInterval(positionUpdateInterval.current);
      positionUpdateInterval.current = null;
    }
  };

  const startWaveAnimation = () => {
    stopWaveAnimation();

    waveAnimation.current = Animated.loop(
      Animated.sequence([
        Animated.timing(animatedWaveOpacity, {
          toValue: 1,
          duration: 500,
          easing: Easing.inOut(Easing.ease),
          useNativeDriver: true,
        }),
        Animated.timing(animatedWaveOpacity, {
          toValue: 0.3,
          duration: 500,
          easing: Easing.inOut(Easing.ease),
          useNativeDriver: true,
        }),
      ])
    );

    waveAnimation.current.start();
  };

  const stopWaveAnimation = () => {
    if (waveAnimation.current) {
      waveAnimation.current.stop();
      animatedWaveOpacity.setValue(0.3);
    }
  };

  const togglePlayback = async () => {
    if (!sound || !isLoaded) {
      console.log('⚠️ VoicePlayer: Sound non disponibile per playback');
      return;
    }

    try {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);

      // FERMA QUALSIASI ALTRO AUDIO IN RIPRODUZIONE
      if (globalSound && globalPlayingId !== playerId) {
        try {
          await globalSound.pauseAsync();
        } catch (e) {}
        globalSound = null;
        globalPlayingId = null;
      }

      if (isPlaying) {
        await sound.pauseAsync();
        globalSound = null;
        globalPlayingId = null;
      } else {
        // RESET POSIZIONE PRIMA DI INIZIARE
        setPlaybackPosition(0);
        console.log('🔄 VoicePlayer: Reset posizione UI a 0');

        // SEMPRE RIAVVIA DALL'INIZIO QUANDO PREMI PLAY
        if (sound && sound.setPositionAsync) {
          await sound.setPositionAsync(0);
          console.log('🔄 VoicePlayer: Reset posizione audio a 0');
        }

        await Audio.setAudioModeAsync({
          playsInSilentModeIOS: true,
          staysActiveInBackground: true,
        });

        await sound.playAsync();
        globalSound = sound;
        globalPlayingId = playerId;
      }
    } catch (error) {
      console.error('❌ VoicePlayer: Errore riproduzione:', error);
      // Reset stato in caso di errore
      setIsPlaying(false);
      setPlaybackPosition(0);
      globalSound = null;
      globalPlayingId = null;
    }
  };

  const handleSliderValueChange = (value) => {
    setIsSeeking(true);
    setPlaybackPosition(value);
  };

  const handleSliderSlidingComplete = async (value) => {
    setIsSeeking(false);

    if (!sound || !isLoaded) {
      console.log('⚠️ VoicePlayer: Sound non disponibile per seek');
      return;
    }

    try {
      // VERIFICA CHE IL SOUND ESISTA E SIA VALIDO
      if (!sound.setPositionAsync || typeof sound.setPositionAsync !== 'function') {
        console.log('⚠️ VoicePlayer: setPositionAsync non disponibile');
        return;
      }

      // Verifica che il sound sia ancora valido prima di usarlo
      const status = await sound.getStatusAsync();
      if (status && status.isLoaded) {
        await sound.setPositionAsync(value * 1000);
        setPlaybackPosition(value);
        console.log('✅ VoicePlayer: Seek completato a', value, 'secondi');
      } else {
        console.log('⚠️ VoicePlayer: Sound non più caricato per seek');
        // Ricarica il sound se necessario
        await loadSound();
      }
    } catch (error) {
      console.error('❌ VoicePlayer: Errore seek:', error);
      // Reset in caso di errore
      setPlaybackPosition(0);
      setIsPlaying(false);
      // Prova a ricaricare il sound
      try {
        await loadSound();
      } catch (reloadError) {
        console.error('❌ VoicePlayer: Errore ricaricamento:', reloadError);
      }
    }
  };

  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs < 10 ? '0' : ''}${secs}`;
  };

  return (
    <View style={[
      styles.container,
      { backgroundColor: isOwnMessage ? theme.colors.primary : theme.colors.card }
    ]}>
      <TouchableOpacity
        style={[
          styles.playButton,
          { backgroundColor: isOwnMessage ? 'rgba(255, 255, 255, 0.2)' : theme.colors.primary }
        ]}
        onPress={togglePlayback}
        disabled={!isLoaded}
      >
        {!isLoaded ? (
          <Ionicons
            name="hourglass-outline"
            size={18}
            color={isOwnMessage ? '#FFFFFF' : '#FFFFFF'}
          />
        ) : (
          <Ionicons
            name={isPlaying ? 'pause' : 'play'}
            size={18}
            color={isOwnMessage ? '#FFFFFF' : '#FFFFFF'}
          />
        )}
      </TouchableOpacity>

      <View style={styles.waveContainer}>
        {[...Array(15)].map((_, index) => (
          <Animated.View
            key={index}
            style={[
              styles.wave,
              {
                height: 3 + Math.min(index, 14 - index) * 2,
                backgroundColor: isOwnMessage ? '#FFFFFF' : theme.colors.primary,
                opacity: isPlaying ? animatedWaveOpacity : 0.3,
              },
            ]}
          />
        ))}
      </View>

      <Slider
        style={[styles.slider, { transform: [{ scaleX: 1 }] }]}
        minimumValue={0}
        maximumValue={playbackDuration}
        value={Math.max(0, Math.min(playbackPosition, playbackDuration))}
        minimumTrackTintColor={isOwnMessage ? '#FFFFFF' : theme.colors.primary}
        maximumTrackTintColor={isOwnMessage ? 'rgba(255, 255, 255, 0.3)' : 'rgba(0, 0, 0, 0.1)'}
        thumbTintColor={isOwnMessage ? '#FFFFFF' : theme.colors.primary}
        onValueChange={handleSliderValueChange}
        onSlidingComplete={handleSliderSlidingComplete}
        disabled={!isLoaded || !sound}
        inverted={false}
      />

      <Text style={[
        styles.timeText,
        { color: isOwnMessage ? '#FFFFFF' : theme.colors.text }
      ]}>
        {formatTime(playbackPosition)} / {formatTime(playbackDuration)}
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 16,
    padding: 8,
    marginVertical: 2,
    maxWidth: '80%',
  },
  playButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  waveContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    width: 60,
    height: 30,
    marginRight: 8,
  },
  wave: {
    width: 2,
    borderRadius: 1,
    marginHorizontal: 1,
  },
  slider: {
    flex: 1,
    height: 30,
    marginHorizontal: 4,
  },
  timeText: {
    fontSize: 12,
    marginLeft: 4,
    minWidth: 30,
    textAlign: 'right',
  },
});

export default VoiceMessagePlayer;
