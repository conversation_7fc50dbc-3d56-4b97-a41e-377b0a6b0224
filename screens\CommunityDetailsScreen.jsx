import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  TouchableOpacity,
  FlatList,
  Alert,
  SafeAreaView,
  StatusBar,
  ActivityIndicator,
  Modal,
  Platform,
} from 'react-native';
import { useRoute, useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import useCommunityStore from '../store/communityStore';
import useAuthStore from '../store/authStore';
import useContactStore from '../store/contactStore';
import { theme } from '../theme';

const CommunityDetailsScreen = () => {
  const route = useRoute();
  const navigation = useNavigation();
  const { communityId } = route.params || {};

  const { user } = useAuthStore();
  const { loadCommunity, currentCommunity, loading, error, updateCommunity, deleteCommunity, removeGroupFromCommunity } = useCommunityStore();
  const { contacts, loadContacts } = useContactStore();

  const [activeTab, setActiveTab] = useState('groups'); // 'groups' o 'members'
  const [showOptions, setShowOptions] = useState(false);

  useEffect(() => {
    if (communityId) {
      loadCommunity(communityId);
      loadContacts();
    }
  }, [communityId]);

  const isAdmin = currentCommunity?.admins?.includes(user?.uid);
  const isCreator = currentCommunity?.createdBy === user?.uid;

  const getContactInfo = (userId) => {
    if (userId === user?.uid) {
      return { name: 'Tu', photoURL: user?.photoURL };
    }

    const contact = contacts.find(c => c.id === userId);
    return contact || { name: 'Utente sconosciuto', photoURL: null };
  };

  const handleEditCommunity = () => {
    setShowOptions(false);
    navigation.navigate('EditCommunity', { communityId });
  };

  const handleDeleteCommunity = () => {
    setShowOptions(false);

    Alert.alert(
      'Elimina community',
      'Sei sicuro di voler eliminare questa community? Questa azione non può essere annullata.',
      [
        { text: 'Annulla', style: 'cancel' },
        {
          text: 'Elimina',
          style: 'destructive',
          onPress: async () => {
            try {
              await deleteCommunity(communityId);
              navigation.goBack();
            } catch (error) {
              console.error('Errore nell\'eliminazione della community:', error);
              Alert.alert('Errore', 'Impossibile eliminare la community');
            }
          }
        },
      ]
    );
  };

  const handleAddGroup = () => {
    navigation.navigate('AddGroupToCommunity', { communityId });
  };

  const handleRemoveGroup = (groupId) => {
    Alert.alert(
      'Rimuovi gruppo',
      'Sei sicuro di voler rimuovere questo gruppo dalla community?',
      [
        { text: 'Annulla', style: 'cancel' },
        {
          text: 'Rimuovi',
          style: 'destructive',
          onPress: async () => {
            try {
              await removeGroupFromCommunity(communityId, groupId);
            } catch (error) {
              console.error('Errore nella rimozione del gruppo:', error);
              Alert.alert('Errore', 'Impossibile rimuovere il gruppo');
            }
          }
        },
      ]
    );
  };

  const handleAddMembers = () => {
    navigation.navigate('AddMembersToCommunity', { communityId });
  };

  const handleGroupPress = (groupId) => {
    navigation.navigate('GroupChat', { groupId });
  };

  const renderGroupItem = ({ item }) => (
    <TouchableOpacity
      style={styles.groupItem}
      onPress={() => handleGroupPress(item.id)}
      onLongPress={() => isAdmin && handleRemoveGroup(item.id)}
    >
      <Image
        source={{ uri: item.photoURL || 'https://via.placeholder.com/150' }}
        style={styles.groupImage}
      />

      <View style={styles.groupInfo}>
        <Text style={styles.groupName}>{item.name}</Text>
      </View>

      {isAdmin && (
        <TouchableOpacity
          style={styles.removeButton}
          onPress={() => handleRemoveGroup(item.id)}
        >
          <Ionicons name="close-circle" size={24} color="#F44336" />
        </TouchableOpacity>
      )}
    </TouchableOpacity>
  );

  const renderMemberItem = ({ item }) => {
    const contact = getContactInfo(item);
    const isItemAdmin = currentCommunity?.admins?.includes(item);

    return (
      <View style={styles.memberItem}>
        <Image
          source={{ uri: contact.photoURL || 'https://via.placeholder.com/150' }}
          style={styles.memberImage}
        />

        <View style={styles.memberInfo}>
          <Text style={styles.memberName}>{contact.name}</Text>
          {isItemAdmin && (
            <Text style={styles.adminBadge}>Admin</Text>
          )}
        </View>
      </View>
    );
  };

  if (loading && !currentCommunity) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle="light-content" backgroundColor="transparent" translucent={true} />
        <LinearGradient
          colors={['#1E88E5', '#D81B60']}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 0 }}
          style={styles.header}
        >
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <Ionicons name="arrow-back" size={24} color="#FFFFFF" />
          </TouchableOpacity>

          <Text style={styles.headerTitle}>Dettagli community</Text>

          <View style={styles.placeholder} />
        </LinearGradient>

        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#1E88E5" />
        </View>
      </SafeAreaView>
    );
  }

  if (error || !currentCommunity) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle="light-content" backgroundColor="transparent" translucent={true} />
        <LinearGradient
          colors={['#1E88E5', '#D81B60']}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 0 }}
          style={styles.header}
        >
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <Ionicons name="arrow-back" size={24} color="#FFFFFF" />
          </TouchableOpacity>

          <Text style={styles.headerTitle}>Dettagli community</Text>

          <View style={styles.placeholder} />
        </LinearGradient>

        <View style={styles.errorContainer}>
          <Ionicons name="alert-circle-outline" size={64} color="#F44336" />
          <Text style={styles.errorText}>
            {error || 'Community non trovata'}
          </Text>
          <TouchableOpacity
            style={styles.retryButton}
            onPress={() => loadCommunity(communityId)}
          >
            <Text style={styles.retryButtonText}>Riprova</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent={true} />

      <LinearGradient
        colors={['#1E88E5', '#D81B60']}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 0 }}
        style={styles.header}
      >
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color="#FFFFFF" />
        </TouchableOpacity>

        <Text style={styles.headerTitle}>Dettagli community</Text>

        {isAdmin && (
          <TouchableOpacity
            style={styles.optionsButton}
            onPress={() => setShowOptions(true)}
          >
            <Ionicons name="ellipsis-vertical" size={24} color="#FFFFFF" />
          </TouchableOpacity>
        )}
      </LinearGradient>

      <View style={styles.communityHeader}>
        <Image
          source={{ uri: currentCommunity.photoURL || 'https://via.placeholder.com/150' }}
          style={styles.communityImage}
        />

        <Text style={styles.communityName}>{currentCommunity.name}</Text>

        {currentCommunity.description && (
          <Text style={styles.communityDescription}>
            {currentCommunity.description}
          </Text>
        )}

        <View style={styles.communityStats}>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>
              {currentCommunity.groups?.length || 0}
            </Text>
            <Text style={styles.statLabel}>Gruppi</Text>
          </View>

          <View style={styles.statDivider} />

          <View style={styles.statItem}>
            <Text style={styles.statValue}>
              {currentCommunity.members?.length || 0}
            </Text>
            <Text style={styles.statLabel}>Membri</Text>
          </View>
        </View>
      </View>

      <View style={styles.tabContainer}>
        <TouchableOpacity
          style={[styles.tabButton, activeTab === 'groups' && styles.activeTabButton]}
          onPress={() => setActiveTab('groups')}
        >
          <Text style={[styles.tabText, activeTab === 'groups' && styles.activeTabText]}>
            Gruppi
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.tabButton, activeTab === 'members' && styles.activeTabButton]}
          onPress={() => setActiveTab('members')}
        >
          <Text style={[styles.tabText, activeTab === 'members' && styles.activeTabText]}>
            Membri
          </Text>
        </TouchableOpacity>
      </View>

      {activeTab === 'groups' ? (
        <>
          <FlatList
            data={currentCommunity.groups || []}
            renderItem={renderGroupItem}
            keyExtractor={(item) => item.id}
            contentContainerStyle={styles.listContent}
            ListEmptyComponent={
              <View style={styles.emptyContainer}>
                <Ionicons name="people-outline" size={64} color="#CCCCCC" />
                <Text style={styles.emptyText}>Nessun gruppo in questa community</Text>
                {isAdmin && (
                  <Text style={styles.emptySubtext}>
                    Aggiungi gruppi per iniziare
                  </Text>
                )}
              </View>
            }
          />

          {isAdmin && (
            <TouchableOpacity
              style={styles.floatingButton}
              onPress={handleAddGroup}
            >
              <Ionicons name="add" size={24} color="#FFFFFF" />
            </TouchableOpacity>
          )}
        </>
      ) : (
        <>
          <FlatList
            data={currentCommunity.members || []}
            renderItem={renderMemberItem}
            keyExtractor={(item) => item}
            contentContainerStyle={styles.listContent}
            ListEmptyComponent={
              <View style={styles.emptyContainer}>
                <Ionicons name="people-outline" size={64} color="#CCCCCC" />
                <Text style={styles.emptyText}>Nessun membro in questa community</Text>
              </View>
            }
          />

          {isAdmin && (
            <TouchableOpacity
              style={styles.floatingButton}
              onPress={handleAddMembers}
            >
              <Ionicons name="person-add" size={24} color="#FFFFFF" />
            </TouchableOpacity>
          )}
        </>
      )}

      <Modal
        visible={showOptions}
        transparent
        animationType="fade"
        onRequestClose={() => setShowOptions(false)}
      >
        <TouchableOpacity
          style={styles.modalOverlay}
          activeOpacity={1}
          onPress={() => setShowOptions(false)}
        >
          <View style={styles.optionsContainer}>
            <TouchableOpacity
              style={styles.optionItem}
              onPress={handleEditCommunity}
            >
              <Ionicons name="create-outline" size={24} color="#333333" />
              <Text style={styles.optionText}>Modifica community</Text>
            </TouchableOpacity>

            {isCreator && (
              <TouchableOpacity
                style={styles.optionItem}
                onPress={handleDeleteCommunity}
              >
                <Ionicons name="trash-outline" size={24} color="#F44336" />
                <Text style={[styles.optionText, styles.deleteText]}>
                  Elimina community
                </Text>
              </TouchableOpacity>
            )}
          </View>
        </TouchableOpacity>
      </Modal>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#1E1E1E',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#1E1E1E',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#1E1E1E',
  },
  errorText: {
    fontSize: 16,
    color: '#FFFFFF',
    textAlign: 'center',
    marginTop: 16,
  },
  retryButton: {
    marginTop: 16,
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 20,
  },
  retryButtonText: {
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    paddingTop: Platform.OS === 'ios' ? 50 : 40,
    paddingBottom: 16,
    height: Platform.OS === 'ios' ? 110 : 100,
    borderBottomWidth: 0,
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  optionsButton: {
    padding: 8,
  },
  placeholder: {
    width: 40,
  },
  communityHeader: {
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#2A2A2A',
    backgroundColor: '#1E1E1E',
  },
  communityImage: {
    width: 80,
    height: 80,
    borderRadius: 40,
    marginBottom: 12,
  },
  communityName: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 8,
  },
  communityDescription: {
    fontSize: 14,
    color: '#AAAAAA',
    textAlign: 'center',
    marginBottom: 16,
  },
  communityStats: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
  },
  statItem: {
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  statValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  statLabel: {
    fontSize: 12,
    color: '#AAAAAA',
    marginTop: 4,
  },
  statDivider: {
    width: 1,
    height: 24,
    backgroundColor: '#2A2A2A',
  },
  tabContainer: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: '#2A2A2A',
    backgroundColor: '#1E1E1E',
  },
  tabButton: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
  },
  activeTabButton: {
    borderBottomWidth: 2,
    borderBottomColor: '#D81B60',
  },
  tabText: {
    fontSize: 16,
    color: '#AAAAAA',
  },
  activeTabText: {
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  listContent: {
    padding: 16,
    flexGrow: 1,
    backgroundColor: '#1E1E1E',
  },
  groupItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#2A2A2A',
  },
  groupImage: {
    width: 50,
    height: 50,
    borderRadius: 25,
    marginRight: 16,
  },
  groupInfo: {
    flex: 1,
  },
  groupName: {
    fontSize: 16,
    fontWeight: '500',
    color: '#FFFFFF',
  },
  removeButton: {
    padding: 8,
  },
  memberItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#2A2A2A',
  },
  memberImage: {
    width: 50,
    height: 50,
    borderRadius: 25,
    marginRight: 16,
  },
  memberInfo: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  memberName: {
    fontSize: 16,
    fontWeight: '500',
    color: '#FFFFFF',
  },
  adminBadge: {
    fontSize: 12,
    color: '#FFFFFF',
    backgroundColor: '#D81B60',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 10,
    marginLeft: 8,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
    backgroundColor: '#1E1E1E',
  },
  emptyText: {
    fontSize: 16,
    color: '#AAAAAA',
    textAlign: 'center',
    marginTop: 16,
  },
  emptySubtext: {
    fontSize: 14,
    color: '#888888',
    textAlign: 'center',
    marginTop: 8,
  },
  floatingButton: {
    position: 'absolute',
    bottom: 20,
    right: 20,
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: '#D81B60',
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 4,
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    justifyContent: 'flex-end',
  },
  optionsContainer: {
    backgroundColor: '#252525',
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    padding: 16,
  },
  optionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
  },
  optionText: {
    fontSize: 16,
    color: '#FFFFFF',
    marginLeft: 16,
  },
  deleteText: {
    color: '#FF5252',
  },
});

export default CommunityDetailsScreen;
