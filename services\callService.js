// 📞 Call Service - Wrapper per CallRouter
// Fornisce funzioni compatibili con i componenti esistenti

import callRouter from './callRouter';

// 📞 Ottieni chiamate utente (mock per compatibilità)
export const getUserCalls = (callback) => {
  // Mock function per compatibilità con Firebase
  // In futuro dovrebbe chiamare il server HP per ottenere la cronologia chiamate
  console.log('📞 getUserCalls chiamato - implementazione mock');
  
  // Restituisce un array vuoto per ora
  const mockCalls = [];
  callback(mockCalls);
  
  // Restituisce una funzione di cleanup
  return () => {
    console.log('📞 getUserCalls cleanup');
  };
};

// 📞 Rispondi a una chiamata
export const answerCall = async (callId) => {
  try {
    console.log('📞 Rispondendo alla chiamata:', callId);
    
    // Usa callRouter per rispondere alla chiamata
    const result = await callRouter.joinCall(callId, 'p2p');
    
    console.log('✅ Chiamata risposta con successo');
    return result;
  } catch (error) {
    console.error('❌ Errore risposta chiamata:', error);
    throw error;
  }
};

// 📞 Rifiuta una chiamata
export const rejectCall = async (callId) => {
  try {
    console.log('📞 Rifiutando la chiamata:', callId);
    
    // Per ora termina semplicemente la chiamata
    await callRouter.endCall();
    
    console.log('✅ Chiamata rifiutata con successo');
    return true;
  } catch (error) {
    console.error('❌ Errore rifiuto chiamata:', error);
    throw error;
  }
};

// 📞 Avvia una nuova chiamata
export const startCall = async (targetUserId, options = {}) => {
  try {
    console.log('📞 Avviando chiamata verso:', targetUserId);
    
    const result = await callRouter.startCall([targetUserId], options);
    
    console.log('✅ Chiamata avviata con successo');
    return result;
  } catch (error) {
    console.error('❌ Errore avvio chiamata:', error);
    throw error;
  }
};

// 📞 Termina la chiamata attiva
export const endCall = async () => {
  try {
    console.log('📞 Terminando chiamata attiva');
    
    await callRouter.endCall();
    
    console.log('✅ Chiamata terminata con successo');
    return true;
  } catch (error) {
    console.error('❌ Errore termine chiamata:', error);
    throw error;
  }
};

// 📊 Ottieni statistiche chiamata
export const getCallStats = () => {
  return callRouter.getCallStats();
};

// 📡 Ottieni informazioni server
export const getServerInfo = () => {
  return callRouter.getServerInfo();
};

export default {
  getUserCalls,
  answerCall,
  rejectCall,
  startCall,
  endCall,
  getCallStats,
  getServerInfo
};
