import AsyncStorage from '../utils/asyncStorageConfig';
import { create } from 'zustand';
import useAuthStore from './authStore';
import contactsSync from '../utils/contactsSync';
import {
  query,
  where,
  onSnapshot,
  doc,
  updateDoc,
  addDoc,
  arrayUnion,
  arrayRemove,
  serverTimestamp
} from '../utils/asyncStorageConfig';

const useContactStore = create((set, get) => ({
  contacts: [],
  registeredContacts: [],
  unregisteredContacts: [],
  loading: false,
  error: null,
  typingUsers: {},
  lastSync: null,

  // Carica i contatti
  loadContacts: async () => {
    const { user } = useAuthStore.getState();
    if (!user) return;

    try {
      set({ loading: true, error: null });

      // Utilizziamo AsyncStorage esteso con funzionalità di tipo Firestore
      const usersRef = AsyncStorage.collection("users");
      const q = query(usersRef, where('uid', '!=', user.uid));

      const unsubscribe = onSnapshot(q, (snapshot) => {
        const contacts = snapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data(),
        }));
        set({ contacts });
      });

      return unsubscribe;
    } catch (error) {
      console.error('Errore nel caricamento dei contatti:', error);
      set({ error: 'Impossibile caricare i contatti' });
    } finally {
      set({ loading: false });
    }
  },

  // Blocca un contatto
  blockContact: async (contactId) => {
    const { user } = useAuthStore.getState();
    if (!user) return;

    try {
      set({ loading: true, error: null });

      const userRef = doc;
      await updateDoc(userRef, {
        blockedUsers: [...get().contacts.find(c => c.id === user.uid).blockedUsers || [], contactId],
      });
    } catch (error) {
      console.error('Errore nel blocco del contatto:', error);
      set({ error: 'Impossibile bloccare il contatto' });
      throw error;
    } finally {
      set({ loading: false });
    }
  },

  // Sblocca un contatto
  unblockContact: async (contactId) => {
    const { user } = useAuthStore.getState();
    if (!user) return;

    try {
      set({ loading: true, error: null });

      const userRef = doc;
      const blockedUsers = get().contacts.find(c => c.id === user.uid).blockedUsers || [];
      await updateDoc(userRef, {
        blockedUsers: blockedUsers.filter(id => id !== contactId),
      });
    } catch (error) {
      console.error('Errore nello sblocco del contatto:', error);
      set({ error: 'Impossibile sbloccare il contatto' });
      throw error;
    } finally {
      set({ loading: false });
    }
  },

  // Segnala un contatto
  reportContact: async (contactId, reason) => {
    const { user } = useAuthStore.getState();
    if (!user) return;

    try {
      set({ loading: true, error: null });

      const reportsRef = mockStorage.collection("reports");
      await addDoc(reportsRef, {
        reporterId: user.uid,
        reportedId: contactId,
        reason,
        timestamp: serverTimestamp(),
        status: 'pending',
      });
    } catch (error) {
      console.error('Errore nella segnalazione del contatto:', error);
      set({ error: 'Impossibile segnalare il contatto' });
      throw error;
    } finally {
      set({ loading: false });
    }
  },

  // Imposta lo stato "sta scrivendo"
  setTypingStatus: async (chatId, isTyping) => {
    const { user } = useAuthStore.getState();
    if (!user) return;

    try {
      const chatRef = doc;
      await updateDoc(chatRef, {
        [`typing.${user.uid}`]: isTyping,
      });
    } catch (error) {
      console.error('Errore nell\'impostazione dello stato di digitazione:', error);
    }
  },

  // Ascolta gli stati di digitazione
  listenToTypingStatus: (chatId) => {
    try {
      const chatRef = doc;
      const unsubscribe = onSnapshot(chatRef, (doc) => {
        if (doc.exists()) {
          set(state => ({
            typingUsers: {
              ...state.typingUsers,
              [chatId]: doc.data().typing || {},
            }
          }));
        }
      });

      return unsubscribe;
    } catch (error) {
      console.error('Errore nell\'ascolto dello stato di digitazione:', error);
    }
  },

  // Sincronizza i contatti dalla rubrica
  syncContacts: async () => {
    try {
      set({ loading: true, error: null });

      const syncedContacts = await contactsSync.syncContacts();

      // Dividi i contatti in registrati e non registrati
      const registered = syncedContacts.filter(contact => contact.isRegistered);
      const unregistered = syncedContacts.filter(contact => !contact.isRegistered);

      set({
        registeredContacts: registered,
        unregisteredContacts: unregistered,
        lastSync: new Date(),
      });

      return syncedContacts;
    } catch (error) {
      console.error('Errore nella sincronizzazione dei contatti:', error);
      set({ error: 'Impossibile sincronizzare i contatti' });
      throw error;
    } finally {
      set({ loading: false });
    }
  },

  // Invia un invito a un contatto non registrato
  inviteContact: async (contact) => {
    try {
      set({ loading: true, error: null });

      const success = await contactsSync.inviteContact(contact);

      if (success) {
        // Aggiorna lo stato del contatto
        set(state => ({
          unregisteredContacts: state.unregisteredContacts.map(c =>
            c.phoneNumber === contact.phoneNumber
              ? { ...c, invited: true, invitedAt: new Date() }
              : c
          )
        }));
      }

      return success;
    } catch (error) {
      console.error('Errore nell\'invio dell\'invito:', error);
      set({ error: 'Impossibile inviare l\'invito' });
      throw error;
    } finally {
      set({ loading: false });
    }
  },

  // Imposta errore
  setError: (error) => set({ error }),

  // Blocca un contatto
  blockContact: async (contactId) => {
    try {
      set({ loading: true, error: null });

      const { user } = useAuthStore.getState();
      if (!user) throw new Error('Utente non autenticato');

      // Aggiorna il documento dell'utente con il contatto bloccato
      const userRef = doc;
      await updateDoc(userRef, {
        blockedContacts: arrayUnion(contactId),
      });

      // Aggiorna lo stato locale
      set(state => ({
        contacts: state.contacts.map(contact =>
          contact.id === contactId
            ? { ...contact, isBlocked: true, blockedAt: new Date() }
            : contact
        ),
      }));

      return true;
    } catch (error) {
      console.error('Errore nel blocco del contatto:', error);
      set({ error: 'Impossibile bloccare il contatto' });
      throw error;
    } finally {
      set({ loading: false });
    }
  },

  // Sblocca un contatto
  unblockContact: async (contactId) => {
    try {
      set({ loading: true, error: null });

      const { user } = useAuthStore.getState();
      if (!user) throw new Error('Utente non autenticato');

      // Aggiorna il documento dell'utente rimuovendo il contatto bloccato
      const userRef = doc;
      await updateDoc(userRef, {
        blockedContacts: arrayRemove(contactId),
      });

      // Aggiorna lo stato locale
      set(state => ({
        contacts: state.contacts.map(contact =>
          contact.id === contactId
            ? { ...contact, isBlocked: false, blockedAt: null }
            : contact
        ),
      }));

      return true;
    } catch (error) {
      console.error('Errore nello sblocco del contatto:', error);
      set({ error: 'Impossibile sbloccare il contatto' });
      throw error;
    } finally {
      set({ loading: false });
    }
  },

  // Segnala un contatto
  reportContact: async (contactId, reason) => {
    try {
      set({ loading: true, error: null });

      const { user } = useAuthStore.getState();
      if (!user) throw new Error('Utente non autenticato');

      // Crea un documento di segnalazione
      const reportsRef = AsyncStorage.collection("reports");
      await addDoc(reportsRef, {
        reportedBy: user.uid,
        reportedUser: contactId,
        reason,
        timestamp: serverTimestamp(),
        status: 'pending', // pending, reviewed, resolved, dismissed
      });

      return true;
    } catch (error) {
      console.error('Errore nella segnalazione del contatto:', error);
      set({ error: 'Impossibile segnalare il contatto' });
      throw error;
    } finally {
      set({ loading: false });
    }
  },

  // Pulisci errore
  clearError: () => set({ error: null }),
}));

export default useContactStore;

// Esporto le variabili vuote per mantenere la compatibilità
export const db = null;