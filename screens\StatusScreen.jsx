import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Image,
  ActivityIndicator,
  Alert,
  SafeAreaView,
  StatusBar,
  Dimensions,
  Platform,
  Modal,
  TouchableWithoutFeedback,
  TextInput,
} from 'react-native';
import TextStatusCreator from '../components/status/TextStatusCreator';
import TransparentStatusModal from '../components/status/TransparentStatusModal';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { Video } from 'expo-av';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as ImagePicker from 'expo-image-picker';
import useStatusStore from '../store/statusStore';
import useMediaStore from '../store/mediaStore';
import useAuthStore from '../store/authStore';
import StatusMenuModal from '../components/StatusMenuModal';
import StatusSearchModal from '../components/StatusSearchModal';
import StatusPrivacyModal from '../components/StatusPrivacyModal';
import StatusSettingsModal from '../components/StatusSettingsModal';
import StatusNotificationsModal from '../components/StatusNotificationsModal';

const { width, height } = Dimensions.get('window');

const StatusScreen = () => {
  const navigation = useNavigation();
  const { user } = useAuthStore();
  const { statuses, myStatus, loading, error, loadStatuses, createStatus, deleteStatus } = useStatusStore();
  const { pickImage, pickVideo, loading: mediaLoading } = useMediaStore();
  const [isLoading, setIsLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [showTextCreator, setShowTextCreator] = useState(false);
  const [showStatusModal, setShowStatusModal] = useState(false);
  const [showSearchModal, setShowSearchModal] = useState(false);
  const [showOptionsMenu, setShowOptionsMenu] = useState(false);
  const [showPrivacyModal, setShowPrivacyModal] = useState(false);
  const [showSettingsModal, setShowSettingsModal] = useState(false);
  const [showNotificationsModal, setShowNotificationsModal] = useState(false);

  useEffect(() => {
    loadStatuses();
  }, []);

  useEffect(() => {
    console.log('myStatus aggiornato:', myStatus);
    if (myStatus) {
      console.log('myStatus.mediaUrl:', myStatus.mediaUrl);
      console.log('myStatus.type:', myStatus.type);
    }
  }, [myStatus]);

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadStatuses();
    setRefreshing(false);
  };

  // 🗑️ Elimina la mia storia dal server
  const handleDeleteMyStory = async () => {
    if (!myStatus) return;

    Alert.alert(
      'Elimina storia',
      'Sei sicuro di voler eliminare la tua storia? Questa azione non può essere annullata.',
      [
        { text: 'Annulla', style: 'cancel' },
        {
          text: 'Elimina',
          style: 'destructive',
          onPress: async () => {
            try {
              console.log('🗑️ STORIE: Eliminando storia:', myStatus.id);

              const serverUrl = 'http://192.168.1.66:3001';
              const endpoint = `${serverUrl}/api/stories/${myStatus.id}`;

              const response = await fetch(endpoint, {
                method: 'DELETE',
                headers: {
                  'Content-Type': 'application/json',
                  'Authorization': `Bearer ${await AsyncStorage.getItem('token')}`
                }
              });

              if (response.ok) {
                console.log('✅ STORIE: Storia eliminata dal server');
                Alert.alert('Successo', 'Storia eliminata con successo');
                // Ricarica le storie
                await loadStatuses();
              } else {
                console.error('❌ STORIE: Errore eliminazione dal server:', response.status);
                Alert.alert('Errore', 'Impossibile eliminare la storia');
              }
            } catch (error) {
              console.error('❌ STORIE: Errore eliminazione storia:', error);
              Alert.alert('Errore', 'Impossibile eliminare la storia');
            }
          }
        },
      ]
    );
  };

  const handleCreateStatus = async (type) => {
    try {
      setIsLoading(true);
      let mediaUri;
      let result;

      if (type === 'image') {
        const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
        if (status !== 'granted') {
          Alert.alert('Permesso negato', 'Permesso di accesso alla galleria negato');
          return;
        }

        result = await ImagePicker.launchImageLibraryAsync({
          mediaTypes: 'images',
          allowsEditing: true,
          quality: 0.8,
        });

        if (!result.canceled && result.assets && result.assets.length > 0) {
          mediaUri = result.assets[0].uri;
        }
      } else if (type === 'video') {
        const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
        if (status !== 'granted') {
          Alert.alert('Permesso negato', 'Permesso di accesso alla galleria negato');
          return;
        }

        result = await ImagePicker.launchImageLibraryAsync({
          mediaTypes: 'videos',
          allowsEditing: true,
          quality: 0.8,
          videoMaxDuration: 30,
        });

        if (!result.canceled && result.assets && result.assets.length > 0) {
          mediaUri = result.assets[0].uri;
        }
      } else if (type === 'camera') {
        const { status } = await ImagePicker.requestCameraPermissionsAsync();
        if (status !== 'granted') {
          Alert.alert('Permesso negato', 'Permesso di accesso alla fotocamera negato');
          return;
        }

        result = await ImagePicker.launchCameraAsync({
          mediaTypes: 'images',
          allowsEditing: true,
          quality: 0.8,
        });

        if (!result.canceled && result.assets && result.assets.length > 0) {
          mediaUri = result.assets[0].uri;
        }
      } else if (type === 'text') {
        setShowTextCreator(true);
        return;
      }

      if (mediaUri) {
        await createStatus(mediaUri, type === 'video' ? 'video' : 'image');
        Alert.alert('Successo', 'Stato pubblicato con successo!');
      }
    } catch (error) {
      console.error('Errore nella creazione dello stato:', error);
      Alert.alert('Errore', 'Impossibile creare lo stato');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteStatus = async (statusId) => {
    try {
      Alert.alert(
        'Elimina stato',
        'Sei sicuro di voler eliminare questo stato?',
        [
          { text: 'Annulla', style: 'cancel' },
          {
            text: 'Elimina',
            style: 'destructive',
            onPress: async () => {
              setIsLoading(true);
              await deleteStatus(statusId);
              Alert.alert('Successo', 'Stato eliminato con successo!');
              setIsLoading(false);
            }
          },
        ]
      );
    } catch (error) {
      console.error('Errore nell\'eliminazione dello stato:', error);
      Alert.alert('Errore', 'Impossibile eliminare lo stato');
    }
  };

  const handleSaveTextStatus = async (textData) => {
    try {
      setIsLoading(true);
      await createStatus(null, 'text', textData);
      setShowTextCreator(false);
      Alert.alert('Successo', 'Stato testuale pubblicato con successo!');
    } catch (error) {
      console.error('Errore nella creazione dello stato testuale:', error);
      Alert.alert('Errore', 'Impossibile creare lo stato testuale');
    } finally {
      setIsLoading(false);
    }
  };

  const renderStatusItem = ({ item }) => {
    const isMyStatus = item.userId === user.uid;
    const statusDate = item.createdAt?.toDate ? new Date(item.createdAt.toDate()) : new Date();
    const timeString = statusDate.toLocaleTimeString('it-IT', { hour: '2-digit', minute: '2-digit' });
    const dateString = statusDate.toLocaleDateString('it-IT', { day: 'numeric', month: 'short' });

    return (
      <TouchableOpacity
        style={styles.statusItem}
        onPress={() => navigation.navigate('StatusView', { statusId: item.id })}
        onLongPress={() => isMyStatus && handleDeleteStatus(item.id)}
      >
        <View style={styles.statusImageContainer}>
          <LinearGradient
            colors={['#1E88E5', '#D81B60']}
            style={styles.statusImageBorder}
          >
            <Image
              source={{ uri: item.mediaUrl || (isMyStatus ? user.photoURL : 'https://via.placeholder.com/50') }}
              style={styles.statusImage}
            />
          </LinearGradient>
        </View>

        <View style={styles.statusInfo}>
          <Text style={styles.statusName}>
            {isMyStatus ? 'Il mio stato' : (item.userName || 'Utente')}
          </Text>
          <View style={styles.statusTimeContainer}>
            <Text style={styles.statusTime}>
              {timeString} · {dateString}
            </Text>
            {item.viewers && (
              <Text style={styles.viewCount}>
                {item.viewers.length} {item.viewers.length === 1 ? 'visualizzazione' : 'visualizzazioni'}
              </Text>
            )}
          </View>
        </View>

        {isMyStatus && (
          <TouchableOpacity
            style={styles.deleteButton}
            onPress={() => handleDeleteStatus(item.id)}
          >
            <Ionicons name="trash-outline" size={20} color="#D81B60" />
          </TouchableOpacity>
        )}
      </TouchableOpacity>
    );
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent={true} hidden={false} />

      <LinearGradient
        colors={['#1E88E5', '#D81B60']}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 0 }}
        style={[styles.header, { paddingTop: Platform.OS === 'ios' ? 50 : 40 }]}
      >
        <Text style={styles.headerTitle}>Aggiornamenti</Text>
        <View style={styles.headerButtons}>
          <TouchableOpacity
            style={styles.headerButton}
            onPress={() => setShowSearchModal(true)}
          >
            <Ionicons name="search" size={24} color="#FFFFFF" />
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.headerButton}
            onPress={() => setShowOptionsMenu(true)}
          >
            <Ionicons name="menu" size={24} color="#FFFFFF" />
          </TouchableOpacity>
        </View>
      </LinearGradient>

      <View style={styles.content}>
        <View style={styles.myStatusContainer}>
          <TouchableOpacity
            style={styles.myStatus}
            onPress={() => {
              if (myStatus) {
                // Se ho una storia, aprila per visualizzarla
                // Converti le date in stringhe per evitare problemi di serializzazione
                const serializedStatus = {
                  ...myStatus,
                  createdAt: myStatus.createdAt.toISOString(),
                  expiresAt: myStatus.expiresAt.toISOString()
                };
                navigation.navigate('StatusView', {
                  statuses: [serializedStatus],
                  initialIndex: 0
                });
              } else {
                // Se non ho una storia, apri il modale per crearne una
                setShowStatusModal(true);
              }
            }}
          >
            <View style={styles.myStatusImageContainer}>
              {myStatus ? (
                <LinearGradient
                  colors={['#1E88E5', '#D81B60']}
                  style={styles.myStatusImageBorder}
                >
                  {myStatus?.type === 'video' ? (
                    // Per i video, mostra il primo frame come anteprima
                    <Video
                      source={{ uri: myStatus.mediaUrl }}
                      style={styles.myStatusImage}
                      shouldPlay={false}
                      isLooping={false}
                      resizeMode="cover"
                      useNativeControls={false}
                    />
                  ) : (
                    // Per le immagini, mostra l'immagine normale
                    <Image
                      source={{ uri: myStatus?.mediaUrl || user?.photoURL || 'https://via.placeholder.com/50' }}
                      style={styles.myStatusImage}
                    />
                  )}
                </LinearGradient>
              ) : (
                <LinearGradient
                  colors={['#1E88E5', '#D81B60']}
                  start={{ x: 0, y: 0 }}
                  end={{ x: 1, y: 0 }}
                  style={styles.addStatusButton}
                >
                  <Ionicons name="add" size={24} color="#FFFFFF" />
                </LinearGradient>
              )}
            </View>

            <View style={styles.myStatusInfo}>
              <Text style={styles.myStatusText}>Il mio stato</Text>
              <Text style={styles.addStatusText}>
                {myStatus ? 'Tocca per aggiornare' : 'Tocca per aggiungere'}
              </Text>
            </View>
          </TouchableOpacity>
        </View>

        {loading || isLoading || mediaLoading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#1E88E5" />
          </View>
        ) : (
          <FlatList
            data={statuses}
            renderItem={renderStatusItem}
            keyExtractor={(item) => item.id}
            refreshing={refreshing}
            onRefresh={handleRefresh}
            ListHeaderComponent={
              statuses.length > 0 && (
                <View style={styles.sectionHeader}>
                  <Text style={styles.sectionTitle}>Aggiornamenti recenti</Text>
                </View>
              )
            }
            ListEmptyComponent={
              <View style={styles.emptyContainer}>
                <Ionicons name="images-outline" size={64} color="#AAAAAA" />
                <Text style={styles.emptyText}>Nessuno stato disponibile</Text>
                <Text style={styles.emptySubtext}>
                  Gli stati dei tuoi contatti appariranno qui
                </Text>
              </View>
            }
            contentContainerStyle={styles.listContent}
          />
        )}

        {/* Pulsante FAB rimosso per evitare ridondanza con l'icona "+" nel "Il mio stato" */}
      </View>

      <TextStatusCreator
        visible={showTextCreator}
        onClose={() => setShowTextCreator(false)}
        onSave={handleSaveTextStatus}
      />

      <TransparentStatusModal
        visible={showStatusModal}
        onClose={() => setShowStatusModal(false)}
        onSelectOption={handleCreateStatus}
      />

      {/* 🔍 MODAL RICERCA STORIE - STILE PIATTAFORMA */}
      <StatusSearchModal
        visible={showSearchModal}
        onClose={() => setShowSearchModal(false)}
        statuses={statuses}
        renderStatusItem={renderStatusItem}
      />

      {/* ⋯ MENU OPZIONI STORIE - STILE PIATTAFORMA */}
      <StatusMenuModal
        visible={showOptionsMenu}
        onClose={() => setShowOptionsMenu(false)}
        myStatus={myStatus}
        onDeleteMyStory={handleDeleteMyStory}
        onOpenPrivacy={() => setShowPrivacyModal(true)}
        onOpenSettings={() => setShowSettingsModal(true)}
        onOpenNotifications={() => setShowNotificationsModal(true)}
      />

      {/* 🛡️ MODAL PRIVACY STORIE */}
      <StatusPrivacyModal
        visible={showPrivacyModal}
        onClose={() => setShowPrivacyModal(false)}
      />

      {/* ⚙️ MODAL IMPOSTAZIONI STORIE */}
      <StatusSettingsModal
        visible={showSettingsModal}
        onClose={() => setShowSettingsModal(false)}
      />

      {/* 🔔 MODAL NOTIFICHE STORIE */}
      <StatusNotificationsModal
        visible={showNotificationsModal}
        onClose={() => setShowNotificationsModal(false)}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#121212',
    paddingTop: 0,
    marginTop: 0,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-end',
    paddingHorizontal: 16,
    paddingTop: 0,
    paddingBottom: 16,
    height: Platform.OS === 'ios' ? 110 : 100,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  headerButtons: {
    flexDirection: 'row',
  },
  headerButton: {
    marginLeft: 16,
    padding: 8,
  },
  content: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  myStatusContainer: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#2A2A2A',
  },
  myStatus: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  myStatusImageContainer: {
    position: 'relative',
  },
  myStatusImageBorder: {
    width: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
  },
  myStatusImage: {
    width: 54,
    height: 54,
    borderRadius: 27,
    borderWidth: 2,
    borderColor: '#121212',
  },
  videoThumbnailContainer: {
    width: '100%',
    height: '100%',
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    borderRadius: 27,
    justifyContent: 'center',
    alignItems: 'center',
  },
  addStatusButton: {
    width: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
  },
  myStatusInfo: {
    marginLeft: 16,
  },
  myStatusText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  addStatusText: {
    fontSize: 14,
    color: '#AAAAAA',
    marginTop: 4,
  },
  sectionHeader: {
    padding: 16,
    paddingBottom: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#2A2A2A',
  },
  sectionTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#AAAAAA',
  },
  statusItem: {
    flexDirection: 'row',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#2A2A2A',
    alignItems: 'center',
  },
  statusImageContainer: {
    position: 'relative',
  },
  statusImageBorder: {
    width: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
  },
  statusImage: {
    width: 54,
    height: 54,
    borderRadius: 27,
    borderWidth: 2,
    borderColor: '#121212',
  },
  statusInfo: {
    flex: 1,
    marginLeft: 16,
  },
  statusName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  statusTimeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
  },
  statusTime: {
    fontSize: 14,
    color: '#AAAAAA',
  },
  viewCount: {
    fontSize: 14,
    color: '#AAAAAA',
    marginLeft: 8,
  },
  deleteButton: {
    padding: 8,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    marginTop: 50,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginTop: 16,
    textAlign: 'center',
  },
  emptySubtext: {
    fontSize: 14,
    color: '#AAAAAA',
    textAlign: 'center',
    marginTop: 8,
  },
  listContent: {
    flexGrow: 1,
  },
  fab: {
    position: 'absolute',
    bottom: Platform.OS === 'ios' ? 90 : 70,
    right: 20,
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    zIndex: 999,
  },
  fabGradient: {
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default StatusScreen;