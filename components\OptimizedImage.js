import React, { useState, useEffect, memo } from 'react';
import { Image, View, ActivityIndicator, StyleSheet } from 'react-native';
import * as FileSystem from 'expo-file-system';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { getImageSource, DEFAULT_IMAGES } from '../utils/optimizedImageUtils';

// Costanti per la gestione della cache
const IMAGE_CACHE_PREFIX = '@trendychat:image_cache_';
const IMAGE_CACHE_DIRECTORY = `${FileSystem.cacheDirectory}images/`;
const IMAGE_CACHE_TTL = 7 * 24 * 60 * 60 * 1000; // 7 giorni

// Assicurati che la directory della cache esista
const ensureCacheDirectory = async () => {
  try {
    const dirInfo = await FileSystem.getInfoAsync(IMAGE_CACHE_DIRECTORY);
    if (!dirInfo.exists) {
      await FileSystem.makeDirectoryAsync(IMAGE_CACHE_DIRECTORY, { intermediates: true });
    }
  } catch (error) {
    console.warn('Errore nella creazione della directory della cache:', error);
  }
};

// Inizializza la directory della cache
ensureCacheDirectory();

/**
 * Componente ottimizzato per il caricamento delle immagini con cache
 */
const OptimizedImage = memo(({
  source,
  style,
  resizeMode = 'cover',
  fallbackSource = DEFAULT_IMAGES.PLACEHOLDER,
  cacheEnabled = true,
  priority = 'normal',
  onLoad,
  onError,
  ...props
}) => {
  const [imageSource, setImageSource] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);

  useEffect(() => {
    let isMounted = true;
    
    const loadImage = async () => {
      try {
        // Se non c'è una fonte, usa il fallback
        if (!source) {
          if (isMounted) {
            setImageSource(fallbackSource);
            setIsLoading(false);
          }
          return;
        }

        // Se è un modulo locale o un oggetto, usalo direttamente
        if (typeof source !== 'string' || !source.startsWith('http')) {
          if (isMounted) {
            setImageSource(source);
            setIsLoading(false);
          }
          return;
        }

        // Per gli URL, usa la cache se abilitata
        if (cacheEnabled) {
          const cachedImage = await getCachedImage(source);
          
          if (cachedImage && isMounted) {
            setImageSource({ uri: cachedImage });
            setIsLoading(false);
            
            // Verifica se l'immagine in cache è scaduta
            const cacheInfo = await AsyncStorage.getItem(`${IMAGE_CACHE_PREFIX}${source}`);
            if (cacheInfo) {
              try {
                const { timestamp } = JSON.parse(cacheInfo);
                if (Date.now() - timestamp > IMAGE_CACHE_TTL) {
                  // Aggiorna la cache in background
                  updateCacheInBackground(source);
                }
              } catch (e) {
                // Ignora errori nel parsing
              }
            }
          } else {
            // Se non è in cache, scaricala
            await downloadAndCacheImage(source);
            
            if (isMounted) {
              const cachedImage = await getCachedImage(source);
              setImageSource(cachedImage ? { uri: cachedImage } : { uri: source });
              setIsLoading(false);
            }
          }
        } else {
          // Se la cache è disabilitata, usa l'URL direttamente
          if (isMounted) {
            setImageSource({ uri: source });
            setIsLoading(false);
          }
        }
      } catch (error) {
        console.error('Errore nel caricamento dell\'immagine:', error);
        
        if (isMounted) {
          setHasError(true);
          setImageSource(fallbackSource);
          setIsLoading(false);
          
          if (onError) {
            onError(error);
          }
        }
      }
    };

    setIsLoading(true);
    setHasError(false);
    loadImage();

    return () => {
      isMounted = false;
    };
  }, [source, cacheEnabled, fallbackSource, onError]);

  // Gestione del caricamento completato
  const handleLoad = () => {
    setIsLoading(false);
    if (onLoad) {
      onLoad();
    }
  };

  // Gestione degli errori
  const handleError = (error) => {
    setHasError(true);
    setImageSource(fallbackSource);
    if (onError) {
      onError(error);
    }
  };

  return (
    <View style={[styles.container, style]}>
      {imageSource && (
        <Image
          source={imageSource}
          style={[styles.image, style]}
          resizeMode={resizeMode}
          onLoad={handleLoad}
          onError={handleError}
          fadeDuration={300}
          {...props}
        />
      )}
      
      {isLoading && (
        <View style={[styles.loaderContainer, style]}>
          <ActivityIndicator
            size="small"
            color="#0066CC"
            style={styles.loader}
          />
        </View>
      )}
    </View>
  );
});

/**
 * Ottiene un'immagine dalla cache
 */
const getCachedImage = async (url) => {
  try {
    // Genera un nome di file sicuro
    const filename = url
      .replace(/[^a-zA-Z0-9]/g, '_')
      .replace(/_+/g, '_')
      .toLowerCase();
    
    const filePath = `${IMAGE_CACHE_DIRECTORY}${filename}`;
    
    // Verifica se il file esiste
    const fileInfo = await FileSystem.getInfoAsync(filePath);
    
    if (fileInfo.exists) {
      return filePath;
    }
    
    return null;
  } catch (error) {
    console.warn('Errore nel recupero dell\'immagine dalla cache:', error);
    return null;
  }
};

/**
 * Scarica e memorizza un'immagine nella cache
 */
const downloadAndCacheImage = async (url) => {
  try {
    // Genera un nome di file sicuro
    const filename = url
      .replace(/[^a-zA-Z0-9]/g, '_')
      .replace(/_+/g, '_')
      .toLowerCase();
    
    const filePath = `${IMAGE_CACHE_DIRECTORY}${filename}`;
    
    // Scarica l'immagine
    await FileSystem.downloadAsync(url, filePath);
    
    // Salva le informazioni sulla cache
    await AsyncStorage.setItem(`${IMAGE_CACHE_PREFIX}${url}`, JSON.stringify({
      url,
      path: filePath,
      timestamp: Date.now()
    }));
    
    return filePath;
  } catch (error) {
    console.warn('Errore nel download dell\'immagine:', error);
    throw error;
  }
};

/**
 * Aggiorna la cache in background
 */
const updateCacheInBackground = async (url) => {
  try {
    await downloadAndCacheImage(url);
  } catch (error) {
    // Ignora errori nell'aggiornamento in background
  }
};

const styles = StyleSheet.create({
  container: {
    overflow: 'hidden',
  },
  image: {
    width: '100%',
    height: '100%',
  },
  loaderContainer: {
    ...StyleSheet.absoluteFillObject,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f0f0f0',
  },
  loader: {
    position: 'absolute',
  },
});

export default OptimizedImage;
