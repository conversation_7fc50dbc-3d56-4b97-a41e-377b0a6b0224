export const APP_NAME = 'TrendyChat';

// IP del server HP ProLiant
export const SERVER_IP = '************';

export const STORAGE_KEYS = {
  AUTH_TOKEN: '@TrendyChat:authToken',
  USER_DATA: '@TrendyChat:userData',
  THEME: '@TrendyChat:theme',
  LANGUAGE: '@TrendyChat:language',
  NOTIFICATIONS: '@TrendyChat:notifications',
};

export const MESSAGE_TYPES = {
  TEXT: 'text',
  IMAGE: 'image',
  AUDIO: 'audio',
  VIDEO: 'video',
  DOCUMENT: 'document',
  LOCATION: 'location',
};

export const CHAT_TYPES = {
  PRIVATE: 'private',
  GROUP: 'group',
  BROADCAST: 'broadcast',
};

export const STATUS_TYPES = {
  TEXT: 'text',
  IMAGE: 'image',
  VIDEO: 'video',
};

export const NOTIFICATION_TYPES = {
  MESSAGE: 'message',
  CALL: 'call',
  GROUP: 'group',
  STATUS: 'status',
};

export const PRIVACY_OPTIONS = {
  EVERYONE: 'everyone',
  CONTACTS: 'contacts',
  NOBODY: 'nobody',
};

export const FILE_TYPES = {
  IMAGE: ['jpg', 'jpeg', 'png', 'gif'],
  VIDEO: ['mp4', 'mov', 'avi'],
  AUDIO: ['mp3', 'wav', 'm4a'],
  DOCUMENT: ['pdf', 'doc', 'docx', 'txt'],
};

export const MAX_FILE_SIZE = {
  IMAGE: 5 * 1024 * 1024, // 5MB
  VIDEO: 16 * 1024 * 1024, // 16MB
  AUDIO: 5 * 1024 * 1024, // 5MB
  DOCUMENT: 10 * 1024 * 1024, // 10MB
};

export const STATUS_DURATION = 24 * 60 * 60 * 1000; // 24 hours in milliseconds

export const TYPING_TIMEOUT = 3000; // 3 seconds

export const MESSAGE_PAGE_SIZE = 20;

export const CONTACTS_PAGE_SIZE = 50;

export const STATUS_PAGE_SIZE = 20;

export const DEFAULT_AVATAR = 'https://via.placeholder.com/150';

export const DEFAULT_GROUP_AVATAR = 'https://via.placeholder.com/150';

export const SUPPORTED_LANGUAGES = {
  it: 'Italiano',
  en: 'English',
  es: 'Español',
  fr: 'Français',
  de: 'Deutsch',
};

export const ERROR_MESSAGES = {
  NETWORK_ERROR: 'Errore di rete. Controlla la tua connessione.',
  AUTH_ERROR: 'Errore di autenticazione. Effettua nuovamente l\'accesso.',
  PERMISSION_ERROR: 'Permesso negato. Controlla le impostazioni dell\'app.',
  FILE_ERROR: 'Errore nel caricamento del file. Riprova.',
  UNKNOWN_ERROR: 'Si è verificato un errore. Riprova più tardi.',
};

export const APP_CONSTANTS = {
  // 🔧 CONFIGURAZIONE SERVER HP + SISTEMA IBRIDO
  serverHP: {
    baseURL: "http://************:3001",
    apiEndpoints: {
      auth: "/api/auth",
      users: "/api/users",
      chats: "/api/chats",
      messages: "/api/messages",
      media: "/api/cloud/upload",
      contacts: "/api/contacts",
      calls: "/api/calls",
      stories: "/api/stories"
    },
    timeout: 10000, // 10 secondi
    retryAttempts: 3
  },

  // Configurazione Sistema Ibrido WebRTC
  hybridSignaling: {
    primary: "wss://trendychat-signaling.onrender.com",
    fallback: "wss://signaling.trendychat.it",
    sfu: "wss://sfu.trendychat.it",
    turn: "turn.trendychat.it:3478",
    reconnectAttempts: 5,
    reconnectDelay: 2000
  },

  // Configurazione media
  media: {
    // Configurazione immagini
    image: {
      maxSize: 5 * 1024 * 1024, // 5 MB
      maxWidth: 1920,
      maxHeight: 1080,
      quality: 0.8,
      formats: ['jpg', 'jpeg', 'png', 'gif']
    },

    // Configurazione video
    video: {
      maxSize: 100 * 1024 * 1024, // 100 MB
      maxDuration: 300, // 5 minuti
      maxWidth: 1920,
      maxHeight: 1080,
      quality: 0.8,
      formats: ['mp4', 'mov', 'avi']
    },

    // Configurazione audio
    audio: {
      maxSize: 16 * 1024 * 1024, // 16 MB
      maxDuration: 300, // 5 minuti
      quality: 0.8,
      formats: ['mp3', 'wav', 'm4a']
    },

    // Configurazione documenti
    document: {
      maxSize: 100 * 1024 * 1024, // 100 MB
      formats: ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'txt']
    }
  },

  // Configurazione chat
  chat: {
    // Configurazione messaggi
    message: {
      maxLength: 4096,
      maxForward: 100,
      maxDelete: 100,
      editTime: 3600, // 1 ora
      maxPin: 3,
      maxReaction: 20
    },

    // Configurazione media
    media: {
      maxImages: 30,
      maxVideos: 10,
      maxAudio: 10,
      maxFiles: 10
    },

    // Configurazione stato
    status: {
      online: 'online',
      offline: 'offline',
      away: 'away',
      busy: 'busy'
    }
  },

  // Configurazione notifiche
  notification: {
    // Configurazione push
    push: {
      sound: true,
      vibration: true,
      priority: 'high',
      channel: 'default'
    },

    // Configurazione in-app
    inApp: {
      sound: true,
      vibration: true,
      duration: 5000 // 5 secondi
    }
  },

  // Configurazione cache
  cache: {
    // Configurazione memoria
    memory: {
      maxSize: 100 * 1024 * 1024, // 100 MB
      maxAge: 3600 // 1 ora
    },

    // Configurazione disco
    disk: {
      maxSize: 500 * 1024 * 1024, // 500 MB
      maxAge: 86400 // 24 ore
    }
  },

  // Configurazione API
  api: {
    // Configurazione rate limit
    rateLimit: {
      maxRequests: 100,
      windowMs: 900000 // 15 minuti
    },

    // Configurazione timeout
    timeout: {
      request: 30000, // 30 secondi
      upload: 300000, // 5 minuti
      download: 300000 // 5 minuti
    }
  },

  // Configurazione analisi
  analytics: {
    // Configurazione eventi
    events: {
      maxLength: 100,
      maxProperties: 25,
      maxValueLength: 100
    },

    // Configurazione metriche
    metrics: {
      maxLength: 100,
      maxValue: 1000000
    }
  },

  // Configurazione errori
  errors: {
    // Configurazione retry
    retry: {
      maxAttempts: 3,
      delay: 1000 // 1 secondo
    },

    // Configurazione timeout
    timeout: {
      request: 30000, // 30 secondi
      operation: 60000 // 1 minuto
    }
  },

  // Configurazione performance
  performance: {
    // Configurazione monitoraggio
    monitoring: {
      interval: 1000, // 1 secondo
      maxSamples: 100
    },

    // Configurazione soglie
    thresholds: {
      render: 16, // 60 FPS
      network: 1000, // 1 secondo
      memory: 100 * 1024 * 1024 // 100 MB
    }
  },

  // Configurazione debug
  debug: {
    // Configurazione logging
    logging: {
      maxLength: 1000,
      maxFiles: 5,
      maxSize: 10 * 1024 * 1024 // 10 MB
    },

    // Configurazione console
    console: {
      maxLength: 1000,
      maxLines: 100
    }
  }
};

// 🔧 CONFIGURAZIONE SERVER HP + SISTEMA IBRIDO
export const SERVER_CONFIG = {
  HP_SERVER: "http://************:3001",
  HYBRID_SIGNALING: {
    primary: "wss://trendychat-signaling.onrender.com",
    fallback: "wss://signaling.trendychat.it",
    sfu: "wss://sfu.trendychat.it",
    turn: "turn.trendychat.it:3478"
  }
};

// Configurazione App
export const APP_CONFIG = {
  name: 'TrendyChat',
  version: '1.0.0',
  build: '1',
  supportEmail: '<EMAIL>',
  website: 'https://trendychat.com',
  privacyPolicy: 'https://trendychat.com/privacy',
  termsOfService: 'https://trendychat.com/terms',
};

// Configurazione Media
export const MEDIA_CONFIG = {
  maxImageSize: 5 * 1024 * 1024, // 5MB
  maxVideoSize: 50 * 1024 * 1024, // 50MB
  maxAudioSize: 10 * 1024 * 1024, // 10MB
  maxDocumentSize: 20 * 1024 * 1024, // 20MB
  allowedImageTypes: ['image/jpeg', 'image/png', 'image/gif'],
  allowedVideoTypes: ['video/mp4', 'video/quicktime'],
  allowedAudioTypes: ['audio/mpeg', 'audio/wav', 'audio/ogg'],
  allowedDocumentTypes: ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
  imageQuality: 0.8,
  thumbnailSize: 200
};

// Configurazione Chat
export const CHAT_CONFIG = {
  maxMessageLength: 1000,
  maxMediaPerMessage: 10,
  typingTimeout: 3000,
  messageRetentionDays: 30,
  maxGroupMembers: 100,
  maxGroupAdmins: 10,
  maxGroupNameLength: 50,
  maxGroupDescriptionLength: 200
};

// Configurazione Status
export const STATUS_CONFIG = {
  maxDuration: 30, // 30 secondi
  maxTextLength: 100,
  maxMediaCount: 30,
  retentionHours: 24,
  maxViewers: 500,
  privacyOptions: ['everyone', 'contacts', 'nobody'],
  expiryOptions: [24, 48, 72], // ore
};

// Configurazione Notifiche
export const NOTIFICATION_CONFIG = {
  maxTitleLength: 100,
  maxBodyLength: 500,
  maxImageSize: 1024 * 1024, // 1MB
  retentionDays: 30,
  maxUnreadCount: 100,
  soundEnabled: true,
  vibrationEnabled: true,
  lightEnabled: true
};

// Configurazione Sicurezza
export const SECURITY_CONFIG = {
  minPasswordLength: 8,
  requireUppercase: true,
  requireLowercase: true,
  requireNumbers: true,
  requireSpecialChars: true,
  maxLoginAttempts: 5,
  lockoutDuration: 15 * 60 * 1000, // 15 minutes
  sessionTimeout: 24 * 60 * 60 * 1000, // 24 hours
  maxDevices: 5
};

// Configurazione Cache
export const CACHE_CONFIG = {
  maxSize: 100 * 1024 * 1024, // 100MB
  maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
  cleanupInterval: 60 * 60 * 1000 // 1 hour
};

// Configurazione API
export const API_CONFIG = {
  baseUrl: process.env.API_BASE_URL,
  timeout: 30000, // 30 seconds
  retryAttempts: 3,
  retryDelay: 1000, // 1 second
  maxConcurrentRequests: 5
};

// Configurazione Analytics
export const ANALYTICS_CONFIG = {
  enabled: true,
  trackScreenViews: true,
  trackUserActions: true,
  trackErrors: true,
  trackPerformance: true,
  trackNetwork: true,
  trackBattery: true,
  trackMemory: true
};

// Configurazione Errori
export const ERROR_CONFIG = {
  maxRetries: 3,
  retryDelay: 1000, // 1 second
  maxErrorLogs: 100,
  errorLogRetentionDays: 30,
  reportErrors: true,
  showErrorMessages: true
};

// Configurazione Performance
export const PERFORMANCE_CONFIG = {
  maxImageCacheSize: 50 * 1024 * 1024, // 50MB
  maxMemoryCacheSize: 100 * 1024 * 1024, // 100MB
  maxDiskCacheSize: 500 * 1024 * 1024, // 500MB
  cleanupInterval: 60 * 60 * 1000, // 1 hour
  monitorMemory: true,
  monitorBattery: true,
  monitorNetwork: true
};

// Configurazione Debug
export const DEBUG_CONFIG = {
  enabled: process.env.NODE_ENV === 'development',
  logLevel: 'debug',
  showDebugMenu: true,
  showPerformanceMonitor: true,
  showNetworkMonitor: true,
  showErrorBoundary: true,
  showDevMenu: true
};

// 🔧 ESPORTA TUTTE LE CONFIGURAZIONI (SENZA FIREBASE)
export const CONFIG_BUNDLE = {
  SERVER_CONFIG,
  MEDIA_CONFIG,
  CHAT_CONFIG,
  NOTIFICATION_CONFIG,
  SECURITY_CONFIG,
  CACHE_CONFIG,
  API_CONFIG,
  ANALYTICS_CONFIG,
  ERROR_CONFIG,
  PERFORMANCE_CONFIG,
  DEBUG_CONFIG
};

// Configurazione delle costanti
export const Constants = {
  // Configurazione generale
  general: {
    appName: 'TrendyChat',
    appVersion: '1.0.0',
    appBuild: '1',
    appEnvironment: process.env.NODE_ENV,
    appLanguage: 'it',
    appTheme: 'light'
  },

  // 🔧 CONFIGURAZIONE SERVER HP + SISTEMA IBRIDO
  serverHP: {
    baseURL: "http://************:3001",
    apiKey: process.env.HP_API_KEY,
    timeout: 10000,
    retryAttempts: 3
  },

  hybridSignaling: {
    primary: "wss://trendychat-signaling.onrender.com",
    fallback: "wss://signaling.trendychat.it",
    sfu: "wss://sfu.trendychat.it",
    turn: "turn.trendychat.it:3478"
  },

  // Configurazione dei media
  media: {
    maxImageSize: 5 * 1024 * 1024, // 5MB
    maxVideoSize: 50 * 1024 * 1024, // 50MB
    maxAudioSize: 10 * 1024 * 1024, // 10MB
    maxFileSize: 20 * 1024 * 1024, // 20MB
    allowedImageTypes: ['image/jpeg', 'image/png', 'image/gif'],
    allowedVideoTypes: ['video/mp4', 'video/quicktime'],
    allowedAudioTypes: ['audio/mpeg', 'audio/wav', 'audio/ogg', 'audio/m4a', 'audio/aac'],
    allowedFileTypes: ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document']
  },

  // Configurazione della chat
  chat: {
    maxMessageLength: 1000,
    maxMediaPerMessage: 10,
    maxParticipants: 100,
    typingTimeout: 5000, // 5 secondi
    messageRetention: 30 * 24 * 60 * 60 * 1000, // 30 giorni
    mediaRetention: 7 * 24 * 60 * 60 * 1000 // 7 giorni
  },

  // Configurazione delle notifiche
  notifications: {
    maxNotifications: 100,
    retentionPeriod: 7 * 24 * 60 * 60 * 1000, // 7 giorni
    soundEnabled: true,
    vibrationEnabled: true,
    badgeEnabled: true
  },

  // Configurazione della sicurezza
  security: {
    minPasswordLength: 8,
    maxLoginAttempts: 5,
    lockoutDuration: 15 * 60 * 1000, // 15 minuti
    sessionTimeout: 24 * 60 * 60 * 1000, // 24 ore
    tokenExpiration: 7 * 24 * 60 * 60 * 1000 // 7 giorni
  },

  // Configurazione della cache
  cache: {
    maxSize: 100 * 1024 * 1024, // 100MB
    maxAge: 24 * 60 * 60 * 1000, // 24 ore
    cleanupInterval: 60 * 60 * 1000 // 1 ora
  },

  // Configurazione delle API
  api: {
    baseUrl: 'https://api.trendychat.com',
    version: 'v1',
    timeout: 30000, // 30 secondi
    retries: 3,
    retryDelay: 1000 // 1 secondo
  },

  // Configurazione dell'analytics
  analytics: {
    enabled: true,
    sampleRate: 1.0, // 100% dei campioni
    maxEvents: 1000,
    flushInterval: 60000 // 1 minuto
  },

  // Configurazione degli errori
  errors: {
    maxErrors: 100,
    retentionPeriod: 7 * 24 * 60 * 60 * 1000, // 7 giorni
    logToConsole: true,
    logToFile: true
  },

  // Configurazione delle performance
  performance: {
    maxEvents: 1000,
    sampleRate: 1.0, // 100% dei campioni
    flushInterval: 60000 // 1 minuto
  },

  // Configurazione del debug
  debug: {
    enabled: process.env.NODE_ENV === 'development',
    level: 'info',
    maxLogs: 1000,
    maxFileSize: 10 * 1024 * 1024, // 10MB
    maxFiles: 5
  }
};

// Funzione helper per ottenere una costante
export const getConstant = (path) => {
  return path.split('.').reduce((obj, key) => obj && obj[key], Constants);
};

// Funzione helper per impostare una costante
export const setConstant = (path, value) => {
  const keys = path.split('.');
  const lastKey = keys.pop();
  const obj = keys.reduce((obj, key) => obj[key] = obj[key] || {}, Constants);
  obj[lastKey] = value;
};

// Esportiamo le funzioni e le costanti individualmente
// Non utilizziamo export default per evitare conflitti