import { Audio } from 'expo-av';
import { Platform } from 'react-native';

/**
 * WHATSAPP-STYLE VOICE RECORDER - SEMPLICE E FUNZIONANTE
 */
class RecordingManager {
  constructor() {
    this.currentRecording = null;
    this.isRecording = false;
    this.recordingStartTime = 0;
  }

  // Singleton instance
  static getInstance() {
    if (!RecordingManager.instance) {
      RecordingManager.instance = new RecordingManager();
    }
    return RecordingManager.instance;
  }

  // WHATSAPP: TIENI PREMUTO → INIZIA REGISTRAZIONE
  async startRecording() {
    try {
      console.log('🎤 WHATSAPP: Iniziando registrazione...');

      // CLEANUP AGGRESSIVO - FORZA RESET COMPLETO
      await this.cleanup();

      // ASPETTA UN MOMENTO PER ASSICURARSI CHE IL CLEANUP SIA COMPLETO
      await new Promise(resolve => setTimeout(resolve, 100));

      // Permessi
      const { status } = await Audio.requestPermissionsAsync();
      if (status !== 'granted') {
        throw new Error('Permessi microfono negati');
      }

      // Configura audio - CROSS-PLATFORM (ANDROID + iOS)
      const audioConfig = {
        allowsRecordingIOS: true,
        playsInSilentModeIOS: true,
      };

      // Configurazioni specifiche per piattaforma
      if (Platform.OS === 'android') {
        audioConfig.shouldDuckAndroid = false;
        audioConfig.staysActiveInBackground = true;
      }

      await Audio.setAudioModeAsync(audioConfig);

      // Crea registrazione
      const { recording } = await Audio.Recording.createAsync(
        Audio.RecordingOptionsPresets.HIGH_QUALITY
      );

      this.currentRecording = recording;
      this.isRecording = true;
      this.recordingStartTime = Date.now();

      console.log('✅ WHATSAPP: Registrazione avviata');
      return recording;
    } catch (error) {
      console.error('❌ WHATSAPP: Errore registrazione:', error);
      this.currentRecording = null;
      this.isRecording = false;
      throw error;
    }
  }

  // WHATSAPP: RILASCIA → FERMA E RESTITUISCI URI
  async stopRecording() {
    if (!this.currentRecording || !this.isRecording) {
      console.log('⚠️ WHATSAPP: Nessuna registrazione attiva');
      return null;
    }

    // CONTROLLO DURATA MINIMA (500ms)
    const recordingDuration = Date.now() - this.recordingStartTime;
    if (recordingDuration < 500) {
      console.log('⚠️ WHATSAPP: Registrazione troppo breve, aspetto...');
      await new Promise(resolve => setTimeout(resolve, 500 - recordingDuration));
    }

    try {
      console.log('🛑 WHATSAPP: Fermando registrazione...');

      const recording = this.currentRecording;

      // CONTROLLO SE RECORDING È ANCORA VALIDO
      if (!recording._canRecord) {
        console.log('⚠️ WHATSAPP: Recording già fermato dal sistema');
        this.currentRecording = null;
        this.isRecording = false;
        return null;
      }

      await recording.stopAndUnloadAsync();
      const uri = recording.getURI();

      this.currentRecording = null;
      this.isRecording = false;

      console.log('✅ WHATSAPP: Registrazione fermata, URI:', uri ? 'OK' : 'ERRORE');
      return uri;
    } catch (error) {
      console.error('❌ WHATSAPP: Errore stop:', error);
      this.currentRecording = null;
      this.isRecording = false;
      return null;
    }
  }

  // CLEANUP COMPLETO - PER CAMBIO CHAT
  async cleanup() {
    try {
      console.log('🧹 WHATSAPP: Cleanup RecordingManager...');

      // FORCE RESET IMMEDIATO
      this.isRecording = false;
      this.recordingStartTime = 0;

      if (this.currentRecording) {
        try {
          await this.currentRecording.stopAndUnloadAsync();
        } catch (e) {
          console.log('⚠️ WHATSAPP: Errore cleanup recording:', e.message);
        }
        this.currentRecording = null;
      }

      // RESET AUDIO MODE
      try {
        await Audio.setAudioModeAsync({
          allowsRecordingIOS: false,
          playsInSilentModeIOS: true,
        });
      } catch (e) {
        console.log('⚠️ WHATSAPP: Errore reset audio mode:', e.message);
      }

      console.log('✅ WHATSAPP: Cleanup completato');
    } catch (error) {
      console.error('❌ WHATSAPP: Errore cleanup:', error);
      // Force reset anche in caso di errore
      this.currentRecording = null;
      this.isRecording = false;
      this.recordingStartTime = 0;
    }
  }

  // Stato corrente
  getState() {
    return {
      isRecording: this.isRecording,
      recording: this.currentRecording
    };
  }
}

// Export singleton instance
export default RecordingManager.getInstance();
