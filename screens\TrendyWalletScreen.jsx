import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  Platform,
  StatusBar,
  Dimensions
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import useTrendyCoinStore from '../store/trendyCoinStore';
import useAuthStore from '../store/authStore';

const { width } = Dimensions.get('window');

const TrendyWalletScreen = () => {
  const navigation = useNavigation();
  const { user } = useAuthStore();
  const {
    adsBalance,
    liveBalance,
    balance,
    totalEarned,
    weeklyEarned,
    loading,
    loadTrendyCoins,
    processWeeklyPayout
  } = useTrendyCoinStore();

  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    loadWalletData();

    // Auto-refresh ogni 60 secondi per TrendyWallet
    const refreshInterval = setInterval(() => {
      if (!refreshing) {
        loadTrendyCoins();
      }
    }, 60000);

    return () => clearInterval(refreshInterval);
  }, []);

  const loadWalletData = async () => {
    try {
      setRefreshing(true);
      await loadTrendyCoins();
    } catch (error) {
      console.error('❌ Errore caricamento wallet:', error);
    } finally {
      setRefreshing(false);
    }
  };

  const handleWithdraw = () => {
    Alert.alert(
      '💰 Prelievo TrendyCoin',
      `Vuoi prelevare ${balance} TrendyCoin?\n\nSaranno convertiti in euro e trasferiti al tuo conto.`,
      [
        { text: 'Annulla', style: 'cancel' },
        {
          text: 'Preleva',
          onPress: processWithdrawal,
          style: 'destructive'
        }
      ]
    );
  };

  const processWithdrawal = async () => {
    try {
      await processWeeklyPayout();
      Alert.alert(
        '✅ Prelievo Completato',
        'I tuoi TrendyCoin sono stati prelevati con successo!'
      );
      await loadWalletData();
    } catch (error) {
      Alert.alert(
        '❌ Errore Prelievo',
        'Impossibile completare il prelievo. Riprova più tardi.'
      );
    }
  };

  const totalBalance = adsBalance + liveBalance + balance; // Solo saldi effettivi, NON weeklyEarned
  const euroValue = (totalBalance * 0.083).toFixed(2); // 1 TrendyCoin = €0.083

  return (
    <>
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent={true} />
      <SafeAreaView style={styles.container}>
        {/* Header */}
        <LinearGradient
          colors={['#1E88E5', '#D81B60']}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 0 }}
          style={styles.header}
        >
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <Ionicons name="arrow-back" size={24} color="#FFFFFF" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>TrendyWallet</Text>
          <TouchableOpacity
            style={styles.refreshButton}
            onPress={loadWalletData}
          >
            <Ionicons name="refresh" size={24} color="#FFFFFF" />
          </TouchableOpacity>
        </LinearGradient>

        {loading || refreshing ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#1E88E5" />
            <Text style={styles.loadingText}>Caricamento wallet...</Text>
          </View>
        ) : (
          <ScrollView style={styles.scrollContainer}>
            {/* Saldo Totale */}
            <View style={styles.totalBalanceContainer}>
              <LinearGradient
                colors={['#1E88E5', '#D81B60']}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 1 }}
                style={styles.totalBalanceGradient}
              >
                <View style={styles.totalBalanceContent}>
                  <Text style={styles.totalBalanceLabel}>Saldo Totale</Text>
                  <Text style={styles.totalBalanceAmount}>{totalBalance}</Text>
                  <Text style={styles.totalBalanceCurrency}>TrendyCoin</Text>
                  <Text style={styles.euroValue}>≈ €{euroValue}</Text>
                </View>
                <View style={styles.walletIcon}>
                  <Ionicons name="wallet" size={40} color="rgba(255,255,255,0.3)" />
                </View>
              </LinearGradient>
            </View>

            {/* Saldi Separati */}
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>💰 I Tuoi Saldi</Text>

              {/* Saldo Pubblicità */}
              <View style={styles.balanceCard}>
                <View style={styles.balanceHeader}>
                  <View style={styles.balanceIconContainer}>
                    <LinearGradient
                      colors={['#4CAF50', '#8BC34A']}
                      style={styles.balanceIconGradient}
                    >
                      <Ionicons name="tv-outline" size={20} color="#FFFFFF" />
                    </LinearGradient>
                  </View>
                  <View style={styles.balanceInfo}>
                    <Text style={styles.balanceTitle}>Saldo Pubblicità</Text>
                    <Text style={styles.balanceDescription}>Da video ads guardati</Text>
                  </View>
                  <Text style={styles.balanceAmount}>{adsBalance}</Text>
                </View>
              </View>

              {/* Saldo Live */}
              <View style={styles.balanceCard}>
                <View style={styles.balanceHeader}>
                  <View style={styles.balanceIconContainer}>
                    <LinearGradient
                      colors={['#FF5722', '#FF9800']}
                      style={styles.balanceIconGradient}
                    >
                      <Ionicons name="videocam-outline" size={20} color="#FFFFFF" />
                    </LinearGradient>
                  </View>
                  <View style={styles.balanceInfo}>
                    <Text style={styles.balanceTitle}>Saldo Live</Text>
                    <Text style={styles.balanceDescription}>Da regali ricevuti in live</Text>
                  </View>
                  <Text style={styles.balanceAmount}>{liveBalance}</Text>
                </View>
              </View>

              {/* Saldo Disponibile */}
              <View style={styles.balanceCard}>
                <View style={styles.balanceHeader}>
                  <View style={styles.balanceIconContainer}>
                    <LinearGradient
                      colors={['#1E88E5', '#D81B60']}
                      style={styles.balanceIconGradient}
                    >
                      <Ionicons name="cash-outline" size={20} color="#FFFFFF" />
                    </LinearGradient>
                  </View>
                  <View style={styles.balanceInfo}>
                    <Text style={styles.balanceTitle}>Saldo Disponibile</Text>
                    <Text style={styles.balanceDescription}>Pronto per il prelievo</Text>
                  </View>
                  <Text style={styles.balanceAmount}>{balance}</Text>
                </View>
              </View>
            </View>

            {/* Azioni */}
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>⚡ Azioni</Text>

              <TouchableOpacity
                style={styles.actionButton}
                onPress={handleWithdraw}
                disabled={balance <= 0}
              >
                <LinearGradient
                  colors={balance > 0 ? ['#4CAF50', '#8BC34A'] : ['#666666', '#888888']}
                  start={{ x: 0, y: 0 }}
                  end={{ x: 1, y: 0 }}
                  style={styles.actionGradient}
                >
                  <Ionicons name="download-outline" size={24} color="#FFFFFF" />
                  <Text style={styles.actionText}>
                    Preleva TrendyCoin ({balance})
                  </Text>
                </LinearGradient>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.actionButton}
                onPress={() => navigation.navigate('TrendyStore')}
              >
                <LinearGradient
                  colors={['#1E88E5', '#D81B60']}
                  start={{ x: 0, y: 0 }}
                  end={{ x: 1, y: 0 }}
                  style={styles.actionGradient}
                >
                  <Ionicons name="storefront-outline" size={24} color="#FFFFFF" />
                  <Text style={styles.actionText}>Acquista TrendyCoin</Text>
                </LinearGradient>
              </TouchableOpacity>
            </View>

            {/* Statistiche */}
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>📊 Statistiche</Text>

              <View style={styles.statsContainer}>
                <View style={styles.statItem}>
                  <Text style={styles.statValue}>{totalEarned}</Text>
                  <Text style={styles.statLabel}>Totale Guadagnato</Text>
                </View>
                <View style={styles.statItem}>
                  <Text style={styles.statValue}>{totalBalance}</Text>
                  <Text style={styles.statLabel}>Saldo Effettivo</Text>
                </View>
                <View style={styles.statItem}>
                  <Text style={styles.statValue}>€{euroValue}</Text>
                  <Text style={styles.statLabel}>Valore Euro</Text>
                </View>
              </View>
            </View>
          </ScrollView>
        )}
      </SafeAreaView>
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#121212',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    paddingTop: Platform.OS === 'ios' ? 50 : 30,
  },
  backButton: {
    padding: 8,
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  refreshButton: {
    padding: 8,
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    color: '#FFFFFF',
    marginTop: 16,
    fontSize: 16,
  },
  scrollContainer: {
    flex: 1,
    backgroundColor: '#121212',
  },
  // Saldo Totale
  totalBalanceContainer: {
    margin: 16,
    borderRadius: 16,
    overflow: 'hidden',
  },
  totalBalanceGradient: {
    padding: 24,
    position: 'relative',
  },
  totalBalanceContent: {
    alignItems: 'center',
  },
  totalBalanceLabel: {
    fontSize: 16,
    color: 'rgba(255,255,255,0.8)',
    marginBottom: 8,
  },
  totalBalanceAmount: {
    fontSize: 48,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 4,
  },
  totalBalanceCurrency: {
    fontSize: 18,
    color: 'rgba(255,255,255,0.9)',
    marginBottom: 8,
  },
  euroValue: {
    fontSize: 16,
    color: 'rgba(255,255,255,0.7)',
  },
  walletIcon: {
    position: 'absolute',
    top: 16,
    right: 16,
  },
  // Sezioni
  section: {
    marginHorizontal: 16,
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 16,
  },
  // Carte Saldo
  balanceCard: {
    backgroundColor: '#1E1E1E',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
  },
  balanceHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  balanceIconContainer: {
    marginRight: 12,
  },
  balanceIconGradient: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  balanceInfo: {
    flex: 1,
  },
  balanceTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
    marginBottom: 2,
  },
  balanceDescription: {
    fontSize: 14,
    color: '#AAAAAA',
  },
  balanceAmount: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  // Azioni
  actionButton: {
    marginBottom: 12,
    borderRadius: 12,
    overflow: 'hidden',
  },
  actionGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    paddingHorizontal: 24,
  },
  actionText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
    marginLeft: 8,
  },
  // Statistiche
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  statItem: {
    flex: 1,
    alignItems: 'center',
    backgroundColor: '#1E1E1E',
    borderRadius: 12,
    padding: 16,
    marginHorizontal: 4,
  },
  statValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: '#AAAAAA',
    textAlign: 'center',
  },
});

export default TrendyWalletScreen;
