import React, { useState } from 'react';
import {
  TouchableOpacity,
  StyleSheet,
  View,
  Modal,
  Text,
  Animated,
  Alert
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { useNavigation } from '@react-navigation/native';
import callRouter from '../services/callRouter';

const CallButton = ({ userId, userName }) => {
  const [showOptions, setShowOptions] = useState(false);
  const navigation = useNavigation();
  const scaleAnim = React.useRef(new Animated.Value(0)).current;

  const handleCallPress = () => {
    setShowOptions(true);
    Animated.spring(scaleAnim, {
      toValue: 1,
      useNativeDriver: true,
      tension: 50,
      friction: 7
    }).start();
  };

  const handleClose = () => {
    Animated.spring(scaleAnim, {
      toValue: 0,
      useNativeDriver: true,
      tension: 50,
      friction: 7
    }).start(() => {
      setShowOptions(false);
    });
  };

  const handleCallType = async (type) => {
    try {
      console.log(`📞 Avviando ${type} con ${userName || userId}`);

      // Opzioni chiamata
      const options = {
        isVideoEnabled: type === 'video',
        isAudioEnabled: true,
        forceP2P: true // Forza P2P per chiamate 1-a-1
      };

      // Avvia chiamata tramite Call Router
      const callData = await callRouter.startCall([userId], options);

      console.log('✅ Chiamata avviata:', callData);

      // Naviga alla schermata di chiamata
      navigation.navigate('Call', {
        callId: callData.id,
        callType: callData.type,
        isIncoming: false,
        recipientId: userId,
        recipientName: userName,
        isVideoCall: type === 'video',
        server: callData.server
      });

      handleClose();

    } catch (error) {
      console.error('❌ Errore avvio chiamata:', error);

      Alert.alert(
        'Errore Chiamata',
        'Impossibile avviare la chiamata. Riprova più tardi.',
        [{ text: 'OK' }]
      );

      handleClose();
    }
  };

  return (
    <>
      <TouchableOpacity
        style={styles.button}
        onPress={handleCallPress}
      >
        <Icon name="phone" size={24} color="#fff" />
      </TouchableOpacity>

      <Modal
        visible={showOptions}
        transparent
        animationType="none"
        onRequestClose={handleClose}
      >
        <TouchableOpacity
          style={styles.modalOverlay}
          activeOpacity={1}
          onPress={handleClose}
        >
          <Animated.View
            style={[
              styles.optionsContainer,
              {
                transform: [{ scale: scaleAnim }]
              }
            ]}
          >
            <TouchableOpacity
              style={styles.option}
              onPress={() => handleCallType('audio')}
            >
              <Icon name="phone" size={24} color="#4CAF50" />
              <Text style={styles.optionText}>Chiamata vocale</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.option}
              onPress={() => handleCallType('video')}
            >
              <Icon name="video" size={24} color="#2196F3" />
              <Text style={styles.optionText}>Videochiamata</Text>
            </TouchableOpacity>
          </Animated.View>
        </TouchableOpacity>
      </Modal>
    </>
  );
};

const styles = StyleSheet.create({
  button: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#4CAF50',
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 5,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  optionsContainer: {
    backgroundColor: '#fff',
    borderRadius: 15,
    padding: 20,
    width: '80%',
    maxWidth: 300,
  },
  option: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  optionText: {
    fontSize: 16,
    marginLeft: 15,
  },
});

export default CallButton;