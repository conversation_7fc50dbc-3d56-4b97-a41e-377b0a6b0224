import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  ScrollView,
  Image,
  Alert,
  Switch,
  Linking,
  Platform
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation, useRoute } from '@react-navigation/native';
import { LinearGradient } from 'expo-linear-gradient';
import AsyncStorage from '@react-native-async-storage/async-storage';
import useChatStore from '../store/useChatStore';
import useAuthStore from '../store/authStore';
import usePresenceStore from '../store/usePresenceStore';
import useContactStore from '../store/contactStore';
import { useFocusEffect } from '@react-navigation/native';

const ContactInfoScreen = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const { chatId, contactId, name, isPrivateChat } = route.params;
  const { user } = useAuthStore();
  const { updateChatSettings, clearChatMessages } = useChatStore();
  const { blockContact, unblockContact } = useContactStore();

  const [contact, setContact] = useState({
    id: contactId || 'unknown',
    name: name || 'Utente',
    phoneNumber: 'Caricamento...',
    about: 'Caricamento...',
    photoURL: null,
    online: false,
    lastSeen: new Date().toISOString(),
  });

  const [isMuted, setIsMuted] = useState(false);
  const [isBlocked, setIsBlocked] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Carica i dati del contatto dal database
    loadContactInfo();
    // 🔧 CARICA ANCHE LE IMPOSTAZIONI DELLA CHAT
    loadChatSettings();
  }, [contactId]);

  // 🔄 RICARICA AUTOMATICA QUANDO LA SCHERMATA TORNA IN FOCUS
  useFocusEffect(
    React.useCallback(() => {
      console.log('📱 ContactInfo: Schermata in focus, ricaricando dati...');
      loadContactInfo();
      // 🔧 RICARICA ANCHE LE IMPOSTAZIONI
      loadChatSettings();

      // Avvia aggiornamento presenza ogni 30 secondi
      const presenceInterval = setInterval(() => {
        updatePresenceStatus();
      }, 30000);

      return () => {
        clearInterval(presenceInterval);
      };
    }, [contactId])
  );

  // 🟢 AGGIORNA SOLO LO STATO DI PRESENZA (PIÙ VELOCE)
  const updatePresenceStatus = async () => {
    try {
      // ✅ LOGICA CHAT CON SE STESSI (come in ChatRoomScreen)
      if (user && contactId === user.id) {
        console.log('🟢 Info Contatto - Chat con se stessi, impostando Online');
        setContact(prev => ({
          ...prev,
          online: true,
          lastSeen: new Date().toISOString()
        }));
        return;
      }

      const { getUserPresence } = usePresenceStore.getState();
      const presenceData = await getUserPresence(contactId);

      if (presenceData && presenceData.success) {
        setContact(prev => ({
          ...prev,
          online: presenceData.online,
          lastSeen: presenceData.lastSeen
        }));
        console.log('🔄 ContactInfo: Presenza aggiornata:', {
          online: presenceData.online,
          lastSeen: presenceData.lastSeen
        });
      }
    } catch (error) {
      console.error('❌ Errore aggiornamento presenza:', error);
    }
  };

  // 🔧 CARICA LE IMPOSTAZIONI DELLA CHAT DAL SERVER
  const loadChatSettings = async () => {
    try {
      console.log('⚙️ ContactInfo: Caricando impostazioni chat per:', chatId);

      const token = await AsyncStorage.getItem('@trendychat:token');
      const userData = await AsyncStorage.getItem('@trendychat:user');

      if (!token || !userData) {
        console.log('❌ Token o userData non trovato per impostazioni');
        return;
      }

      const currentUserId = JSON.parse(userData).id;
      console.log('🔍 ContactInfo: userId recuperato:', currentUserId);

      // Carica le impostazioni della chat dal server
      const response = await fetch(`http://192.168.1.66:3001/api/groups/${chatId}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        console.log('⚙️ ContactInfo: Dati chat ricevuti:', data);

        if (data.success && data.group) {
          const chatSettings = data.group.settings || {};

          // Aggiorna lo stato muted dalle impostazioni della chat
          setIsMuted(chatSettings.notifications?.muted || false);

          console.log('✅ ContactInfo: Impostazioni caricate:', {
            muted: chatSettings.notifications?.muted || false
          });
        }
      } else {
        console.log('⚠️ Impossibile caricare impostazioni chat:', response.status);
      }

      // 🚫 CARICA SEPARATAMENTE LO STATO DI BLOCCO DAL SERVER HP
      try {
        const blockResponse = await fetch(`http://192.168.1.66:3001/api/users/blocked?userId=${currentUserId}`);

        if (blockResponse.ok) {
          const blockData = await blockResponse.json();

          if (blockData.success) {
            const blockedUsers = blockData.blockedUsers || [];
            const isContactBlocked = blockedUsers.includes(contact.id || contactId);

            setIsBlocked(isContactBlocked);
            console.log('🚫 Stato blocco caricato:', {
              contactId: contact.id || contactId,
              isBlocked: isContactBlocked,
              totalBlocked: blockedUsers.length
            });
          }
        }
      } catch (blockError) {
        console.error('❌ Errore caricamento stato blocco:', blockError);
      }

    } catch (error) {
      console.error('❌ Errore caricamento impostazioni chat:', error);
    }
  };

  const loadContactInfo = async () => {
    console.log('📱 ContactInfo: Caricando dati reali per:', contactId, name);

    try {
      // Ottieni i dati reali dell'utente dal server
      const token = await AsyncStorage.getItem('@trendychat:token');
      if (!token) {
        console.log('❌ Token non trovato');
        return;
      }

      // Cerca l'utente dal server usando l'endpoint corretto
      let response;

      // Usa lo stesso metodo della barra di ricerca che funziona
      console.log('🔍 Cercando utente per ID:', contactId);
      response = await fetch(`http://192.168.1.66:3001/api/users`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response && response.ok) {
        const data = await response.json();
        const users = data.users || [];

        // Cerca l'utente per ID
        let userData = users.find(u => u.id === contactId);

        // Se non trovato per ID, cerca per nome
        if (!userData && name) {
          console.log('🔍 Cercando utente per nome:', name);
          userData = users.find(u => u.displayName === name || u.name === name);
        }

        if (userData) {
          console.log('✅ ContactInfo: Utente trovato dal server:', {
            id: userData.id,
            name: userData.displayName || userData.name,
            phoneNumber: userData.phoneNumber,
            status: userData.status || userData.about,
            photoURL: userData.photoURL || userData.avatar,
            online: userData.online,
            lastSeen: userData.lastSeen
          });

          const updatedContact = {
            id: userData.id || userData._id || contactId,
            name: userData.displayName || userData.name || name,
            phoneNumber: userData.phoneNumber || 'Non disponibile',
            about: userData.status || userData.about || 'Hey, sto usando TrendyChat!',
            photoURL: userData.photoURL || userData.avatar,
            // ✅ LOGICA CHAT CON SE STESSI: Se è il proprio ID, sempre online
            online: (user && contactId === user.id) ? true : (userData.online || false),
            lastSeen: userData.lastSeen || new Date().toISOString(),
          };

          setContact(updatedContact);
          console.log('📱 ContactInfo: Dati contatto aggiornati:', updatedContact);
          setLoading(false);
          return;
        }
      }

      // Fallback: prova con la ricerca per nome se tutto il resto fallisce
      if (name) {
        console.log('🔍 Fallback: Cercando utente per nome:', name);
        response = await fetch(`http://192.168.1.66:3001/api/users/search?query=${encodeURIComponent(name)}`, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });
      }

      if (response.ok) {
        const data = await response.json();
        console.log('📡 Dati utente ricevuti:', data);

        let userData = null;

        // Gestisci diversi formati di risposta
        if (data.user) {
          // Risposta singola da /api/users/{id}
          userData = data.user;
        } else if (data.users && data.users.length > 0) {
          // Risposta multipla da /api/users/search
          userData = data.users.find(u => u.displayName === name) || data.users[0];
        } else if (data.displayName || data.phoneNumber) {
          // Risposta diretta
          userData = data;
        }

        if (userData) {
          console.log('✅ ContactInfo: Utente trovato (fallback):', {
            id: userData.id,
            name: userData.displayName || userData.name,
            phoneNumber: userData.phoneNumber,
            status: userData.status || userData.about,
            photoURL: userData.photoURL || userData.avatar,
            online: userData.online,
            lastSeen: userData.lastSeen
          });

          const updatedContact = {
            id: userData.id || userData._id || contactId,
            name: userData.displayName || userData.name || name,
            phoneNumber: userData.phoneNumber || 'Non disponibile',
            about: userData.status || userData.about || 'Hey, sto usando TrendyChat!',
            photoURL: userData.photoURL || userData.avatar,
            // ✅ LOGICA CHAT CON SE STESSI: Se è il proprio ID, sempre online
            online: (user && contactId === user.id) ? true : (userData.online || false),
            lastSeen: userData.lastSeen || new Date().toISOString(),
          };

          setContact(updatedContact);
          console.log('📱 ContactInfo: Dati contatto aggiornati (fallback):', updatedContact);
        } else {
          console.log('⚠️ Utente non trovato, uso dati dai parametri');
          setContact(prev => ({
            ...prev,
            name: name || 'Utente',
            phoneNumber: 'Non disponibile'
          }));
        }
      } else {
        console.log('❌ Errore risposta server:', response.status);
      }
    } catch (error) {
      console.error('❌ Errore caricamento dati contatto:', error);
      // Fallback con dati dai parametri
      setContact(prev => ({
        ...prev,
        name: name || 'Utente',
        phoneNumber: 'Non disponibile'
      }));
    } finally {
      setLoading(false);
    }
  };

  const handleToggleMute = async () => {
    try {
      setIsMuted(!isMuted);

      // Aggiorna le impostazioni della chat
      await updateChatSettings(chatId, {
        notifications: {
          muted: !isMuted
        }
      });
    } catch (error) {
      console.error('Errore durante l\'aggiornamento delle impostazioni:', error);
      setIsMuted(isMuted); // Ripristina lo stato precedente in caso di errore
      Alert.alert('Errore', 'Impossibile aggiornare le impostazioni di notifica');
    }
  };

  const handleToggleBlock = async () => {
    try {
      const userData = await AsyncStorage.getItem('@trendychat:user');
      if (!userData) {
        Alert.alert('Errore', 'Utente non autenticato');
        return;
      }

      const currentUserId = JSON.parse(userData).id;
      console.log('🔍 ContactInfo: userId per blocco:', currentUserId);

      // 🚫 CONTROLLO: Non permettere di bloccare se stessi
      if (currentUserId === contact.id) {
        Alert.alert('Attenzione', 'Non puoi bloccare te stesso!');
        return;
      }

      if (!isBlocked) {
        // Conferma blocco
        Alert.alert(
          'Blocca contatto',
          `Vuoi bloccare ${contact.name}? Non riceverai più messaggi o chiamate da questo contatto.`,
          [
            { text: 'Annulla', style: 'cancel' },
            {
              text: 'Blocca',
              style: 'destructive',
              onPress: async () => {
                try {
                  console.log(`🚫 Tentativo di bloccare utente: ${contact.id}`);

                  const response = await fetch('http://192.168.1.66:3001/api/users/block', {
                    method: 'POST',
                    headers: {
                      'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                      userId: currentUserId,
                      blockedUserId: contact.id
                    }),
                  });

                  const data = await response.json();

                  if (data.success) {
                    setIsBlocked(true);
                    Alert.alert('Contatto bloccato', `Hai bloccato ${contact.name}`);
                    console.log(`✅ Utente bloccato con successo`);
                  } else {
                    throw new Error(data.message || 'Errore sconosciuto');
                  }
                } catch (error) {
                  console.error('❌ Errore blocco contatto:', error);
                  Alert.alert('Errore', 'Impossibile bloccare il contatto');
                }
              }
            }
          ]
        );
      } else {
        // Sblocca contatto
        try {
          console.log(`✅ Tentativo di sbloccare utente: ${contact.id}`);

          const response = await fetch('http://192.168.1.66:3001/api/users/unblock', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              userId: currentUserId,
              unblockedUserId: contact.id
            }),
          });

          const data = await response.json();

          if (data.success) {
            setIsBlocked(false);
            Alert.alert('Contatto sbloccato', `Hai sbloccato ${contact.name}`);
            console.log(`✅ Utente sbloccato con successo`);
          } else {
            throw new Error(data.message || 'Errore sconosciuto');
          }
        } catch (error) {
          console.error('❌ Errore sblocco contatto:', error);
          Alert.alert('Errore', 'Impossibile sbloccare il contatto');
        }
      }
    } catch (error) {
      console.error('❌ Errore durante l\'aggiornamento delle impostazioni:', error);
      Alert.alert('Errore', 'Impossibile aggiornare le impostazioni di blocco');
    }
  };

  const handleCall = () => {
    navigation.navigate('CallScreen', { contactId: contact.id, name: contact.name, isVideo: false });
  };

  const handleVideoCall = () => {
    navigation.navigate('CallScreen', { contactId: contact.id, name: contact.name, isVideo: true });
  };

  const handleSearchMessages = () => {
    navigation.navigate('ChatSearch', { chatId, name: contact.name });
  };

  const handleViewMedia = () => {
    navigation.navigate('ChatMedia', {
      chatId,
      name: contact.name,
      isPrivateChat: isPrivateChat || false // 🔧 Passa il flag per distinguere chat private da gruppi
    });
  };

  const handleChatSettings = () => {
    navigation.navigate('ChatNotification', { chatId, name: contact.name });
  };

  const handleChatBackground = () => {
    navigation.navigate('ChatBackground', { chatId, name: contact.name });
  };

  const handleEphemeralMessages = () => {
    navigation.navigate('EphemeralMessages', { chatId, name: contact.name });
  };



  const handleClearChat = () => {
    // 🔧 LOGICA REPLICATA DAL MENU HAMBURGER
    Alert.alert(
      'Svuota chat',
      'Vuoi eliminare tutti i messaggi di questa chat?',
      [
        { text: 'Annulla', style: 'cancel' },
        {
          text: 'Elimina',
          style: 'destructive',
          onPress: async () => {
            try {
              // Usa la stessa logica del menu hamburger
              if (clearChatMessages) {
                await clearChatMessages(chatId);
              }
              // Mostra messaggio di successo dopo un breve delay
              setTimeout(() => {
                Alert.alert('Chat svuotata', 'Tutti i messaggi sono stati eliminati');
              }, 1000);
            } catch (error) {
              console.error('Errore svuotamento chat:', error);
              Alert.alert('Errore', 'Impossibile svuotare la chat');
            }
          }
        }
      ]
    );
  };

  const formatLastSeen = (dateString) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffMs = now - date;
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMins / 60);
    const diffDays = Math.floor(diffHours / 24);

    if (diffMins < 1) {
      return 'online ora';
    } else if (diffMins < 60) {
      return `ultimo accesso ${diffMins} min fa`;
    } else if (diffHours < 24) {
      return `ultimo accesso ${diffHours} ore fa`;
    } else {
      return `ultimo accesso ${diffDays} giorni fa`;
    }
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent={true} />

      <LinearGradient
        colors={['#1E88E5', '#D81B60']}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 0 }}
        style={styles.header}
      >
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color="#FFFFFF" />
        </TouchableOpacity>

        <Text style={styles.headerTitle}>Info contatto</Text>
      </LinearGradient>

      <ScrollView style={styles.content}>
        <View style={styles.profileSection}>
          <View style={styles.avatarContainer}>
            {contact.photoURL ? (
              <Image source={{ uri: contact.photoURL }} style={styles.avatar} />
            ) : (
              <LinearGradient
                colors={['#1E88E5', '#D81B60']}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 0 }}
                style={styles.defaultAvatar}
              >
                <Text style={styles.avatarText}>{contact.name.charAt(0)}</Text>
              </LinearGradient>
            )}
          </View>

          <Text style={styles.contactName}>{contact.name}</Text>
          <Text style={styles.contactPhone}>{contact.phoneNumber}</Text>

          <Text style={styles.lastSeen}>
            {contact.online ? 'online' : formatLastSeen(contact.lastSeen)}
          </Text>
        </View>

        {/* ✅ SEZIONE STATO SPOSTATA AL POSTO DELLE 3 ICONE */}
        <View style={styles.infoSection}>
          <Text style={styles.sectionTitle}>Stato</Text>
          <Text style={styles.aboutText}>{contact.about}</Text>
        </View>

        <View style={styles.optionsSection}>
          <TouchableOpacity style={styles.optionRow} onPress={handleViewMedia}>
            <LinearGradient
              colors={['#1E88E5', '#D81B60']}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 0 }}
              style={styles.optionIconContainer}
            >
              <Ionicons name="images-outline" size={20} color="#FFFFFF" />
            </LinearGradient>
            <View style={styles.optionTextContainer}>
              <Text style={styles.optionTitle}>Media, link e documenti</Text>
            </View>
            <Ionicons name="chevron-forward" size={20} color="#666666" />
          </TouchableOpacity>

          <TouchableOpacity style={styles.optionRow} onPress={handleChatSettings}>
            <LinearGradient
              colors={['#1E88E5', '#D81B60']}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 0 }}
              style={styles.optionIconContainer}
            >
              <Ionicons name="notifications-outline" size={20} color="#FFFFFF" />
            </LinearGradient>
            <View style={styles.optionTextContainer}>
              <Text style={styles.optionTitle}>Silenzia notifiche</Text>
            </View>
            <Switch
              value={isMuted}
              onValueChange={handleToggleMute}
              trackColor={{ false: '#767577', true: '#D81B60' }}
              thumbColor={isMuted ? '#1E88E5' : '#f4f3f4'}
            />
          </TouchableOpacity>

          <TouchableOpacity style={styles.optionRow} onPress={handleEphemeralMessages}>
            <LinearGradient
              colors={['#1E88E5', '#D81B60']}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 0 }}
              style={styles.optionIconContainer}
            >
              <Ionicons name="timer-outline" size={20} color="#FFFFFF" />
            </LinearGradient>
            <View style={styles.optionTextContainer}>
              <Text style={styles.optionTitle}>Messaggi effimeri</Text>
            </View>
            <Ionicons name="chevron-forward" size={20} color="#666666" />
          </TouchableOpacity>

          <TouchableOpacity style={styles.optionRow} onPress={handleChatBackground}>
            <LinearGradient
              colors={['#1E88E5', '#D81B60']}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 0 }}
              style={styles.optionIconContainer}
            >
              <Ionicons name="color-palette-outline" size={20} color="#FFFFFF" />
            </LinearGradient>
            <View style={styles.optionTextContainer}>
              <Text style={styles.optionTitle}>Sfondo chat</Text>
            </View>
            <Ionicons name="chevron-forward" size={20} color="#666666" />
          </TouchableOpacity>

          {/* 🔧 NUOVE FUNZIONALITÀ REPLICATE DAL MENU HAMBURGER */}

          <TouchableOpacity style={styles.optionRow} onPress={handleToggleBlock}>
            <LinearGradient
              colors={['#1E88E5', '#D81B60']}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 0 }}
              style={styles.optionIconContainer}
            >
              <Ionicons name="ban-outline" size={20} color="#FFFFFF" />
            </LinearGradient>
            <View style={styles.optionTextContainer}>
              <Text style={styles.optionTitle}>{isBlocked ? 'Sblocca' : 'Blocca'}</Text>
            </View>
            <Switch
              value={isBlocked}
              onValueChange={handleToggleBlock}
              trackColor={{ false: '#767577', true: '#D81B60' }}
              thumbColor={isBlocked ? '#1E88E5' : '#f4f3f4'}
            />
          </TouchableOpacity>

          <TouchableOpacity style={styles.optionRow} onPress={handleClearChat}>
            <LinearGradient
              colors={['#1E88E5', '#D81B60']}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 0 }}
              style={styles.optionIconContainer}
            >
              <Ionicons name="trash-outline" size={20} color="#FFFFFF" />
            </LinearGradient>
            <View style={styles.optionTextContainer}>
              <Text style={styles.optionTitle}>Svuota chat</Text>
            </View>
            <Ionicons name="chevron-forward" size={20} color="#666666" />
          </TouchableOpacity>


        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#121212',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingTop: Platform.OS === 'ios' ? 50 : 30,
    paddingBottom: 10,
    paddingHorizontal: 15,
  },
  backButton: {
    padding: 8,
    marginRight: 10,
  },
  headerTitle: {
    color: '#FFFFFF',
    fontSize: 20,
    fontWeight: 'bold',
  },
  content: {
    flex: 1,
  },
  profileSection: {
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 255, 255, 0.1)',
    backgroundColor: 'rgba(30, 136, 229, 0.05)',
    marginBottom: 10,
  },
  avatarContainer: {
    width: 100,
    height: 100,
    borderRadius: 50,
    overflow: 'hidden',
    marginBottom: 15,
  },
  avatar: {
    width: '100%',
    height: '100%',
  },
  defaultAvatar: {
    width: '100%',
    height: '100%',
    backgroundColor: '#1E88E5',
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatarText: {
    color: '#FFFFFF',
    fontSize: 40,
    fontWeight: 'bold',
  },
  contactName: {
    color: '#FFFFFF',
    fontSize: 22,
    fontWeight: 'bold',
    marginBottom: 5,
  },
  contactPhone: {
    color: '#AAAAAA',
    fontSize: 16,
    marginBottom: 5,
  },
  lastSeen: {
    color: '#AAAAAA',
    fontSize: 14,
    marginBottom: 20,
  },
  actionsRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    width: '100%',
  },
  actionButton: {
    alignItems: 'center',
  },
  actionIconContainer: {
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 5,
  },
  actionText: {
    color: '#FFFFFF',
    fontSize: 14,
  },
  infoSection: {
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 255, 255, 0.1)',
    backgroundColor: 'rgba(30, 136, 229, 0.05)',
    marginBottom: 10,
    borderRadius: 10,
    marginHorizontal: 10,
  },
  sectionTitle: {
    color: '#1E88E5',
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  aboutText: {
    color: '#FFFFFF',
    fontSize: 16,
  },
  optionsSection: {
    marginBottom: 20,
    backgroundColor: 'rgba(30, 136, 229, 0.05)',
    borderRadius: 10,
    marginHorizontal: 10,
    overflow: 'hidden',
  },
  optionRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 15,
    paddingHorizontal: 20,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 255, 255, 0.1)',
  },
  optionIcon: {
    marginRight: 15,
  },
  optionIconContainer: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 15,
  },
  optionTextContainer: {
    flex: 1,
  },
  optionTitle: {
    color: '#FFFFFF',
    fontSize: 16,
  },

});

export default ContactInfoScreen;
