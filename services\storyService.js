import AsyncStorage from '../utils/asyncStorageConfig';
import { API_URL } from '../config/api';
import useAuthStore from '../store/authStore';

/**
 * Servizio per la gestione delle storie
 */
class StoryService {
  constructor() {
    // Rimuovi /api da ${API_URL}/api/stories perché API_URL già include /api
    this.baseUrl = `${API_URL}/stories`;
    console.log('URL base per le storie:', this.baseUrl);
  }

  /**
   * Ottiene il token di autenticazione
   * @returns {Promise<string>} Token di autenticazione
   */
  async getAuthToken() {
    try {
      // Importa il servizio di autenticazione
      const authService = require('./authService').default;

      // Ottieni il token direttamente dal servizio di autenticazione
      let token = authService.getToken();
      console.log('Token da authService:', token ? 'Trovato' : 'Non trovato');

      // Se il token contiene il prefisso jwt_token_, rimuovilo
      if (token && token.startsWith('jwt_token_')) {
        token = token.replace('jwt_token_', '');
        console.log('Prefisso jwt_token_ rimosso dal token');
      }

      return token;
    } catch (error) {
      console.error('Errore nel recupero del token di autenticazione:', error);
      return null;
    }
  }

  /**
   * Carica un media per una storia
   * @param {string} mediaUri - URI del media
   * @param {string} userId - ID dell'utente
   * @param {string} mediaType - Tipo di media (image o video)
   * @returns {Promise<Object>} Risultato dell'operazione
   */
  async uploadStoryMedia(mediaUri, userId, mediaType = 'image') {
    try {
      console.log('Caricamento media storia sul server HP:', mediaUri);
      console.log('ID utente:', userId);
      console.log('Tipo di media:', mediaType);

      // Verifica che l'URI del media sia valido
      if (!mediaUri || typeof mediaUri !== 'string') {
        throw new Error('URI del media non valido');
      }

      // Ottieni il token di autenticazione
      const token = await this.getAuthToken();
      if (!token) {
        throw new Error('Token di autenticazione non disponibile');
      }

      // Importa il servizio cloud
      const cloudService = require('./cloudService').default;
      
      // Assicurati che cloudService sia inizializzato con il token
      cloudService.setToken(token);
      
      // Carica il media sul server con metadati espliciti per il tipo di file
      const uploadResult = await cloudService.uploadFile(
        mediaUri,
        userId,
        'story',
        true, // I media delle storie sono pubblici
        {
          type: 'story',
          userId: userId,
          mediaType: mediaType,
          isStoryMedia: true,
          expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString() // Scade dopo 24 ore
        }
      );
      
      if (!uploadResult.success) {
        throw new Error(uploadResult.error || 'Errore durante il caricamento del media');
      }
      
      console.log('Media storia caricato con successo:', uploadResult.file);
      
      return {
        success: true,
        mediaUrl: uploadResult.file.url,
        localUri: mediaUri,
        data: uploadResult // Includi tutti i dati della risposta
      };
    } catch (error) {
      console.error('Errore nel caricamento del media della storia:', error);

      // In caso di errore generale, restituisci l'URI locale come fallback
      return {
        success: false,
        error: error.message,
        mediaUrl: mediaUri,
        localUri: mediaUri
      };
    }
  }

  /**
   * Crea una nuova storia
   * @param {Object} storyData - Dati della storia
   * @returns {Promise<Object>} Risultato dell'operazione
   */
  async createStory(storyData) {
    try {
      console.log('Creazione storia sul server HP:', storyData);

      // Ottieni il token di autenticazione
      const token = await this.getAuthToken();
      console.log('Token di autenticazione:', token ? 'Disponibile' : 'Non disponibile');

      if (!token) {
        throw new Error('Token di autenticazione non disponibile');
      }

      // Ottieni l'ID utente dal servizio di autenticazione
      const authService = require('./authService').default;
      const currentUser = authService.getCurrentUser();
      const userId = currentUser?.id || currentUser?._id;

      if (!userId) {
        console.warn('ID utente non disponibile, potrebbe causare problemi con la creazione della storia');
      }

      console.log('ID utente corrente:', userId);

      let mediaUrl = null;
      let mediaType = storyData.mediaType || 'image';

      // Fase 1: Caricamento del media (se presente)
      if (storyData.mediaUri) {
        try {
          console.log('Caricamento media della storia...');

          // Utilizziamo il metodo uploadStoryMedia
          const uploadResult = await this.uploadStoryMedia(storyData.mediaUri, userId, mediaType);

          if (uploadResult.success) {
            mediaUrl = uploadResult.mediaUrl;
            console.log('Media caricato con successo:', mediaUrl);
          } else {
            console.warn('Errore nel caricamento del media:', uploadResult.error);
            // Continuiamo comunque con la creazione della storia
            // Usiamo l'URI locale come fallback
            mediaUrl = storyData.mediaUri;
          }
        } catch (mediaError) {
          console.error('Errore nel caricamento del media:', mediaError);
          // Continuiamo comunque con la creazione della storia
          mediaUrl = storyData.mediaUri;
        }
      }

      // Fase 2: Creazione della storia
      console.log('Invio richiesta per creare la storia:', this.baseUrl);

      // Prepara i dati per la creazione della storia
      const storyFormData = new FormData();
      storyFormData.append('content', storyData.content || '');
      storyFormData.append('mediaType', mediaType);

      // Se abbiamo un URL del media, lo aggiungiamo
      if (mediaUrl) {
        storyFormData.append('mediaUrl', mediaUrl);
      }

      // Aggiungi l'ID utente come creatore
      if (userId) {
        storyFormData.append('userId', userId);
      }

      // Invia la richiesta al server
      const response = await fetch(this.baseUrl, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'multipart/form-data',
        },
        body: storyFormData
      });

      // Log della risposta
      console.log('Risposta dal server - Status:', response.status);

      const responseText = await response.text();
      console.log('Risposta dal server (testo):', responseText.substring(0, 200) + (responseText.length > 200 ? '...' : ''));

      let data;
      try {
        data = JSON.parse(responseText);
      } catch (parseError) {
        console.error('Errore nel parsing della risposta JSON:', parseError);

        // Se la risposta non è JSON ma il server ha risposto con successo, creiamo una storia locale
        if (response.ok) {
          const localStory = {
            id: `story_${Date.now()}`,
            content: storyData.content || '',
            mediaUrl: mediaUrl,
            mediaType: mediaType,
            user: userId,
            createdAt: new Date().toISOString(),
            expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // Scade dopo 24 ore
            viewers: []
          };

          return {
            success: true,
            story: localStory,
            isLocal: true,
            message: 'Storia creata con successo (risposta non JSON)'
          };
        }

        throw new Error('Errore nel parsing della risposta JSON');
      }

      if (!response.ok) {
        throw new Error(data.error?.message || data.message || data.error || 'Errore nella creazione della storia');
      }

      // Crea l'oggetto storia con i dati ricevuti dal server
      const serverStory = data.story || data;

      // Assicurati che l'oggetto storia abbia tutti i campi necessari
      const story = {
        id: serverStory.id || serverStory._id || `story_${Date.now()}`,
        content: serverStory.content || storyData.content || '',
        mediaUrl: serverStory.mediaUrl || mediaUrl,
        mediaType: serverStory.mediaType || mediaType,
        user: serverStory.user || userId,
        createdAt: serverStory.createdAt || new Date().toISOString(),
        expiresAt: serverStory.expiresAt || new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
        viewers: serverStory.viewers || []
      };

      console.log('Storia creata con successo');

      return {
        success: true,
        story: story,
        isLocal: false // Indica che è una storia del server
      };
    } catch (error) {
      console.error('Errore nella creazione della storia:', error);

      // Ottieni l'ID utente dal servizio di autenticazione
      const authService = require('./authService').default;
      const currentUser = authService.getCurrentUser();
      const userId = currentUser?.id || currentUser?._id;

      // Anche in caso di errore generale, crea una storia locale
      const localStory = {
        id: `story_${Date.now()}`,
        content: storyData.content || '',
        mediaUrl: storyData.mediaUri, // Usa l'URI locale
        mediaType: storyData.mediaType || 'image',
        user: userId,
        createdAt: new Date().toISOString(),
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // Scade dopo 24 ore
        viewers: []
      };

      console.log('Creata storia locale (dopo errore)');

      return {
        success: true,
        story: localStory,
        isLocal: true, // Indica che è una storia locale
        error: error.message
      };
    }
  }
}

export default new StoryService();
