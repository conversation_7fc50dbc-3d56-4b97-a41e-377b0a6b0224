import AsyncStorage from '@react-native-async-storage/async-storage';
import { create } from 'zustand';
import useAuthStore from './authStore';
import communityService from '../services/communityService';

const useCommunityStore = create((set, get) => ({
  communities: [],
  currentCommunity: null,
  loading: false,
  error: null,

  // Carica le community dell'utente
  loadCommunities: async () => {
    try {
      set({ loading: true, error: null });

      // Ottieni l'utente corrente
      const { user } = useAuthStore.getState();
      if (!user) {
        console.warn('Nessun utente autenticato, impossibile caricare le community');
        set({ communities: [], loading: false });
        return;
      }

      // Prima tentiamo di caricare le community dal server
      try {
        const token = await communityService.getAuthToken();

        if (!token) {
          throw new Error('Token non disponibile');
        }

        // Timeout più lungo per il caricamento (30 secondi)
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 30000);

        // Se abbiamo un token, tentiamo di caricare le community dal server
        const response = await fetch(`${communityService.baseUrl}`, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
          signal: controller.signal
        });

        // Pulisci il timeout
        clearTimeout(timeoutId);

        if (response.ok) {
          const responseText = await response.text();

          try {
            const data = JSON.parse(responseText);

            // Estrai le community dalla risposta
            const serverCommunitiesArray = data.communities || data;

            if (Array.isArray(serverCommunitiesArray)) {
              // Formatta le community dal server
              const serverCommunities = serverCommunitiesArray.map(community => ({
                id: community.id || community._id,
                name: community.name,
                description: community.description || '',
                photoURL: community.photoURL,
                createdAt: community.createdAt || new Date().toISOString(),
                members: community.members || [],
                admins: community.admins || [],
                groups: community.groups || []
              }));

              console.log('Community caricate dal server:', serverCommunities.length);

              // Carica anche le community locali
              const communitiesData = await AsyncStorage.getItem('communities');
              let localCommunities = [];

              if (communitiesData) {
                try {
                  localCommunities = JSON.parse(communitiesData);
                  console.log('Community caricate da AsyncStorage:', localCommunities.length);

                  // Filtra le community locali che non sono presenti sul server
                  const serverIds = serverCommunities.map(c => c.id);
                  const uniqueLocalCommunities = localCommunities.filter(
                    local => !serverIds.includes(local.id) && local.id.startsWith('community_')
                  );

                  console.log('Community locali uniche:', uniqueLocalCommunities.length);

                  // Unisci le community del server con quelle locali uniche
                  const allCommunities = [...serverCommunities, ...uniqueLocalCommunities];

                  // Salva tutte le community in AsyncStorage
                  await AsyncStorage.setItem('communities', JSON.stringify(allCommunities));

                  // Aggiorna lo stato
                  set({ communities: allCommunities, loading: false });
                  return;
                } catch (parseError) {
                  console.error('Errore nel parsing delle community locali:', parseError);
                  // Se c'è un errore nel parsing, usa solo le community del server
                  await AsyncStorage.setItem('communities', JSON.stringify(serverCommunities));
                  set({ communities: serverCommunities, loading: false });
                  return;
                }
              } else {
                // Se non ci sono community locali, usa solo quelle del server
                await AsyncStorage.setItem('communities', JSON.stringify(serverCommunities));
                set({ communities: serverCommunities, loading: false });
                return;
              }
            } else {
              console.warn('La risposta del server non contiene un array di community:', data);
              // Continua con il caricamento da AsyncStorage
            }
          } catch (parseError) {
            console.error('Errore nel parsing della risposta JSON per le community:', parseError);
            // Continua con il caricamento da AsyncStorage
          }
        } else {
          // Continua con il caricamento da AsyncStorage
        }
      } catch (serverError) {
        // Continua con il caricamento da AsyncStorage
      }

      // Se il caricamento dal server fallisce, carica da AsyncStorage
      try {
        const communitiesData = await AsyncStorage.getItem('communities');
        if (communitiesData) {
          const communities = JSON.parse(communitiesData);
          set({ communities, loading: false });
        } else {
          // Se non ci sono community salvate, inizializza con un array vuoto
          await AsyncStorage.setItem('communities', JSON.stringify([]));
          set({ communities: [], loading: false });
        }
      } catch (storageError) {
        set({ communities: [], loading: false, error: storageError.message });
      }
    } catch (error) {
      console.error('Errore nel caricamento delle community:', error);
      set({ error: error.message, loading: false });
    }
  },

  // Carica una community specifica
  loadCommunity: async (communityId) => {
    try {
      set({ loading: true, error: null });

      if (!communityId) {
        console.error('ID community non valido');
        set({ error: 'ID community non valido', loading: false });
        return null;
      }

      console.log('Caricamento community con ID:', communityId);

      // Prima tentiamo di caricare la community dal server
      try {
        console.log('Tentativo di caricamento della community dal server...');
        const token = await communityService.getAuthToken();

        if (!token) {
          console.warn('Token di autenticazione non disponibile, caricamento solo da storage locale');
          throw new Error('Token non disponibile');
        }

        console.log('Token disponibile, invio richiesta al server...');

        // Timeout più lungo per il caricamento (30 secondi)
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 30000);

        // Se abbiamo un token, tentiamo di caricare la community dal server
        const response = await fetch(`${communityService.baseUrl}/${communityId}`, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
          signal: controller.signal
        });

        // Pulisci il timeout
        clearTimeout(timeoutId);

        console.log('Risposta dal server - Status:', response.status);
        console.log('Risposta dal server - Headers:', JSON.stringify([...response.headers.entries()]));

        if (response.ok) {
          const responseText = await response.text();
          console.log('Risposta dal server (testo):', responseText.substring(0, 200) + (responseText.length > 200 ? '...' : ''));

          try {
            const data = JSON.parse(responseText);
            console.log('Risposta dal server (JSON):', JSON.stringify(data).substring(0, 200) + (JSON.stringify(data).length > 200 ? '...' : ''));

            // Estrai la community dalla risposta
            const serverCommunity = data.community || data;

            if (serverCommunity && (serverCommunity.id || serverCommunity._id)) {
              // Formatta la community dal server
              const formattedCommunity = {
                id: serverCommunity.id || serverCommunity._id,
                name: serverCommunity.name,
                description: serverCommunity.description || '',
                photoURL: serverCommunity.photoURL,
                createdAt: serverCommunity.createdAt || new Date().toISOString(),
                members: serverCommunity.members || [],
                admins: serverCommunity.admins || [],
                groups: serverCommunity.groups || []
              };

              console.log('Community caricata dal server:', formattedCommunity.name);

              // Aggiorna la community in AsyncStorage
              try {
                const communitiesData = await AsyncStorage.getItem('communities');
                if (communitiesData) {
                  let communities = JSON.parse(communitiesData);
                  const index = communities.findIndex(c => c.id === communityId);

                  if (index !== -1) {
                    // Aggiorna la community esistente
                    communities[index] = formattedCommunity;
                    console.log('Community aggiornata in AsyncStorage');
                  } else {
                    // Aggiungi la nuova community
                    communities.push(formattedCommunity);
                    console.log('Community aggiunta in AsyncStorage');
                  }

                  await AsyncStorage.setItem('communities', JSON.stringify(communities));
                } else {
                  // Se non ci sono community salvate, crea un nuovo array
                  await AsyncStorage.setItem('communities', JSON.stringify([formattedCommunity]));
                  console.log('Creato nuovo array di community in AsyncStorage');
                }
              } catch (storageError) {
                console.error('Errore nell\'aggiornamento delle community in AsyncStorage:', storageError);
                // Continua comunque, l'importante è che abbiamo la community dal server
              }

              // Aggiorna lo stato
              set({ currentCommunity: formattedCommunity, loading: false });
              return formattedCommunity;
            } else {
              console.warn('La risposta del server non contiene una community valida:', data);
              // Continua con il caricamento da AsyncStorage
            }
          } catch (parseError) {
            console.error('Errore nel parsing della risposta JSON per la community:', parseError);
            // Continua con il caricamento da AsyncStorage
          }
        } else {
          // Continua con il caricamento da AsyncStorage
        }
      } catch (serverError) {
        console.error('Errore nel caricamento della community dal server:', serverError);

        // Se l'errore è un timeout, fornisci un messaggio più specifico
        if (serverError.name === 'AbortError') {
          console.warn('Timeout durante il caricamento della community dal server');
        }

        // Continua con il caricamento da AsyncStorage
      }

      // Se il caricamento dal server fallisce, carica da AsyncStorage
      console.log('Caricamento della community da AsyncStorage:', communityId);
      try {
        const communitiesData = await AsyncStorage.getItem('communities');
        if (!communitiesData) {
          console.warn('Nessuna community trovata in AsyncStorage');
          set({ error: 'Community non trovata', loading: false });
          return null;
        }

        const communities = JSON.parse(communitiesData);
        const community = communities.find(c => c.id === communityId);

        if (community) {
          console.log('Community caricata da AsyncStorage:', community.name);
          set({ currentCommunity: community, loading: false });
          return community;
        } else {
          console.warn('Community non trovata in AsyncStorage');
          set({ error: 'Community non trovata', loading: false });
          return null;
        }
      } catch (storageError) {
        console.error('Errore nel caricamento della community da AsyncStorage:', storageError);
        set({ error: storageError.message, loading: false });
        return null;
      }
    } catch (error) {
      console.error('Errore nel caricamento della community:', error);
      set({ error: error.message, loading: false });
      return null;
    }
  },

  // Crea una nuova community
  createCommunity: async (name, description, photoUri) => {
    const { user } = useAuthStore.getState();
    if (!user) return;

    try {
      set({ loading: true, error: null });

      // Utilizziamo il servizio per creare la community
      const result = await communityService.createCommunity({
        name,
        description,
        photoUri
      });

      if (!result.success) {
        throw new Error(result.error || 'Errore nella creazione della community');
      }

      // Se il server non ha restituito una community, creiamo un oggetto locale
      const communityData = result.community || {
        id: Date.now().toString(),
        name,
        description,
        photoURL: null, // Il server gestirà l'URL dell'immagine
        createdBy: user.id,
        createdAt: new Date(),
        members: [user.id],
        admins: [user.id],
        groups: [],
      };

      // Salva la community in locale
      const communitiesData = await AsyncStorage.getItem('communities');
      const communities = communitiesData ? JSON.parse(communitiesData) : [];
      communities.push(communityData);
      await AsyncStorage.setItem('communities', JSON.stringify(communities));

      set(state => ({
        communities: [communityData, ...state.communities],
        currentCommunity: communityData,
        loading: false,
      }));

      return communityData.id;
    } catch (error) {
      console.error('Errore nella creazione della community:', error);
      set({ error: error.message, loading: false });
      return null;
    }
  },

  // Aggiorna una community
  updateCommunity: async (communityId, updates) => {
    try {
      set({ loading: true, error: null });

      const communitiesData = await AsyncStorage.getItem('communities');
      const communities = communitiesData ? JSON.parse(communitiesData) : [];
      const index = communities.findIndex(c => c.id === communityId);

      if (index !== -1) {
        communities[index] = { ...communities[index], ...updates };
        await AsyncStorage.setItem('communities', JSON.stringify(communities));

        set(state => ({
          communities: state.communities.map(community =>
            community.id === communityId ? { ...community, ...updates } : community
          ),
          currentCommunity: state.currentCommunity?.id === communityId ?
            { ...state.currentCommunity, ...updates } : state.currentCommunity,
          loading: false,
        }));
      }
    } catch (error) {
      console.error('Errore nell\'aggiornamento della community:', error);
      set({ error: error.message, loading: false });
    }
  },

  // Elimina una community
  deleteCommunity: async (communityId) => {
    try {
      set({ loading: true, error: null });

      const communitiesData = await AsyncStorage.getItem('communities');
      const communities = communitiesData ? JSON.parse(communitiesData) : [];
      const filteredCommunities = communities.filter(c => c.id !== communityId);
      await AsyncStorage.setItem('communities', JSON.stringify(filteredCommunities));

      set(state => ({
        communities: state.communities.filter(community => community.id !== communityId),
        currentCommunity: state.currentCommunity?.id === communityId ? null : state.currentCommunity,
        loading: false,
      }));
    } catch (error) {
      console.error('Errore nell\'eliminazione della community:', error);
      set({ error: error.message, loading: false });
    }
  },

  // Aggiungi un gruppo alla community
  addGroupToCommunity: async (communityId, groupId, groupName, groupPhotoURL) => {
    try {
      set({ loading: true, error: null });

      const communitiesData = await AsyncStorage.getItem('communities');
      const communities = communitiesData ? JSON.parse(communitiesData) : [];
      const index = communities.findIndex(c => c.id === communityId);

      if (index !== -1) {
        const newGroup = {
          id: groupId,
          name: groupName,
          photoURL: groupPhotoURL,
          addedAt: new Date(),
        };

        communities[index].groups = [...(communities[index].groups || []), newGroup];
        await AsyncStorage.setItem('communities', JSON.stringify(communities));

        set(state => {
          const updatedCommunity = { ...state.currentCommunity };
          if (updatedCommunity) {
            updatedCommunity.groups = [...(updatedCommunity.groups || []), newGroup];
          }

          return {
            communities: state.communities.map(community =>
              community.id === communityId ? updatedCommunity : community
            ),
            currentCommunity: updatedCommunity,
            loading: false,
          };
        });
      }
    } catch (error) {
      console.error('Errore nell\'aggiunta del gruppo alla community:', error);
      set({ error: error.message, loading: false });
    }
  },

  // Rimuovi un gruppo dalla community
  removeGroupFromCommunity: async (communityId, groupId) => {
    try {
      set({ loading: true, error: null });

      const communitiesData = await AsyncStorage.getItem('communities');
      const communities = communitiesData ? JSON.parse(communitiesData) : [];
      const index = communities.findIndex(c => c.id === communityId);

      if (index !== -1) {
        const updatedCommunity = { ...communities[index] };
        updatedCommunity.groups = communities[index].groups.filter(g => g.id !== groupId);
        communities[index] = updatedCommunity;
        await AsyncStorage.setItem('communities', JSON.stringify(communities));

        set(state => ({
          communities: state.communities.map(community =>
            community.id === communityId ? updatedCommunity : community
          ),
          currentCommunity: state.currentCommunity?.id === communityId ? updatedCommunity : state.currentCommunity,
          loading: false,
        }));
      }
    } catch (error) {
      console.error('Errore nella rimozione del gruppo dalla community:', error);
      set({ error: error.message, loading: false });
    }
  },

  // Aggiungi un membro alla community
  addMemberToCommunity: async (communityId, userId) => {
    try {
      set({ loading: true, error: null });

      const communitiesData = await AsyncStorage.getItem('communities');
      const communities = communitiesData ? JSON.parse(communitiesData) : [];
      const index = communities.findIndex(c => c.id === communityId);

      if (index !== -1) {
        communities[index].members = [...communities[index].members, userId];
        await AsyncStorage.setItem('communities', JSON.stringify(communities));

        set(state => ({
          communities: state.communities.map(community =>
            community.id === communityId ? { ...community, members: [...community.members, userId] } : community
          ),
          currentCommunity: state.currentCommunity?.id === communityId ? { ...state.currentCommunity, members: [...state.currentCommunity.members, userId] } : state.currentCommunity,
          loading: false,
        }));
      }
    } catch (error) {
      console.error('Errore nell\'aggiunta del membro alla community:', error);
      set({ error: error.message, loading: false });
    }
  },

  // Rimuovi un membro dalla community
  removeMemberFromCommunity: async (communityId, userId) => {
    try {
      set({ loading: true, error: null });

      const communitiesData = await AsyncStorage.getItem('communities');
      const communities = communitiesData ? JSON.parse(communitiesData) : [];
      const index = communities.findIndex(c => c.id === communityId);

      if (index !== -1) {
        communities[index].members = communities[index].members.filter(m => m !== userId);
        communities[index].admins = communities[index].admins.filter(a => a !== userId);
        await AsyncStorage.setItem('communities', JSON.stringify(communities));

        set(state => ({
          communities: state.communities.map(community =>
            community.id === communityId ? { ...community, members: community.members.filter(m => m !== userId), admins: community.admins.filter(a => a !== userId) } : community
          ),
          currentCommunity: state.currentCommunity?.id === communityId ? { ...state.currentCommunity, members: state.currentCommunity.members.filter(m => m !== userId), admins: state.currentCommunity.admins.filter(a => a !== userId) } : state.currentCommunity,
          loading: false,
        }));
      }
    } catch (error) {
      console.error('Errore nella rimozione del membro dalla community:', error);
      set({ error: error.message, loading: false });
    }
  },

  // Promuovi un membro ad amministratore
  promoteMemberToAdmin: async (communityId, userId) => {
    try {
      set({ loading: true, error: null });

      const communitiesData = await AsyncStorage.getItem('communities');
      const communities = communitiesData ? JSON.parse(communitiesData) : [];
      const index = communities.findIndex(c => c.id === communityId);

      if (index !== -1) {
        communities[index].admins = [...communities[index].admins, userId];
        await AsyncStorage.setItem('communities', JSON.stringify(communities));

        set(state => ({
          communities: state.communities.map(community =>
            community.id === communityId ? { ...community, admins: [...community.admins, userId] } : community
          ),
          currentCommunity: state.currentCommunity?.id === communityId ? { ...state.currentCommunity, admins: [...state.currentCommunity.admins, userId] } : state.currentCommunity,
          loading: false,
        }));
      }
    } catch (error) {
      console.error('Errore nella promozione del membro ad amministratore:', error);
      set({ error: error.message, loading: false });
    }
  },

  // Rimuovi un amministratore (ma mantienilo come membro)
  demoteAdminToMember: async (communityId, userId) => {
    try {
      set({ loading: true, error: null });

      const communitiesData = await AsyncStorage.getItem('communities');
      const communities = communitiesData ? JSON.parse(communitiesData) : [];
      const index = communities.findIndex(c => c.id === communityId);

      if (index !== -1) {
        communities[index].admins = communities[index].admins.filter(a => a !== userId);
        await AsyncStorage.setItem('communities', JSON.stringify(communities));

        set(state => ({
          communities: state.communities.map(community =>
            community.id === communityId ? { ...community, admins: community.admins.filter(a => a !== userId) } : community
          ),
          currentCommunity: state.currentCommunity?.id === communityId ? { ...state.currentCommunity, admins: state.currentCommunity.admins.filter(a => a !== userId) } : state.currentCommunity,
          loading: false,
        }));
      }
    } catch (error) {
      console.error('Errore nella retrocessione dell\'amministratore a membro:', error);
      set({ error: error.message, loading: false });
    }
  },

  // Resetta lo stato
  resetState: () => {
    set({
      communities: [],
      currentCommunity: null,
      loading: false,
      error: null,
    });
  },
}));

export default useCommunityStore;

// Esporto le variabili vuote per mantenere la compatibilità
export const db = null;
export const storage = null;
