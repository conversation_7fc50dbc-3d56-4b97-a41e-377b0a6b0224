// Utility per ottimizzare le richieste di rete
import NetInfo from '@react-native-community/netinfo';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Costanti per la gestione della rete
const NETWORK_TIMEOUT = 10000; // 10 secondi
const RETRY_DELAY = 1000; // 1 secondo
const MAX_RETRIES = 3;
const NETWORK_CACHE_PREFIX = '@trendychat:network_cache_';
const NETWORK_QUEUE_KEY = '@trendychat:network_queue';

// Stato della connessione
let isConnected = true;
let connectionType = 'unknown';
let isConnectionExpensive = false;

// Inizializza il monitoraggio della rete
export const initNetworkMonitoring = () => {
  // Configura il listener per i cambiamenti di rete
  const unsubscribe = NetInfo.addEventListener(state => {
    isConnected = state.isConnected;
    connectionType = state.type;
    isConnectionExpensive = state.details?.isConnectionExpensive || false;
    
    // Se la connessione è stata ripristinata, tenta di inviare le richieste in coda
    if (isConnected) {
      processPendingRequests();
    }
  });
  
  // Esegui un controllo iniziale
  NetInfo.fetch().then(state => {
    isConnected = state.isConnected;
    connectionType = state.type;
    isConnectionExpensive = state.details?.isConnectionExpensive || false;
  });
  
  return unsubscribe;
};

/**
 * Esegue una richiesta di rete ottimizzata con cache e retry
 * @param {string} url - URL della richiesta
 * @param {Object} options - Opzioni della richiesta
 * @param {Object} cacheOptions - Opzioni della cache
 * @returns {Promise<Object>} - Risposta della richiesta
 */
export const optimizedFetch = async (url, options = {}, cacheOptions = {}) => {
  const {
    useCache = true,
    cacheTTL = 5 * 60 * 1000, // 5 minuti
    forceRefresh = false,
    offlineMode = true,
    retries = MAX_RETRIES
  } = cacheOptions;
  
  // Genera una chiave di cache basata sull'URL e sul metodo
  const method = options.method || 'GET';
  const cacheKey = `${NETWORK_CACHE_PREFIX}${method}_${url}`;
  
  // Se non siamo connessi e la modalità offline è abilitata, usa la cache
  if (!isConnected && offlineMode) {
    console.log('Dispositivo offline, tentativo di usare la cache');
    const cachedResponse = await getCachedResponse(cacheKey);
    
    if (cachedResponse) {
      return cachedResponse;
    }
    
    // Se non c'è una risposta in cache, aggiungi la richiesta alla coda
    if (method === 'GET') {
      await addRequestToQueue(url, options);
    }
    
    throw new Error('Dispositivo offline e nessuna risposta in cache');
  }
  
  // Se useCache è true e non è richiesto un refresh, prova a usare la cache
  if (useCache && !forceRefresh && method === 'GET') {
    const cachedResponse = await getCachedResponse(cacheKey);
    
    if (cachedResponse) {
      // Se la cache è valida, restituiscila
      if (Date.now() - cachedResponse.timestamp < cacheTTL) {
        console.log('Usando risposta dalla cache');
        
        // Aggiorna la cache in background se la connessione è buona
        if (isConnected && !isConnectionExpensive) {
          refreshCacheInBackground(url, options, cacheKey);
        }
        
        return cachedResponse.data;
      }
    }
  }
  
  // Esegui la richiesta con retry
  return fetchWithRetry(url, options, retries, cacheKey, useCache);
};

/**
 * Esegue una richiesta con retry automatico
 */
const fetchWithRetry = async (url, options, retriesLeft, cacheKey, useCache) => {
  try {
    // Aggiungi un timeout alla richiesta
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), NETWORK_TIMEOUT);
    
    const response = await fetch(url, {
      ...options,
      signal: controller.signal
    });
    
    clearTimeout(timeoutId);
    
    // Se la risposta non è ok, lancia un errore
    if (!response.ok) {
      throw new Error(`Errore nella richiesta: ${response.status} ${response.statusText}`);
    }
    
    // Clona la risposta per poterla usare più volte
    const clonedResponse = response.clone();
    
    // Converti la risposta in JSON
    const data = await response.json();
    
    // Se useCache è true, salva la risposta in cache
    if (useCache) {
      await cacheResponse(cacheKey, data);
    }
    
    return data;
  } catch (error) {
    // Se ci sono ancora tentativi disponibili, riprova
    if (retriesLeft > 0) {
      console.log(`Riprovo la richiesta, tentativi rimanenti: ${retriesLeft}`);
      
      // Attendi prima di riprovare
      await new Promise(resolve => setTimeout(resolve, RETRY_DELAY));
      
      return fetchWithRetry(url, options, retriesLeft - 1, cacheKey, useCache);
    }
    
    // Se non ci sono più tentativi, lancia l'errore
    throw error;
  }
};

/**
 * Ottiene una risposta dalla cache
 */
const getCachedResponse = async (cacheKey) => {
  try {
    const cachedData = await AsyncStorage.getItem(cacheKey);
    
    if (cachedData) {
      return JSON.parse(cachedData);
    }
    
    return null;
  } catch (error) {
    console.error('Errore nel recupero della risposta dalla cache:', error);
    return null;
  }
};

/**
 * Salva una risposta in cache
 */
const cacheResponse = async (cacheKey, data) => {
  try {
    const cacheData = {
      data,
      timestamp: Date.now()
    };
    
    await AsyncStorage.setItem(cacheKey, JSON.stringify(cacheData));
  } catch (error) {
    console.error('Errore nel salvataggio della risposta in cache:', error);
  }
};

/**
 * Aggiorna la cache in background
 */
const refreshCacheInBackground = async (url, options, cacheKey) => {
  try {
    const response = await fetch(url, options);
    
    if (response.ok) {
      const data = await response.json();
      await cacheResponse(cacheKey, data);
      console.log('Cache aggiornata in background');
    }
  } catch (error) {
    console.error('Errore nell\'aggiornamento della cache in background:', error);
  }
};

/**
 * Aggiunge una richiesta alla coda
 */
const addRequestToQueue = async (url, options) => {
  try {
    // Ottieni la coda attuale
    const queueStr = await AsyncStorage.getItem(NETWORK_QUEUE_KEY);
    let queue = [];
    
    if (queueStr) {
      queue = JSON.parse(queueStr);
    }
    
    // Aggiungi la richiesta alla coda
    queue.push({
      url,
      options,
      timestamp: Date.now()
    });
    
    // Salva la coda aggiornata
    await AsyncStorage.setItem(NETWORK_QUEUE_KEY, JSON.stringify(queue));
  } catch (error) {
    console.error('Errore nell\'aggiunta della richiesta alla coda:', error);
  }
};

/**
 * Elabora le richieste in coda
 */
const processPendingRequests = async () => {
  try {
    // Ottieni la coda attuale
    const queueStr = await AsyncStorage.getItem(NETWORK_QUEUE_KEY);
    
    if (!queueStr) {
      return;
    }
    
    const queue = JSON.parse(queueStr);
    
    if (queue.length === 0) {
      return;
    }
    
    console.log(`Elaborazione di ${queue.length} richieste in coda`);
    
    // Elabora le richieste in coda
    const newQueue = [];
    
    for (const request of queue) {
      try {
        // Esegui la richiesta
        await fetch(request.url, request.options);
      } catch (error) {
        // Se la richiesta fallisce, mantienila in coda
        newQueue.push(request);
      }
    }
    
    // Salva la coda aggiornata
    if (newQueue.length > 0) {
      await AsyncStorage.setItem(NETWORK_QUEUE_KEY, JSON.stringify(newQueue));
    } else {
      await AsyncStorage.removeItem(NETWORK_QUEUE_KEY);
    }
  } catch (error) {
    console.error('Errore nell\'elaborazione delle richieste in coda:', error);
  }
};

export default {
  initNetworkMonitoring,
  optimizedFetch,
  getNetworkState: () => ({
    isConnected,
    connectionType,
    isConnectionExpensive
  })
};
