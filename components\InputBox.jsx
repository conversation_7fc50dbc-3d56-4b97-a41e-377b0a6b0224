import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Platform,
  Keyboard,
  Animated,
  Text,
  Pressable,
  ActivityIndicator,
  AppState
} from 'react-native';
// Implementazione personalizzata di debounce
import { Ionicons, MaterialCommunityIcons } from '@expo/vector-icons';
import * as ImagePicker from 'expo-image-picker';
import * as DocumentPicker from 'expo-document-picker';
import { Audio } from 'expo-av';
import { Camera } from 'expo-camera';
import { LinearGradient } from 'expo-linear-gradient';
import LottieView from 'lottie-react-native';
// Importa EmojiPicker in modo condizionale
let EmojiPicker = () => null;
try {
  EmojiPicker = require('rn-emoji-keyboard').default;
} catch (error) {
  console.log('Errore nel caricamento di rn-emoji-keyboard:', error.message);
}
import { theme } from '../theme';
import ReplyComposer from './chat/ReplyComposer';
import RecordingManager from '../utils/RecordingManager';

const InputBox = ({
  onSend,
  onAttachment,
  onVoiceMessage,
  placeholder = 'Messaggio...',
  replyTo = null,
  onCancelReply = () => {},
  chatId = null,
  userId = null,
  onTypingStatusChange = () => {}
}) => {
  const [message, setMessage] = useState('');
  const [isRecording, setIsRecording] = useState(false);
  const [showEmoji, setShowEmoji] = useState(false);
  const [recording, setRecording] = useState(null);
  const [isTyping, setIsTyping] = useState(false);
  // Rimuoviamo l'animazione per evitare conflitti
  const micScale = useRef(new Animated.Value(1)).current;
  const typingTimeout = useRef(null);

  // Funzione di debounce personalizzata per lo stato di digitazione
  const debouncedTypingEnd = () => {
    if (typingTimeout.current) {
      clearTimeout(typingTimeout.current);
    }

    typingTimeout.current = setTimeout(() => {
      if (isTyping) {
        setIsTyping(false);
        if (chatId && userId) {
          onTypingStatusChange(chatId, userId, false);
        }
      }
    }, 2000); // 2 secondi di inattività per considerare che l'utente ha smesso di digitare
  };

  const handleSend = () => {
    if (message.trim()) {
      onSend(message, replyTo);
      setMessage('');
      if (replyTo) {
        onCancelReply();
      }
      Keyboard.dismiss();
    }
  };

  const handleAttachment = () => {
    // Apri menu allegati stile TrendyChat
    if (onAttachment) {
      onAttachment();
    }
  };

  // WHATSAPP: TIENI PREMUTO → INIZIA REGISTRAZIONE
  const startRecording = async () => {
    try {
      console.log('🎤 WHATSAPP: Tieni premuto - Iniziando registrazione');
      await RecordingManager.startRecording();
      setIsRecording(true);
      micScale.setValue(1.2); // Feedback visivo
    } catch (error) {
      console.error('❌ WHATSAPP: Errore avvio:', error);
      setIsRecording(false);
    }
  };

  // WHATSAPP: RILASCIA → FERMA E INVIA
  const stopRecording = async () => {
    try {
      console.log('🛑 WHATSAPP: Rilasciato - Fermando e inviando');
      const uri = await RecordingManager.stopRecording();
      setIsRecording(false);
      micScale.setValue(1);

      if (uri) {
        console.log('✅ WHATSAPP: URI ricevuto, invio messaggio vocale');
        onVoiceMessage(uri);
      } else {
        console.log('⚠️ WHATSAPP: Nessun URI, registrazione fallita');
      }
    } catch (error) {
      console.error('❌ WHATSAPP: Errore stop:', error);
      setIsRecording(false);
      micScale.setValue(1);
    }
  };

  const onEmojiSelected = (emoji) => {
    console.log('😊 Emoji selezionata:', emoji);
    // L'emoji può essere una stringa o un oggetto con proprietà .emoji o .native
    const emojiString = typeof emoji === 'string' ? emoji : (emoji.emoji || emoji.native || emoji);
    console.log('😊 Emoji string:', emojiString);
    setMessage((prev) => prev + emojiString);
  };

  // Effetto per gestire lo stato di digitazione
  useEffect(() => {
    try {
      if (message.trim() && !isTyping && chatId && userId) {
        setIsTyping(true);
        onTypingStatusChange(chatId, userId, true);
      }

      if (message.trim()) {
        debouncedTypingEnd();
      } else if (isTyping) {
        setIsTyping(false);
        if (chatId && userId) {
          onTypingStatusChange(chatId, userId, false);
        }
      }
    } catch (error) {
      console.error('Errore nella gestione dello stato di digitazione:', error);
    }

    return () => {
      if (typingTimeout.current) {
        clearTimeout(typingTimeout.current);
      }
    };
  }, [message, chatId, userId]);

  // WHATSAPP: NESSUN LISTENER COMPLESSO - SOLO SEMPLICE

  // Effetto per pulire il timeout quando il componente viene smontato
  useEffect(() => {
    return () => {
      if (typingTimeout.current) {
        clearTimeout(typingTimeout.current);
      }

      try {
        // Assicurati che lo stato di digitazione sia impostato su false quando il componente viene smontato
        if (isTyping && chatId && userId) {
          onTypingStatusChange(chatId, userId, false);
        }
      } catch (error) {
        console.error('Errore nella pulizia dello stato di digitazione:', error);
      }
    };
  }, []);

  return (
    <LinearGradient
      colors={['#121212', '#1A1A1A']}
      style={styles.container}
    >
      {replyTo && (
        <ReplyComposer
          replyTo={replyTo}
          onCancel={onCancelReply}
        />
      )}
      <View style={styles.inputContainer}>
        <TouchableOpacity
          style={styles.emojiButton}
          onPress={() => setShowEmoji(true)}
        >
          <Ionicons name="happy-outline" size={22} color={theme.colors.textSecondary} />
        </TouchableOpacity>

        <TextInput
          style={styles.input}
          value={message}
          onChangeText={setMessage}
          placeholder={placeholder}
          placeholderTextColor={theme.colors.textSecondary}
          multiline
          maxLength={1000}
        />

        <TouchableOpacity
          style={styles.attachmentButton}
          onPress={handleAttachment}
        >
          <Ionicons name="attach-outline" size={22} color={theme.colors.textSecondary} />
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.cameraButton}
          onPress={() => onAttachment({ type: 'camera' })}
        >
          <Ionicons name="camera-outline" size={22} color={theme.colors.textSecondary} />
        </TouchableOpacity>
      </View>

      {message.trim() ? (
        <TouchableOpacity style={styles.sendButton} onPress={handleSend}>
          <LinearGradient
            colors={['#1E88E5', '#D81B60']}
            style={styles.sendButtonGradient}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 0 }}
          >
            <Ionicons name="send" size={20} color={theme.colors.textLight} />
          </LinearGradient>
        </TouchableOpacity>
      ) : (
        <TouchableOpacity
          style={styles.micButton}
          onPressIn={startRecording}
          onPressOut={stopRecording}
        >
          <LinearGradient
            colors={isRecording ? ['#D81B60', '#8E24AA'] : ['#1E88E5', '#D81B60']}
            style={styles.micButtonGradient}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 0 }}
          >
            <View style={styles.micContainer}>
              {isRecording ? (
                <LottieView
                  source={require('../assets/lottie/recording.json')}
                  autoPlay
                  loop
                  style={styles.micAnimation}
                />
              ) : (
                <Ionicons
                  name="mic"
                  size={20}
                  color={theme.colors.textLight}
                />
              )}
            </View>
          </LinearGradient>
        </TouchableOpacity>
      )}

      {typeof EmojiPicker === 'function' && (
        <EmojiPicker
          onEmojiSelected={onEmojiSelected}
          open={showEmoji}
          onClose={() => setShowEmoji(false)}
          theme={theme.colors.background}
        />
      )}
    </LinearGradient>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: theme.spacing.sm,
    paddingVertical: theme.spacing.md,
    backgroundColor: 'transparent',
    zIndex: 10, // Assicura che sia sopra altri elementi
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: -3 },
        shadowOpacity: 0.2,
        shadowRadius: 5,
      },
      android: {
        elevation: 8,
      },
    }),
  },
  inputContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.xl,
    paddingHorizontal: theme.spacing.sm,
    marginRight: theme.spacing.sm,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.1)',
    ...theme.shadows.small,
  },
  emojiButton: {
    padding: theme.spacing.sm,
  },
  input: {
    flex: 1,
    padding: theme.spacing.sm,
    maxHeight: 100,
    minHeight: 40,
    fontSize: 16,
    color: theme.colors.text,
    fontWeight: '400',
  },
  attachmentButton: {
    padding: theme.spacing.sm,
  },
  cameraButton: {
    padding: theme.spacing.sm,
  },
  sendButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  sendButtonGradient: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.1)',
    ...theme.shadows.small,
  },
  sendAnimation: {
    width: 24,
    height: 24,
  },
  micButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  micButtonGradient: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.1)',
    ...theme.shadows.small,
  },
  micContainer: {
    width: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  micAnimation: {
    width: 24,
    height: 24,
  }
});

export default InputBox;