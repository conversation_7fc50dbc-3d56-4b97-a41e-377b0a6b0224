import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  Dimensions,
  ActivityIndicator,
  StatusBar,
  Modal,
  SafeAreaView,
  Share,
} from 'react-native';
import { useRoute, useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { Video } from 'expo-av';
import * as FileSystem from 'expo-file-system';
import * as MediaLibrary from 'expo-media-library';
import * as Sharing from 'expo-sharing';
import { theme } from '../theme';

const { width, height } = Dimensions.get('window');

const MediaViewerScreen = () => {
  const route = useRoute();
  const navigation = useNavigation();
  const { mediaUri, thumbnailUri, mediaType } = route.params;

  const [isPlaying, setIsPlaying] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [showControls, setShowControls] = useState(true);
  const [downloading, setDownloading] = useState(false);

  const videoRef = useRef(null);

  const handleBack = () => {
    if (videoRef.current) {
      videoRef.current.pauseAsync();
    }
    navigation.goBack();
  };

  const toggleControls = () => {
    setShowControls(!showControls);
  };

  const handlePlayPause = async () => {
    if (!videoRef.current) return;

    if (isPlaying) {
      await videoRef.current.pauseAsync();
    } else {
      await videoRef.current.playAsync();
    }

    setIsPlaying(!isPlaying);
  };

  const handleShare = async () => {
    try {
      if (mediaType === 'image') {
        await Share.share({
          url: mediaUri,
          message: 'Condividi immagine',
        });
      } else if (mediaType === 'video') {
        await Share.share({
          url: mediaUri,
          message: 'Condividi video',
        });
      }
    } catch (error) {
      console.error('Errore nella condivisione:', error);
    }
  };

  const handleDownload = async () => {
    try {
      setDownloading(true);

      // Condividi il file invece di salvarlo nella galleria
      if (await Sharing.isAvailableAsync()) {
        await Sharing.shareAsync(mediaUri, {
          mimeType: mediaType === 'image' ? 'image/jpeg' : 'video/mp4',
          dialogTitle: 'Condividi media'
        });
        alert('Media condiviso');
      } else {
        alert('Condivisione non disponibile su questo dispositivo');
      }
    } catch (error) {
      console.error('Errore nella condivisione del media:', error);
      alert('Impossibile condividere il media');
    } finally {
      setDownloading(false);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar hidden />

      <TouchableOpacity
        style={styles.fullScreenTouchable}
        activeOpacity={1}
        onPress={toggleControls}
      >
        {mediaType === 'image' ? (
          <Image
            source={{ uri: mediaUri }}
            style={styles.media}
            resizeMode="contain"
            onLoadStart={() => setIsLoading(true)}
            onLoadEnd={() => setIsLoading(false)}
          />
        ) : (
          <Video
            ref={videoRef}
            source={{ uri: mediaUri }}
            style={styles.media}
            resizeMode="contain"
            shouldPlay={true}
            isLooping={true}
            onPlaybackStatusUpdate={(status) => {
              setIsPlaying(status.isPlaying);
              setIsLoading(status.isLoading);
            }}
          />
        )}

        {isLoading && (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#FFFFFF" />
          </View>
        )}
      </TouchableOpacity>

      {showControls && (
        <>
          <View style={styles.header}>
            <TouchableOpacity
              style={styles.backButton}
              onPress={handleBack}
            >
              <Ionicons name="arrow-back" size={24} color="#FFFFFF" />
            </TouchableOpacity>
          </View>

          <View style={styles.footer}>
            {mediaType === 'video' && (
              <TouchableOpacity
                style={styles.controlButton}
                onPress={handlePlayPause}
              >
                <Ionicons
                  name={isPlaying ? 'pause' : 'play'}
                  size={24}
                  color="#FFFFFF"
                />
              </TouchableOpacity>
            )}

            <TouchableOpacity
              style={styles.controlButton}
              onPress={handleShare}
            >
              <Ionicons name="share-outline" size={24} color="#FFFFFF" />
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.controlButton}
              onPress={handleDownload}
              disabled={downloading}
            >
              {downloading ? (
                <ActivityIndicator size="small" color="#FFFFFF" />
              ) : (
                <Ionicons name="download-outline" size={24} color="#FFFFFF" />
              )}
            </TouchableOpacity>
          </View>
        </>
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000000',
  },
  fullScreenTouchable: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  media: {
    width,
    height: height,
  },
  loadingContainer: {
    ...StyleSheet.absoluteFillObject,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  header: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  footer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'space-around',
    padding: 16,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  controlButton: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default MediaViewerScreen;
