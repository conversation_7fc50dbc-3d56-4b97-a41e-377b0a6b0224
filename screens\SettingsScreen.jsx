import React, { useState, useEffect } from 'react';

import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Switch,
  Image,
  ActivityIndicator,
  Alert,
  SafeAreaView,
  StatusBar,
  Platform,
  Dimensions,
  Clipboard,
  Share,
} from 'react-native';
import * as Sharing from 'expo-sharing';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import useSettingsStore from '../store/settingsStore';
import useMediaStore from '../store/mediaStore';
import * as ImagePicker from 'expo-image-picker';
import useAuthStore from '../store/authStore';
import UserAvatar from '../components/UserAvatar';

const { width, height } = Dimensions.get('window');

const SettingsScreen = () => {
  const navigation = useNavigation();
  const { settings, loading, error, loadSettings, updateSettings } = useSettingsStore();
  const { user, updateUserProfile } = useAuthStore();
  const { pickImage } = useMediaStore();
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    loadSettings();
  }, []);

  const handleProfilePress = () => {
    navigation.navigate('Profile');
  };

  const handleUpdatePhoto = async () => {
    try {
      const options = [
        { text: 'Scatta foto', onPress: () => handleTakePhoto() },
        { text: 'Scegli dalla galleria', onPress: () => handlePickPhoto() },
        { text: 'Annulla', style: 'cancel' },
      ];

      Alert.alert('Aggiorna foto profilo', 'Scegli un\'opzione', options);
    } catch (error) {
      Alert.alert('Errore', 'Impossibile aggiornare la foto profilo');
    }
  };

  const handleTakePhoto = async () => {
    try {
      setIsLoading(true);
      const { status } = await ImagePicker.requestCameraPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permesso negato', 'È necessario concedere l\'accesso alla fotocamera');
        setIsLoading(false);
        return;
      }

      const result = await ImagePicker.launchCameraAsync({
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.8,
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
      });

      if (!result.canceled) {
        console.log('Foto scattata:', result.assets[0].uri);
        try {
          await updateUserProfile({
            avatar: result.assets[0].uri,
            photoURL: result.assets[0].uri // Manteniamo anche photoURL per compatibilità
          });
          Alert.alert('Successo', 'Foto profilo aggiornata con successo');
        } catch (updateError) {
          console.error('Errore durante l\'aggiornamento del profilo:', updateError);
          if (updateError.message.includes('non autenticato') || updateError.message.includes('scaduta')) {
            Alert.alert('Sessione scaduta', 'La tua sessione è scaduta. Effettua nuovamente il login.');
          } else {
            Alert.alert('Errore', 'Impossibile aggiornare la foto profilo: ' + updateError.message);
          }
        }
      }
    } catch (error) {
      console.error('Errore durante l\'acquisizione della foto:', error);
      Alert.alert('Errore', 'Impossibile scattare la foto');
    } finally {
      setIsLoading(false);
    }
  };

  const handlePickPhoto = async () => {
    try {
      setIsLoading(true);
      const imageUri = await pickImage();
      if (imageUri) {
        console.log('Immagine selezionata:', imageUri);
        try {
          await updateUserProfile({
            avatar: imageUri,
            photoURL: imageUri // Manteniamo anche photoURL per compatibilità
          });
          Alert.alert('Successo', 'Foto profilo aggiornata con successo');
        } catch (updateError) {
          console.error('Errore durante l\'aggiornamento del profilo:', updateError);
          if (updateError.message.includes('non autenticato') || updateError.message.includes('scaduta')) {
            Alert.alert('Sessione scaduta', 'La tua sessione è scaduta. Effettua nuovamente il login.');
          } else {
            Alert.alert('Errore', 'Impossibile aggiornare la foto profilo: ' + updateError.message);
          }
        }
      }
    } catch (error) {
      console.error('Errore durante la selezione dell\'immagine:', error);
      Alert.alert('Errore', 'Impossibile selezionare l\'immagine');
    } finally {
      setIsLoading(false);
    }
  };

  const handleQRCodePress = () => {
    navigation.navigate('QRCode');
  };



  const handleInvitePress = async () => {
    try {
      const message = 'Ciao! Ti invito a provare TrendyChat, un\'app di messaggistica moderna e sicura. Scaricala qui: https://trendychat.app';

      // Utilizziamo direttamente l'API Share di React Native
      const shareOptions = {
        title: 'Invita un amico a TrendyChat',
        message: message,
        // Possiamo aggiungere un'immagine o un URL se necessario
        // url: 'https://trendychat.app/download',
      };

      const result = await Share.share(shareOptions);

      if (result.action === Share.sharedAction) {
        if (result.activityType) {
          // Condiviso con attività specifica (solo iOS)
          console.log(`Condiviso con: ${result.activityType}`);
        } else {
          // Condiviso
          console.log('Condiviso con successo');
        }
      } else if (result.action === Share.dismissedAction) {
        // Condivisione annullata
        console.log('Condivisione annullata');
      }
    } catch (error) {
      console.error('Errore durante la condivisione:', error);
      Alert.alert('Errore', 'Si è verificato un errore durante la condivisione.');
    }
  };

  return (
    <>
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle="light-content" backgroundColor="transparent" translucent />

        <LinearGradient
          colors={['#1E88E5', '#D81B60']}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 0 }}
          style={styles.header}
        >
          <Text style={styles.headerTitle}>Impostazioni</Text>
        </LinearGradient>

        {loading || isLoading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#1E88E5" />
          </View>
        ) : (
          <ScrollView style={styles.scrollContainer}>
            {/* Profilo utente */}
            <View style={styles.profileContainer}>
              <TouchableOpacity style={styles.profileAvatarContainer} onPress={handleUpdatePhoto}>
                <UserAvatar
                  uri={user?.avatar || user?.photoURL || null}
                  size={70}
                  isOnline={false}
                />
                <View style={styles.editPhotoButton}>
                  <Ionicons name="camera" size={16} color="#FFFFFF" />
                </View>
              </TouchableOpacity>
              <TouchableOpacity style={styles.profileInfo} onPress={handleProfilePress}>
                <Text style={styles.profileName}>{user?.displayName || 'Utente TrendyChat'}</Text>
                <Text style={styles.profileStatus}>{user?.status || 'Disponibile'}</Text>
              </TouchableOpacity>
              <TouchableOpacity onPress={handleProfilePress}>
                <Ionicons name="chevron-forward" size={24} color="#AAAAAA" />
              </TouchableOpacity>
            </View>

            {/* Sezione principale */}
            <View style={styles.section}>
              <TouchableOpacity
                style={styles.settingItem}
                onPress={() => navigation.navigate('AccountSettings')}
              >
                <View style={styles.settingIconContainer}>
                  <LinearGradient
                    colors={['#1E88E5', '#D81B60']}
                    start={{ x: 0, y: 0 }}
                    end={{ x: 1, y: 0 }}
                    style={styles.iconGradient}
                  >
                    <Ionicons name="key-outline" size={22} color="#FFFFFF" />
                  </LinearGradient>
                </View>
                <View style={styles.settingContent}>
                  <Text style={styles.settingLabel}>Account</Text>
                  <Text style={styles.settingDescription}>Privacy, sicurezza, cambia numero</Text>
                </View>
                <Ionicons name="chevron-forward" size={20} color="#AAAAAA" />
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.settingItem}
                onPress={() => navigation.navigate('ChatSettings')}
              >
                <View style={styles.settingIconContainer}>
                  <LinearGradient
                    colors={['#1E88E5', '#D81B60']}
                    start={{ x: 0, y: 0 }}
                    end={{ x: 1, y: 0 }}
                    style={styles.iconGradient}
                  >
                    <Ionicons name="chatbubble-outline" size={22} color="#FFFFFF" />
                  </LinearGradient>
                </View>
                <View style={styles.settingContent}>
                  <Text style={styles.settingLabel}>Chat</Text>
                  <Text style={styles.settingDescription}>Tema, sfondi, cronologia chat</Text>
                </View>
                <Ionicons name="chevron-forward" size={20} color="#AAAAAA" />
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.settingItem}
                onPress={() => navigation.navigate('NotificationSettings')}
              >
                <View style={styles.settingIconContainer}>
                  <LinearGradient
                    colors={['#1E88E5', '#D81B60']}
                    start={{ x: 0, y: 0 }}
                    end={{ x: 1, y: 0 }}
                    style={styles.iconGradient}
                  >
                    <Ionicons name="notifications-outline" size={22} color="#FFFFFF" />
                  </LinearGradient>
                </View>
                <View style={styles.settingContent}>
                  <Text style={styles.settingLabel}>Notifiche</Text>
                  <Text style={styles.settingDescription}>Messaggi, gruppi, chiamate</Text>
                </View>
                <Ionicons name="chevron-forward" size={20} color="#AAAAAA" />
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.settingItem}
                onPress={() => navigation.navigate('StorageSettings')}
              >
                <View style={styles.settingIconContainer}>
                  <LinearGradient
                    colors={['#1E88E5', '#D81B60']}
                    start={{ x: 0, y: 0 }}
                    end={{ x: 1, y: 0 }}
                    style={styles.iconGradient}
                  >
                    <Ionicons name="server-outline" size={22} color="#FFFFFF" />
                  </LinearGradient>
                </View>
                <View style={styles.settingContent}>
                  <Text style={styles.settingLabel}>Archiviazione e dati</Text>
                  <Text style={styles.settingDescription}>Utilizzo rete, download automatico</Text>
                </View>
                <Ionicons name="chevron-forward" size={20} color="#AAAAAA" />
              </TouchableOpacity>
            </View>

            {/* Sezione strumenti */}
            <View style={styles.section}>
              <TouchableOpacity
                style={styles.settingItem}
                onPress={() => navigation.navigate('Privacy')}
              >
                <View style={styles.settingIconContainer}>
                  <LinearGradient
                    colors={['#1E88E5', '#D81B60']}
                    start={{ x: 0, y: 0 }}
                    end={{ x: 1, y: 0 }}
                    style={styles.iconGradient}
                  >
                    <Ionicons name="lock-closed-outline" size={22} color="#FFFFFF" />
                  </LinearGradient>
                </View>
                <View style={styles.settingContent}>
                  <Text style={styles.settingLabel}>Privacy</Text>
                  <Text style={styles.settingDescription}>Blocca contatti, messaggi che scompaiono</Text>
                </View>
                <Ionicons name="chevron-forward" size={20} color="#AAAAAA" />
              </TouchableOpacity>



              <TouchableOpacity
                style={styles.settingItem}
                onPress={handleQRCodePress}
              >
                <View style={styles.settingIconContainer}>
                  <LinearGradient
                    colors={['#1E88E5', '#D81B60']}
                    start={{ x: 0, y: 0 }}
                    end={{ x: 1, y: 0 }}
                    style={styles.iconGradient}
                  >
                    <Ionicons name="qr-code-outline" size={22} color="#FFFFFF" />
                  </LinearGradient>
                </View>
                <View style={styles.settingContent}>
                  <Text style={styles.settingLabel}>Codice QR</Text>
                  <Text style={styles.settingDescription}>Scansiona codici QR per aggiungere contatti</Text>
                </View>
                <Ionicons name="chevron-forward" size={20} color="#AAAAAA" />
              </TouchableOpacity>
            </View>

            {/* Sezione lingua */}
            <View style={styles.section}>
              <TouchableOpacity
                style={styles.settingItem}
                onPress={() => navigation.navigate('LanguageSettings')}
              >
                <View style={styles.settingIconContainer}>
                  <LinearGradient
                    colors={['#1E88E5', '#D81B60']}
                    start={{ x: 0, y: 0 }}
                    end={{ x: 1, y: 0 }}
                    style={styles.iconGradient}
                  >
                    <Ionicons name="globe-outline" size={22} color="#FFFFFF" />
                  </LinearGradient>
                </View>
                <View style={styles.settingContent}>
                  <Text style={styles.settingLabel}>Lingua</Text>
                  <Text style={styles.settingDescription}>{settings.language === 'it' ? 'Italiano' :
                    settings.language === 'en' ? 'English' :
                    settings.language === 'es' ? 'Español' :
                    settings.language === 'fr' ? 'Français' :
                    settings.language === 'de' ? 'Deutsch' :
                    settings.language === 'pt' ? 'Português' :
                    settings.language === 'ru' ? 'Русский' :
                    settings.language === 'zh' ? '中文' :
                    settings.language === 'ja' ? '日本語' :
                    settings.language === 'ar' ? 'العربية' : 'Italiano'}</Text>
                </View>
                <Ionicons name="chevron-forward" size={20} color="#AAAAAA" />
              </TouchableOpacity>
            </View>

            {/* Sezione invita un amico */}
            <View style={styles.section}>


              <TouchableOpacity
                style={styles.settingItem}
                onPress={handleInvitePress}
              >
                <View style={styles.settingIconContainer}>
                  <LinearGradient
                    colors={['#1E88E5', '#D81B60']}
                    start={{ x: 0, y: 0 }}
                    end={{ x: 1, y: 0 }}
                    style={styles.iconGradient}
                  >
                    <Ionicons name="people-outline" size={22} color="#FFFFFF" />
                  </LinearGradient>
                </View>
                <View style={styles.settingContent}>
                  <Text style={styles.settingLabel}>Invita un amico</Text>
                  <Text style={styles.settingDescription}>Condividi TrendyChat con i tuoi amici</Text>
                </View>
                <Ionicons name="chevron-forward" size={20} color="#AAAAAA" />
              </TouchableOpacity>
            </View>

            {/* Sezione TrendyWallet */}
            <View style={styles.section}>
              <TouchableOpacity
                style={styles.settingItem}
                onPress={() => navigation.navigate('TrendyWallet')}
              >
                <View style={styles.settingIconContainer}>
                  <LinearGradient
                    colors={['#1E88E5', '#D81B60']}
                    start={{ x: 0, y: 0 }}
                    end={{ x: 1, y: 0 }}
                    style={styles.iconGradient}
                  >
                    <Ionicons name="wallet-outline" size={22} color="#FFFFFF" />
                  </LinearGradient>
                </View>
                <View style={styles.settingContent}>
                  <Text style={styles.settingLabel}>TrendyWallet</Text>
                  <Text style={styles.settingDescription}>Gestisci i tuoi guadagni e saldi TrendyCoin</Text>
                </View>
                <Ionicons name="chevron-forward" size={20} color="#AAAAAA" />
              </TouchableOpacity>


            </View>

            {/* Informazioni app */}
            <View style={styles.aboutSection}>
              <Text style={styles.appName}>TrendyChat</Text>
              <Text style={styles.appVersion}>Versione 1.0.0</Text>
              <Text style={styles.copyright}>© 2025 TrendyChat. Tutti i diritti riservati.</Text>
            </View>
          </ScrollView>
        )}
      </SafeAreaView>
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#121212',
    paddingTop: StatusBar.currentHeight,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 12,
    paddingTop: Platform.OS === 'ios' ? 10 : 5,
    paddingBottom: 12,
    backgroundColor: 'transparent',
    height: 50,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  scrollContainer: {
    flex: 1,
    backgroundColor: '#121212',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#121212',
  },
  // Profilo utente
  profileContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#1E1E1E',
    marginBottom: 8,
  },
  profileAvatarContainer: {
    marginRight: 16,
    position: 'relative',
  },
  editPhotoButton: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    backgroundColor: '#1E88E5',
    width: 28,
    height: 28,
    borderRadius: 14,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#121212',
  },
  profileInfo: {
    flex: 1,
  },
  profileName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 4,
  },
  profileStatus: {
    fontSize: 14,
    color: '#AAAAAA',
  },
  // Sezioni
  section: {
    backgroundColor: '#1E1E1E',
    marginBottom: 8,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#AAAAAA',
    marginBottom: 8,
    paddingHorizontal: 16,
    paddingTop: 12,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#2A2A2A',
  },
  settingIconContainer: {
    marginRight: 16,
  },
  iconGradient: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
  },
  settingContent: {
    flex: 1,
  },
  settingLabel: {
    fontSize: 16,
    color: '#FFFFFF',
    marginBottom: 2,
  },
  settingDescription: {
    fontSize: 14,
    color: '#AAAAAA',
  },
  // Sezione informazioni app
  aboutSection: {
    padding: 24,
    alignItems: 'center',
    marginTop: 16,
    marginBottom: 32,
  },
  appName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 8,
  },
  appVersion: {
    fontSize: 14,
    color: '#AAAAAA',
    marginBottom: 8,
  },
  copyright: {
    fontSize: 12,
    color: '#AAAAAA',
    textAlign: 'center',
  },

});

export default SettingsScreen;