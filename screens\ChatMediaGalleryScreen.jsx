import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  Image,
  Dimensions,
  ActivityIndicator,
  SafeAreaView,
  StatusBar,
} from 'react-native';
import { useRoute, useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import useChatStore from '../store/useChatStore';
import { theme } from '../theme';

const { width } = Dimensions.get('window');
const ITEM_WIDTH = width / 3;
const SPACING = 1;

const ChatMediaGalleryScreen = () => {
  const route = useRoute();
  const navigation = useNavigation();
  const { chatId, name } = route.params;
  const { getMediaMessages, loading } = useChatStore();

  const [activeTab, setActiveTab] = useState('all');
  const [media, setMedia] = useState([]);
  const [filteredMedia, setFilteredMedia] = useState([]);

  useEffect(() => {
    loadMedia();
  }, [chatId]);

  useEffect(() => {
    filterMedia(activeTab);
  }, [activeTab, media]);

  const loadMedia = async () => {
    try {
      const mediaMessages = await getMediaMessages(chatId);
      setMedia(mediaMessages);
      filterMedia(activeTab);
    } catch (error) {
      console.error('Errore nel caricamento dei media:', error);
    }
  };

  const filterMedia = (tab) => {
    switch (tab) {
      case 'images':
        setFilteredMedia(media.filter(item => item.type === 'image'));
        break;
      case 'videos':
        setFilteredMedia(media.filter(item => item.type === 'video'));
        break;
      case 'documents':
        setFilteredMedia(media.filter(item => item.type === 'document'));
        break;
      case 'audio':
        setFilteredMedia(media.filter(item => item.type === 'audio'));
        break;
      case 'all':
      default:
        setFilteredMedia(media);
        break;
    }
  };

  const handleMediaPress = (item) => {
    if (item.type === 'image') {
      navigation.navigate('MediaViewer', {
        mediaUri: item.uri,
        mediaType: 'image',
      });
    } else if (item.type === 'video') {
      navigation.navigate('MediaViewer', {
        mediaUri: item.uri,
        thumbnailUri: item.thumbnail,
        mediaType: 'video',
      });
    } else if (item.type === 'document') {
      // Apri il documento
    } else if (item.type === 'audio') {
      // Riproduci l'audio
    }
  };

  const renderMediaItem = ({ item, index }) => {
    if (item.type === 'image') {
      return (
        <TouchableOpacity
          style={styles.mediaItem}
          onPress={() => handleMediaPress(item)}
        >
          <Image
            source={{ uri: item.uri }}
            style={styles.mediaImage}
            resizeMode="cover"
          />
        </TouchableOpacity>
      );
    } else if (item.type === 'video') {
      return (
        <TouchableOpacity
          style={styles.mediaItem}
          onPress={() => handleMediaPress(item)}
        >
          <Image
            source={{ uri: item.thumbnail || item.uri }}
            style={styles.mediaImage}
            resizeMode="cover"
          />
          <View style={styles.videoIndicator}>
            <Ionicons name="play" size={20} color="#FFFFFF" />
          </View>
        </TouchableOpacity>
      );
    } else if (item.type === 'document') {
      return (
        <TouchableOpacity
          style={[styles.mediaItem, styles.documentItem]}
          onPress={() => handleMediaPress(item)}
        >
          <View style={styles.documentIcon}>
            <Ionicons name="document-text" size={32} color={theme.colors.primary} />
          </View>
          <Text style={styles.documentName} numberOfLines={2}>
            {item.name || 'Documento'}
          </Text>
        </TouchableOpacity>
      );
    } else if (item.type === 'audio') {
      return (
        <TouchableOpacity
          style={[styles.mediaItem, styles.audioItem]}
          onPress={() => handleMediaPress(item)}
        >
          <View style={styles.audioIcon}>
            <Ionicons name="musical-note" size={32} color={theme.colors.primary} />
          </View>
          <Text style={styles.audioName} numberOfLines={1}>
            Audio
          </Text>
        </TouchableOpacity>
      );
    }

    return null;
  };

  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <Ionicons name="images-outline" size={64} color={theme.colors.textSecondary} />
      <Text style={styles.emptyText}>Nessun media in questa chat</Text>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#1E88E5" />

      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color="#FFFFFF" />
        </TouchableOpacity>

        <Text style={styles.headerTitle}>Media di {name}</Text>
      </View>

      <View style={styles.tabContainer}>
        <TouchableOpacity
          style={[styles.tab, activeTab === 'all' && styles.activeTab]}
          onPress={() => setActiveTab('all')}
        >
          <Text style={[styles.tabText, activeTab === 'all' && styles.activeTabText]}>
            Tutti
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.tab, activeTab === 'images' && styles.activeTab]}
          onPress={() => setActiveTab('images')}
        >
          <Text style={[styles.tabText, activeTab === 'images' && styles.activeTabText]}>
            Immagini
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.tab, activeTab === 'videos' && styles.activeTab]}
          onPress={() => setActiveTab('videos')}
        >
          <Text style={[styles.tabText, activeTab === 'videos' && styles.activeTabText]}>
            Video
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.tab, activeTab === 'documents' && styles.activeTab]}
          onPress={() => setActiveTab('documents')}
        >
          <Text style={[styles.tabText, activeTab === 'documents' && styles.activeTabText]}>
            Documenti
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.tab, activeTab === 'audio' && styles.activeTab]}
          onPress={() => setActiveTab('audio')}
        >
          <Text style={[styles.tabText, activeTab === 'audio' && styles.activeTabText]}>
            Audio
          </Text>
        </TouchableOpacity>
      </View>

      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={theme.colors.primary} />
        </View>
      ) : (
        <FlatList
          data={filteredMedia}
          renderItem={renderMediaItem}
          keyExtractor={(item) => item.id}
          numColumns={3}
          contentContainerStyle={styles.mediaList}
          ListEmptyComponent={renderEmptyState}
          showsVerticalScrollIndicator={false}
        />
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#1E88E5',
    height: 56,
    paddingHorizontal: 16,
  },
  backButton: {
    marginRight: 16,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  tabContainer: {
    flexDirection: 'row',
    backgroundColor: theme.colors.card,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  tab: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
  },
  activeTab: {
    borderBottomWidth: 2,
    borderBottomColor: theme.colors.primary,
  },
  tabText: {
    fontSize: 14,
    color: theme.colors.textSecondary,
  },
  activeTabText: {
    color: theme.colors.primary,
    fontWeight: 'bold',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  mediaList: {
    padding: SPACING,
  },
  mediaItem: {
    width: ITEM_WIDTH - SPACING * 2,
    height: ITEM_WIDTH - SPACING * 2,
    margin: SPACING,
    backgroundColor: theme.colors.card,
    borderRadius: 4,
    overflow: 'hidden',
  },
  mediaImage: {
    width: '100%',
    height: '100%',
  },
  videoIndicator: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    transform: [{ translateX: -18 }, { translateY: -18 }],
  },
  documentItem: {
    justifyContent: 'center',
    alignItems: 'center',
    padding: 8,
  },
  documentIcon: {
    width: 48,
    height: 48,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  documentName: {
    fontSize: 12,
    color: theme.colors.text,
    textAlign: 'center',
  },
  audioItem: {
    justifyContent: 'center',
    alignItems: 'center',
    padding: 8,
  },
  audioIcon: {
    width: 48,
    height: 48,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  audioName: {
    fontSize: 12,
    color: theme.colors.text,
    textAlign: 'center',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  emptyText: {
    fontSize: 16,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    marginTop: 16,
  },
});

export default ChatMediaGalleryScreen;
