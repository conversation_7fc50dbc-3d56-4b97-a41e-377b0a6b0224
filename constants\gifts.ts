/**
 * <PERSON>nti per i regali
 */

export interface Gift {
  id: string;
  name: string;
  description: string;
  value: number;
  category: 'base' | 'medium' | 'premium' | 'super_premium';
  icon: string; // Emoji icon per l'anteprima
  imageUrl?: any; // Immagine statica per anteprima
  animationType: 'static' | 'lottie' | 'video';
  animationUrl?: any; // File Lottie JSON
  videoUrl?: any; // File video MP4
  soundUrl?: any; // File audio
}

export const GIFT_CATEGORIES = {
  BASE: 'base',
  MEDIUM: 'medium',
  PREMIUM: 'premium',
  SUPER_PREMIUM: 'super_premium',
};

export const GIFTS: Gift[] = [
  // Regali Base (5-50 TC)
  {
    id: 'gift_5tc',
    name: '<PERSON><PERSON>re',
    description: 'Un cuore rosso che pulsa e si espande',
    value: 5,
    category: 'base',
    icon: '❤️',
    animationType: 'lottie',
  },
  {
    id: 'gift_10tc',
    name: '<PERSON>',
    description: 'Una stella dorata che brilla',
    value: 10,
    category: 'base',
    icon: '⭐',
    animationType: 'lottie',
  },
  {
    id: 'gift_15tc',
    name: 'Applauso',
    description: '<PERSON>i che applaudono',
    value: 15,
    category: 'base',
    icon: '👏',
    animationType: 'lottie',
  },
  {
    id: 'gift_20tc',
    name: 'Rosa',
    description: 'Un fiore di rosa che sboccia',
    value: 20,
    category: 'base',
    icon: '🌹',
    animationType: 'lottie',
  },
  {
    id: 'gift_25tc',
    name: 'Palloncino',
    description: 'Un palloncino colorato che vola verso l\'alto',
    value: 25,
    category: 'base',
    icon: '🎈',
    animationType: 'lottie',
  },
  {
    id: 'gift_30tc',
    name: 'Gelato',
    description: 'Un cono gelato colorato',
    value: 30,
    category: 'base',
    icon: '🍦',
    animationType: 'static',
  },
  {
    id: 'gift_35tc',
    name: 'Caffè',
    description: 'Una tazza di caffè fumante',
    value: 35,
    category: 'base',
    icon: '☕',
    animationType: 'static',
  },
  {
    id: 'gift_40tc',
    name: 'Microfono',
    description: 'Un microfono che vibra con onde sonore',
    value: 40,
    category: 'base',
    icon: '🎤',
    animationType: 'lottie',
  },
  {
    id: 'gift_45tc',
    name: 'Like',
    description: 'Un pollice in su che si illumina',
    value: 45,
    category: 'base',
    icon: '👍',
    animationType: 'lottie',
  },
  {
    id: 'gift_50tc',
    name: 'Occhiali da Sole',
    description: 'Occhiali da sole cool',
    value: 50,
    category: 'base',
    icon: '🕶️',
    animationType: 'static',
  },

  // Regali Medium (100-500 TC)
  {
    id: 'gift_100tc',
    name: 'Fuochi d\'Artificio',
    description: 'Esplosione di fuochi d\'artificio colorati',
    value: 100,
    category: 'medium',
    icon: '🎆',
    animationType: 'lottie',
  },
  {
    id: 'gift_150tc',
    name: 'Corona',
    description: 'Una corona reale con gemme scintillanti',
    value: 150,
    category: 'medium',
    icon: '👑',
    animationType: 'lottie',
  },
  {
    id: 'gift_200tc',
    name: 'Trofeo',
    description: 'Un trofeo dorato che brilla',
    value: 200,
    category: 'medium',
    icon: '🏆',
    animationType: 'lottie',
  },
  {
    id: 'gift_250tc',
    name: 'DJ Mixer',
    description: 'Console DJ con luci e controlli',
    value: 250,
    category: 'medium',
    icon: '🎧',
    animationType: 'video',
  },
  {
    id: 'gift_300tc',
    name: 'Pioggia di Monete',
    description: 'Cascata di monete d\'oro',
    value: 300,
    category: 'medium',
    animationType: 'video',
  },
  {
    id: 'gift_350tc',
    name: 'Diamante',
    description: 'Un diamante brillante che ruota',
    value: 350,
    category: 'medium',
    animationType: 'lottie',
  },
  {
    id: 'gift_400tc',
    name: 'Razzo',
    description: 'Un razzo che decolla con scia di fuoco',
    value: 400,
    category: 'medium',
    animationType: 'video',
  },
  {
    id: 'gift_450tc',
    name: 'Auto Sportiva',
    description: 'Auto sportiva che sfreccia',
    value: 450,
    category: 'medium',
    animationType: 'video',
  },
  {
    id: 'gift_500tc',
    name: 'Chitarra Elettrica',
    description: 'Chitarra elettrica con effetti luminosi',
    value: 500,
    category: 'medium',
    animationType: 'video',
  },

  // Regali Premium (1000-2000 TC)
  {
    id: 'gift_1000tc',
    name: 'Drago',
    description: 'Un drago che vola e sputa fuoco',
    value: 1000,
    category: 'premium',
    animationType: 'video',
  },
  {
    id: 'gift_1200tc',
    name: 'Castello Magico',
    description: 'Un castello magico che appare con effetti particellari',
    value: 1200,
    category: 'premium',
    animationType: 'video',
  },
  {
    id: 'gift_1400tc',
    name: 'Concerto',
    description: 'Palco con luci e effetti speciali',
    value: 1400,
    category: 'premium',
    animationType: 'video',
  },
  {
    id: 'gift_1600tc',
    name: 'Nave Spaziale',
    description: 'Astronave futuristica che viaggia nello spazio',
    value: 1600,
    category: 'premium',
    animationType: 'video',
  },
  {
    id: 'gift_1800tc',
    name: 'Isola Tropicale',
    description: 'Isola paradisiaca con palme e mare',
    value: 1800,
    category: 'premium',
    animationType: 'video',
  },
  {
    id: 'gift_2000tc',
    name: 'Supereroe',
    description: 'Supereroe che vola con mantello',
    value: 2000,
    category: 'premium',
    animationType: 'video',
  },

  // Regali Super Premium (3000-5000 TC)
  {
    id: 'gift_3000tc',
    name: 'Città Futuristica',
    description: 'Skyline di città futuristica con ologrammi',
    value: 3000,
    category: 'super_premium',
    animationType: 'video',
  },
  {
    id: 'gift_3500tc',
    name: 'Universo',
    description: 'Galassie e stelle che formano un universo',
    value: 3500,
    category: 'super_premium',
    animationType: 'video',
  },
  {
    id: 'gift_4000tc',
    name: 'Jet Privato',
    description: 'Jet privato di lusso che vola',
    value: 4000,
    category: 'super_premium',
    animationType: 'video',
  },
  {
    id: 'gift_4500tc',
    name: 'Palazzo Reale',
    description: 'Palazzo sontuoso con fontane e giardini',
    value: 4500,
    category: 'super_premium',
    animationType: 'video',
  },
  {
    id: 'gift_5000tc',
    name: 'TrendyVIP',
    description: 'Logo VIP personalizzato con effetti spettacolari',
    value: 5000,
    category: 'super_premium',
    animationType: 'video',
  },
];

export const getGiftById = (id: string): Gift | undefined => {
  return GIFTS.find(gift => gift.id === id);
};

export const getGiftsByCategory = (category: string): Gift[] => {
  return GIFTS.filter(gift => gift.category === category);
};

export const getGiftsByValueRange = (minValue: number, maxValue: number): Gift[] => {
  return GIFTS.filter(gift => gift.value >= minValue && gift.value <= maxValue);
};

// 🔧 AGGIUNGO ICONE MANCANTI AI GIFTS
const addMissingIcons = (gifts: Gift[]): Gift[] => {
  const iconMap: { [key: string]: string } = {
    'Pioggia di Monete': '💰',
    'Diamante': '💎',
    'Razzo': '🚀',
    'Auto Sportiva': '🏎️',
    'Chitarra Elettrica': '🎸',
    'Drago': '🐉',
    'Castello Magico': '🏰',
    'Concerto': '🎵',
    'Nave Spaziale': '🚀',
    'Isola Tropicale': '🏝️',
    'Supereroe': '🦸',
    'Città Futuristica': '🌆',
    'Universo': '🌌',
    'Jet Privato': '✈️',
    'Palazzo Reale': '🏛️',
    'TrendyVIP': '👑'
  };

  return gifts.map(gift => ({
    ...gift,
    icon: gift.icon || iconMap[gift.name] || '🎁'
  }));
};

// 🔧 EXPORT MANCANTE: ALL_GIFTS per compatibilità con icone
export const ALL_GIFTS = addMissingIcons(GIFTS);
