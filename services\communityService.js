import AsyncStorage from '../utils/asyncStorageConfig';
import { API_URL } from '../config/api';
import useAuthStore from '../store/authStore';

/**
 * Servizio per la gestione delle community
 */
class CommunityService {
  constructor() {
    // Rimuovi /api da ${API_URL}/api/communities perché API_URL già include /api
    this.baseUrl = `${API_URL}/communities`;
    console.log('URL base per le community:', this.baseUrl);
  }

  /**
   * Ottiene il token di autenticazione (ID utente per il server HP)
   * @returns {Promise<string>} Token di autenticazione
   */
  async getAuthToken() {
    try {
      // Per il server HP, il token è l'ID utente
      // Prima prova a ottenere l'ID utente da authStore
      try {
        const authState = useAuthStore.getState();
        console.log('AuthStore state:', authState ? 'Disponibile' : 'Non disponibile');

        if (authState && authState.user && authState.user.id) {
          const userId = authState.user.id;
          console.log('Token da authStore.user.id:', userId ? 'Trovato' : 'Non trovato');
          return userId;
        }
      } catch (storeError) {
        console.error('Errore nel recupero dell\'ID utente da authStore:', storeError);
      }

      // Se non trova l'ID utente in authStore, prova con authService
      const authService = require('./authService').default;
      const currentUser = authService.getCurrentUser();

      if (currentUser && currentUser.id) {
        console.log('Token da authService.getCurrentUser().id:', currentUser.id ? 'Trovato' : 'Non trovato');
        return currentUser.id;
      }

      // Se non trova l'ID utente, prova con il token salvato (che potrebbe essere l'ID utente)
      let token = authService.getToken();
      console.log('Token da authService:', token ? 'Trovato' : 'Non trovato');

      // Se il token contiene il prefisso jwt_token_, rimuovilo
      if (token && token.startsWith('jwt_token_')) {
        token = token.replace('jwt_token_', '');
        console.log('Prefisso jwt_token_ rimosso dal token');
      }

      // Se non trova il token, prova con AsyncStorage
      if (!token) {
        token = await AsyncStorage.getItem('@trendychat:token');
        console.log('Token da @trendychat:token:', token ? 'Trovato' : 'Non trovato');

        // Se il token contiene il prefisso jwt_token_, rimuovilo
        if (token && token.startsWith('jwt_token_')) {
          token = token.replace('jwt_token_', '');
          console.log('Prefisso jwt_token_ rimosso dal token di AsyncStorage');
        }
      }

      // Prova anche a recuperare l'utente da AsyncStorage
      if (!token) {
        try {
          const userJson = await AsyncStorage.getItem('@trendychat:user');
          if (userJson) {
            const user = JSON.parse(userJson);
            if (user && user.id) {
              console.log('Token da AsyncStorage user.id:', user.id ? 'Trovato' : 'Non trovato');
              return user.id;
            }
          }
        } catch (parseError) {
          console.error('Errore nel parsing dell\'utente da AsyncStorage:', parseError);
        }
      }

      if (!token) {
        console.warn('Nessun token di autenticazione trovato');
        return null;
      }

      // Log del token (solo per debug)
      console.log('Token di autenticazione trovato:', token.substring(0, 10) + '...');

      // Verifica se il token contiene caratteri non validi o spazi
      if (token && /\s/.test(token)) {
        token = token.trim();
      }

      // Verifica se il token è codificato in Base64
      try {
        if (token && token.includes('%')) {
          token = decodeURIComponent(token);
        }
      } catch (decodeError) {
        console.error('Errore nella decodifica del token:', decodeError);
      }

      return token;
    } catch (error) {
      console.error('Errore nel recupero del token:', error);
      return null;
    }
  }

  /**
   * Carica un'immagine per una community
   * @param {string} imageUri - URI dell'immagine
   * @param {string} userId - ID dell'utente
   * @returns {Promise<Object>} Risultato dell'operazione
   */
  async uploadCommunityImage(imageUri, userId) {
    try {
      console.log('Caricamento immagine community sul server HP:', imageUri);
      console.log('ID utente:', userId);

      // Verifica che l'URI dell'immagine sia valido
      if (!imageUri || typeof imageUri !== 'string') {
        throw new Error('URI dell\'immagine non valido');
      }

      // Ottieni il token di autenticazione
      const token = await this.getAuthToken();
      if (!token) {
        throw new Error('Token di autenticazione non disponibile');
      }

      // Importa il servizio cloud
      const cloudService = require('./cloudService').default;

      // Assicurati che cloudService sia inizializzato con il token
      cloudService.setToken(token);

      // Carica l'immagine sul server con metadati espliciti per il tipo di file
      const uploadResult = await cloudService.uploadFile(
        imageUri,
        userId,
        'community',
        true, // Le immagini delle community sono pubbliche
        {
          type: 'community',
          userId: userId,
          isCommunityPhoto: true
        }
      );

      if (!uploadResult.success) {
        throw new Error(uploadResult.error || 'Errore durante il caricamento dell\'immagine');
      }

      console.log('Immagine community caricata con successo:', uploadResult.file);

      return {
        success: true,
        imageUrl: uploadResult.file.url,
        localUri: imageUri,
        data: uploadResult // Includi tutti i dati della risposta
      };
    } catch (error) {
      console.error('Errore nel caricamento dell\'immagine della community:', error);

      // In caso di errore generale, restituisci l'URI locale come fallback
      return {
        success: false,
        error: error.message,
        imageUrl: imageUri,
        localUri: imageUri
      };
    }
  }

  /**
   * Crea una nuova community
   * @param {Object} communityData - Dati della community
   * @returns {Promise<Object>} Risultato dell'operazione
   */
  async createCommunity(communityData) {
    try {
      console.log('Creazione community sul server HP:', communityData);

      // Ottieni il token di autenticazione
      const token = await this.getAuthToken();
      console.log('Token di autenticazione:', token ? 'Disponibile' : 'Non disponibile');

      if (!token) {
        throw new Error('Token di autenticazione non disponibile');
      }

      // Ottieni l'ID utente dal servizio di autenticazione
      const authService = require('./authService').default;
      const currentUser = authService.getCurrentUser();
      const userId = currentUser?.id || currentUser?._id;

      if (!userId) {
        console.warn('ID utente non disponibile, potrebbe causare problemi con la creazione della community');
      }

      console.log('ID utente corrente:', userId);

      let imageUrl = null;

      // Fase 1: Caricamento dell'immagine (se presente)
      if (communityData.photoUri) {
        try {
          console.log('Caricamento immagine della community...');

          // Utilizziamo il metodo uploadCommunityImage che abbiamo appena migliorato
          const uploadResult = await this.uploadCommunityImage(communityData.photoUri, userId);

          if (uploadResult.success) {
            imageUrl = uploadResult.imageUrl;
            console.log('Immagine caricata con successo:', imageUrl);
          } else {
            console.warn('Errore nel caricamento dell\'immagine:', uploadResult.error);
            // Continuiamo comunque con la creazione della community
            // Usiamo l'URI locale come fallback
            imageUrl = communityData.photoUri;
          }
        } catch (imageError) {
          console.error('Errore nel caricamento dell\'immagine:', imageError);
          // Continuiamo comunque con la creazione della community
          imageUrl = communityData.photoUri;
        }
      }

      // Fase 2: Creazione della community
      console.log('Invio richiesta per creare la community:', this.baseUrl);

      // Prepara i dati per la creazione della community come JSON
      const communityPayload = {
        name: communityData.name,
        description: communityData.description || '',
        photoURL: imageUrl || null
      };

      // Timeout più lungo per la creazione (60 secondi)
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 60000); // 60 secondi

      try {
        // Invia la richiesta al server
        const response = await fetch(this.baseUrl, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(communityPayload),
          signal: controller.signal
        });

        // Pulisci il timeout
        clearTimeout(timeoutId);

        // Log della risposta
        console.log('Risposta dal server - Status:', response.status);
        console.log('Risposta dal server - Headers:', JSON.stringify([...response.headers.entries()]));

        const responseText = await response.text();
        console.log('Risposta dal server (testo):', responseText.substring(0, 200) + (responseText.length > 200 ? '...' : ''));

        let data;
        try {
          data = JSON.parse(responseText);
          console.log('Risposta dal server (JSON):', JSON.stringify(data).substring(0, 200) + (JSON.stringify(data).length > 200 ? '...' : ''));
        } catch (parseError) {
          console.error('Errore nel parsing della risposta JSON:', parseError);

          // Se la risposta non è JSON ma il server ha risposto con successo, creiamo una community locale
          if (response.ok) {
            const localCommunity = {
              id: `community_${Date.now()}`,
              name: communityData.name,
              description: communityData.description || '',
              photoURL: imageUrl,
              createdAt: new Date().toISOString(),
              members: userId ? [userId] : [],
              admins: userId ? [userId] : [],
              groups: []
            };

            return {
              success: true,
              community: localCommunity,
              isLocal: true,
              message: 'Community creata con successo (risposta non JSON)'
            };
          }

          throw new Error('Errore nel parsing della risposta JSON');
        }

        if (!response.ok) {
          throw new Error(data.error?.message || data.message || data.error || 'Errore nella creazione della community');
        }

        // Crea l'oggetto community con i dati ricevuti dal server
        const serverCommunity = data.community || data;

        // Assicurati che l'oggetto community abbia tutti i campi necessari
        const community = {
          id: serverCommunity.id || serverCommunity._id || `community_${Date.now()}`,
          name: serverCommunity.name || communityData.name,
          description: serverCommunity.description || communityData.description || '',
          photoURL: serverCommunity.photoURL || imageUrl,
          createdAt: serverCommunity.createdAt || new Date().toISOString(),
          members: serverCommunity.members || (userId ? [userId] : []),
          admins: serverCommunity.admins || (userId ? [userId] : []),
          groups: serverCommunity.groups || []
        };

        console.log('Community creata con successo:', community.name);

        return {
          success: true,
          community: community,
          isLocal: false // Indica che è una community del server
        };
      } catch (fetchError) {
        // Pulisci il timeout in caso di errore
        clearTimeout(timeoutId);

        console.error('Errore nella richiesta fetch:', fetchError);

        // Se l'errore è un timeout, fornisci un messaggio più specifico
        if (fetchError.name === 'AbortError') {
          console.log('La richiesta è stata interrotta per timeout');
        }

        // In caso di errore, crea una community locale
        const localCommunity = {
          id: `community_${Date.now()}`,
          name: communityData.name,
          description: communityData.description || '',
          photoURL: imageUrl, // Usa l'URL dell'immagine caricata se disponibile
          createdAt: new Date().toISOString(),
          members: userId ? [userId] : [],
          admins: userId ? [userId] : [],
          groups: []
        };

        console.log('Creata community locale:', localCommunity.name);

        return {
          success: true,
          community: localCommunity,
          isLocal: true, // Indica che è una community locale
          error: fetchError.message
        };
      }
    } catch (error) {
      console.error('Errore nella creazione della community:', error);

      // Ottieni l'ID utente dal servizio di autenticazione
      const authService = require('./authService').default;
      const currentUser = authService.getCurrentUser();
      const userId = currentUser?.id || currentUser?._id;

      // Anche in caso di errore generale, crea una community locale
      const localCommunity = {
        id: `community_${Date.now()}`,
        name: communityData.name,
        description: communityData.description || '',
        photoURL: communityData.photoUri, // Usa l'URI locale
        createdAt: new Date().toISOString(),
        members: userId ? [userId] : [],
        admins: userId ? [userId] : [],
        groups: []
      };

      console.log('Creata community locale (dopo errore):', localCommunity.name);

      return {
        success: true,
        community: localCommunity,
        isLocal: true, // Indica che è una community locale
        error: error.message
      };
    }
  }
}

export default new CommunityService();
