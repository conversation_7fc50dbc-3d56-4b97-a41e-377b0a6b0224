import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  FlatList,
  Image,
  Dimensions,
  ActivityIndicator,
  Platform
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation, useRoute } from '@react-navigation/native';
import { LinearGradient } from 'expo-linear-gradient';
import * as FileSystem from 'expo-file-system';
import * as MediaLibrary from 'expo-media-library';
import * as Sharing from 'expo-sharing';
import useChatStore from '../store/useChatStore';
import useGroupStore from '../store/groupStore';
import VoiceMessagePlayer from '../components/chat/VoiceMessagePlayer';
import VideoMessage from '../components/chat/VideoMessage';
import DocumentMessage from '../components/chat/DocumentMessage';

const { width } = Dimensions.get('window');
const imageSize = (width - 40) / 3;

const ChatMediaScreen = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const { chatId, name, isPrivateChat } = route.params;

  // 🔍 DISTINGUI TRA CHAT PRIVATE E GRUPPI (usa il parametro esplicito)
  const isGroup = !isPrivateChat; // Se non è chat privata, è un gruppo

  // 📱 STORE PER CHAT PRIVATE
  const { messages: chatMessages, loadMessages: loadChatMessages } = useChatStore();

  // 👥 STORE PER GRUPPI
  const { currentGroup } = useGroupStore();

  // 📨 MESSAGGI CORRETTI IN BASE AL TIPO
  const messages = isGroup ? (currentGroup?.messages || []) : chatMessages;

  console.log('🔍 ChatMediaScreen - isPrivateChat:', isPrivateChat);
  console.log('🔍 ChatMediaScreen - isGroup:', isGroup);
  console.log('🔍 ChatMediaScreen - messages count:', messages?.length || 0);

  const [activeTab, setActiveTab] = useState('media');
  const [mediaItems, setMediaItems] = useState([]);
  const [links, setLinks] = useState([]);
  const [documents, setDocuments] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // 🔄 Carica i messaggi se necessario (per chat private)
    if (!isGroup && (!messages || messages.length === 0)) {
      console.log('🔄 ChatMediaScreen: Caricando messaggi per chat privata:', chatId);
      loadChatMessages(chatId);
    }
  }, [chatId, isGroup]);

  useEffect(() => {
    // Filtra i messaggi per ottenere media, link e documenti
    if (messages && messages.length > 0) {
      const media = [];
      const linksList = [];
      const docs = [];

      messages.forEach(message => {
        console.log('🔍 Analizzando messaggio:', {
          id: message.id,
          type: message.type,
          hasImageUrl: !!message.imageUrl,
          hasImage: !!message.image,
          hasVideoUrl: !!message.videoUrl,
          hasVideo: !!message.video,
          hasAudioUrl: !!message.audioUrl,
          hasAudio: !!message.audio,
          hasContent: !!message.content,
          hasText: !!message.text
        });

        // 🖼️ IMMAGINI - Basato su type e URL
        if (message.type === 'image' && (message.imageUrl || message.image)) {
          media.push({
            id: message.id,
            type: 'image',
            url: message.imageUrl || message.image,
            timestamp: message.timestamp || message.createdAt,
            sender: message.senderId || message.sender
          });
          console.log('📷 Aggiunta immagine:', message.imageUrl || message.image);
        }
        // 🎥 VIDEO - Basato su type e URL
        else if (message.type === 'video' && (message.videoUrl || message.video)) {
          media.push({
            id: message.id,
            type: 'video',
            url: message.videoUrl || message.video,
            thumbnail: message.thumbnail, // Aggiungi thumbnail se disponibile
            timestamp: message.timestamp || message.createdAt,
            sender: message.senderId || message.sender
          });
          console.log('🎬 Aggiunto video:', message.videoUrl || message.video);
          console.log('🖼️ Thumbnail video:', message.thumbnail || 'Non disponibile');
        }
        // 🎵 AUDIO - Basato su type e URL
        else if (message.type === 'audio' && (message.audioUrl || message.audio)) {
          docs.push({
            id: message.id,
            type: 'audio',
            url: message.audioUrl || message.audio,
            name: 'Registrazione audio',
            duration: message.duration || 0,
            timestamp: message.timestamp || message.createdAt,
            sender: message.senderId || message.sender
          });
          console.log('🎤 Aggiunto audio:', message.audioUrl || message.audio);
        }
        // 📄 DOCUMENTI
        else if (message.document) {
          docs.push({
            id: message.id,
            type: 'document',
            url: message.document.url,
            name: message.document.name,
            size: message.document.size,
            timestamp: message.timestamp || message.createdAt,
            sender: message.senderId || message.sender
          });
          console.log('📄 Aggiunto documento:', message.document.name);
        }
        // 🔗 LINK NEL TESTO (gruppi usano content, chat private usano text)
        else if (message.content || message.text) {
          const textContent = message.content || message.text;
          const urlRegex = /(https?:\/\/[^\s]+)/g;
          const foundLinks = textContent.match(urlRegex);

          if (foundLinks) {
            foundLinks.forEach(link => {
              linksList.push({
                id: message.id + link,
                url: link,
                text: textContent,
                timestamp: message.timestamp || message.createdAt,
                sender: message.senderId || message.sender
              });
              console.log('🔗 Aggiunto link:', link);
            });
          }
        }
      });

      console.log('📊 Media trovati:', {
        immagini: media.filter(m => m.type === 'image').length,
        video: media.filter(m => m.type === 'video').length,
        audio: docs.filter(d => d.type === 'audio').length,
        documenti: docs.filter(d => d.type === 'document').length,
        link: linksList.length,
        totaleMedia: media.length,
        totaleDocumenti: docs.length
      });

      setMediaItems(media);
      setLinks(linksList);
      setDocuments(docs);
    }

    setLoading(false);
  }, [messages]);

  const handleDownload = async (item) => {
    try {
      const { status } = await MediaLibrary.requestPermissionsAsync();

      if (status !== 'granted') {
        alert('Permesso negato per accedere alla galleria');
        return;
      }

      const fileUri = FileSystem.documentDirectory + item.name || 'file';
      const downloadResumable = FileSystem.createDownloadResumable(
        item.url,
        fileUri
      );

      const { uri } = await downloadResumable.downloadAsync();

      if (item.type === 'image' || item.type === 'video') {
        await MediaLibrary.saveToLibraryAsync(uri);
        alert('File salvato nella galleria');
      } else {
        await Sharing.shareAsync(uri);
      }
    } catch (error) {
      console.error('Errore durante il download:', error);
      alert('Impossibile scaricare il file');
    }
  };

  const renderMediaItem = ({ item }) => {
    if (item.type === 'image') {
      return (
        <TouchableOpacity
          style={styles.mediaItem}
          onPress={() => {
            console.log('🖼️ Navigando al MediaViewer per immagine:', item.url);
            navigation.navigate('MediaViewer', {
              mediaUri: item.url,
              mediaType: 'image'
            });
          }}
          onLongPress={() => handleDownload(item)}
        >
          <Image
            source={{ uri: item.url }}
            style={styles.mediaImage}
            resizeMode="cover"
          />
        </TouchableOpacity>
      );
    } else if (item.type === 'video') {
      // 🎥 USA STESSO COMPONENTE VIDEO DELLA CHAT
      return (
        <View style={styles.videoContainer}>
          <VideoMessage
            videoUri={item.url}
            thumbnail={item.thumbnail}
            isOwnMessage={false}
          />
        </View>
      );
    }
    return null;
  };

  const renderLinkItem = ({ item }) => (
    <TouchableOpacity
      style={styles.linkItem}
      onPress={() => navigation.navigate('WebViewer', { url: item.url })}
    >
      <View style={styles.linkContent}>
        <LinearGradient
          colors={['#1E88E5', '#D81B60']}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 0 }}
          style={styles.linkIconContainer}
        >
          <Ionicons name="link-outline" size={20} color="#FFFFFF" />
        </LinearGradient>
        <View style={styles.linkTextContainer}>
          <Text style={styles.linkUrl} numberOfLines={1}>{item.url}</Text>
          <Text style={styles.linkPreview} numberOfLines={2}>{item.text}</Text>
          <Text style={styles.linkTimestamp}>
            {new Date(item.timestamp).toLocaleDateString()}
          </Text>
        </View>
      </View>
    </TouchableOpacity>
  );

  const renderDocumentItem = ({ item }) => {
    if (item.type === 'audio') {
      // 🎵 USA STESSO COMPONENTE AUDIO DELLA CHAT
      return (
        <View style={styles.audioContainer}>
          <VoiceMessagePlayer
            audioUri={item.url}
            duration={item.duration || 0}
            isOwnMessage={false}
          />
        </View>
      );
    } else {
      // 📄 USA STESSO COMPONENTE DOCUMENTO DELLA CHAT
      return (
        <View style={styles.documentContainer}>
          <DocumentMessage
            documentUri={item.url}
            documentName={item.name}
            documentSize={item.size}
            isOwnMessage={false}
          />
        </View>
      );
    }
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent={true} />

      <LinearGradient
        colors={['#1E88E5', '#D81B60']}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 0 }}
        style={styles.header}
      >
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color="#FFFFFF" />
        </TouchableOpacity>

        <Text style={styles.headerTitle}>Media, link e documenti</Text>
      </LinearGradient>

      <View style={styles.tabContainer}>
        <TouchableOpacity
          style={[styles.tab, activeTab === 'media' && styles.activeTab]}
          onPress={() => setActiveTab('media')}
        >
          <Text style={[styles.tabText, activeTab === 'media' && styles.activeTabText]}>
            Media
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.tab, activeTab === 'links' && styles.activeTab]}
          onPress={() => setActiveTab('links')}
        >
          <Text style={[styles.tabText, activeTab === 'links' && styles.activeTabText]}>
            Link
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.tab, activeTab === 'documents' && styles.activeTab]}
          onPress={() => setActiveTab('documents')}
        >
          <Text style={[styles.tabText, activeTab === 'documents' && styles.activeTabText]}>
            Documenti
          </Text>
        </TouchableOpacity>
      </View>

      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#1E88E5" />
        </View>
      ) : (
        <>
          {activeTab === 'media' && (
            mediaItems.length > 0 ? (
              <FlatList
                data={mediaItems}
                renderItem={renderMediaItem}
                keyExtractor={(item) => item.id}
                numColumns={3}
                contentContainerStyle={styles.mediaList}
              />
            ) : (
              <View style={styles.emptyContainer}>
                <Ionicons name="images" size={64} color="#666666" />
                <Text style={styles.emptyText}>Nessun media condiviso</Text>
              </View>
            )
          )}

          {activeTab === 'links' && (
            links.length > 0 ? (
              <FlatList
                data={links}
                renderItem={renderLinkItem}
                keyExtractor={(item) => item.id}
                contentContainerStyle={styles.linksList}
              />
            ) : (
              <View style={styles.emptyContainer}>
                <Ionicons name="link" size={64} color="#666666" />
                <Text style={styles.emptyText}>Nessun link condiviso</Text>
              </View>
            )
          )}

          {activeTab === 'documents' && (
            documents.length > 0 ? (
              <FlatList
                data={documents}
                renderItem={renderDocumentItem}
                keyExtractor={(item) => item.id}
                contentContainerStyle={styles.documentsList}
              />
            ) : (
              <View style={styles.emptyContainer}>
                <Ionicons name="document-text" size={64} color="#666666" />
                <Text style={styles.emptyText}>Nessun documento condiviso</Text>
              </View>
            )
          )}
        </>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#121212',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingTop: Platform.OS === 'ios' ? 50 : 30,
    paddingBottom: 10,
    paddingHorizontal: 10,
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    color: '#FFFFFF',
    fontSize: 20,
    fontWeight: 'bold',
    marginLeft: 10,
  },
  tabContainer: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 255, 255, 0.1)',
    backgroundColor: 'rgba(30, 136, 229, 0.05)',
    marginHorizontal: 10,
    marginTop: 10,
    borderTopLeftRadius: 10,
    borderTopRightRadius: 10,
  },
  tab: {
    flex: 1,
    paddingVertical: 15,
    alignItems: 'center',
  },
  activeTab: {
    borderBottomWidth: 2,
    borderBottomColor: '#D81B60',
  },
  tabText: {
    fontSize: 16,
    color: '#AAAAAA',
  },
  activeTabText: {
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyText: {
    fontSize: 18,
    color: '#666666',
    marginTop: 10,
  },
  mediaList: {
    padding: 5,
  },
  mediaItem: {
    width: imageSize,
    height: imageSize,
    margin: 5,
    borderRadius: 8,
    overflow: 'hidden',
  },
  mediaImage: {
    width: '100%',
    height: '100%',
  },
  videoIndicator: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
  },
  linksList: {
    padding: 10,
  },
  linkItem: {
    backgroundColor: '#1E1E1E',
    borderRadius: 10,
    marginBottom: 10,
    overflow: 'hidden',
  },
  linkContent: {
    flexDirection: 'row',
    padding: 15,
  },
  linkIconContainer: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 10,
  },
  linkTextContainer: {
    flex: 1,
  },
  linkUrl: {
    color: '#1E88E5',
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 5,
  },
  linkPreview: {
    color: '#FFFFFF',
    fontSize: 14,
    marginBottom: 5,
  },
  linkTimestamp: {
    color: '#AAAAAA',
    fontSize: 12,
  },
  documentsList: {
    padding: 10,
  },
  documentItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#1E1E1E',
    borderRadius: 10,
    marginBottom: 10,
    padding: 15,
  },
  documentIconContainer: {
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 15,
    overflow: 'hidden',
  },
  documentContent: {
    flex: 1,
  },
  documentName: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 5,
  },
  documentSize: {
    color: '#AAAAAA',
    fontSize: 14,
    marginBottom: 5,
  },
  documentTimestamp: {
    color: '#AAAAAA',
    fontSize: 12,
  },
  downloadButton: {
    padding: 5,
  },
  downloadButtonGradient: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
  },
  // 🎥 STILI PER COMPONENTI CHAT
  videoContainer: {
    width: imageSize,
    height: imageSize,
    margin: 5,
    borderRadius: 8,
    overflow: 'hidden',
  },
  audioContainer: {
    backgroundColor: '#1E1E1E',
    borderRadius: 10,
    marginBottom: 10,
    padding: 10,
  },
  documentContainer: {
    backgroundColor: '#1E1E1E',
    borderRadius: 10,
    marginBottom: 10,
    padding: 10,
  },
});

export default ChatMediaScreen;
