// 🎥 SFU Service per TrendyChat - Architettura Ibrida
// Gestisce le connessioni al server SuperMicron SFU con mediasoup

import io from 'socket.io-client';
import {
  RTCPeerConnection,
  RTCSessionDescription,
  RTCIceCandidate,
  mediaDevices
} from 'react-native-webrtc';

const webrtcConfig = require('../webrtc.config');

class SFUService {
  constructor() {
    this.socket = null;
    this.peerConnections = new Map(); // Una connessione per ogni partecipante
    this.localStream = null;
    this.remoteStreams = new Map();
    this.roomId = null;
    this.userId = null;
    this.isConnected = false;
    this.participants = new Map();

    // Event listeners
    this.onParticipantJoined = null;
    this.onParticipantLeft = null;
    this.onStreamReceived = null;
    this.onStreamRemoved = null;
    this.onError = null;
  }

  // 🚀 Inizializza la connessione al SFU
  async initialize(userId) {
    try {
      console.log('🎥 Inizializzazione SFU Service...');

      this.userId = userId;

      // Connessione al server SFU SuperMicron
      this.socket = io(webrtcConfig.sfuConfig.url, {
        transports: webrtcConfig.sfuConfig.transports,
        reconnectionAttempts: webrtcConfig.sfuConfig.reconnectionAttempts,
        reconnectionDelay: webrtcConfig.sfuConfig.reconnectionDelay,
        timeout: webrtcConfig.sfuConfig.timeout,
        query: {
          userId: this.userId
        }
      });

      this._setupSocketEvents();

      return new Promise((resolve, reject) => {
        this.socket.on('connect', () => {
          console.log('✅ Connesso al SFU SuperMicron');
          this.isConnected = true;
          resolve(true);
        });

        this.socket.on('connect_error', (error) => {
          console.error('❌ Errore connessione SFU:', error);
          reject(error);
        });

        // Timeout di connessione
        setTimeout(() => {
          if (!this.isConnected) {
            reject(new Error('Timeout connessione SFU'));
          }
        }, webrtcConfig.sfuConfig.timeout);
      });

    } catch (error) {
      console.error('❌ Errore inizializzazione SFU:', error);
      throw error;
    }
  }

  // 🏠 Unisciti a una room per chiamata di gruppo
  async joinRoom(roomId, isVideoEnabled = true, isAudioEnabled = true) {
    try {
      console.log(`🏠 Unendosi alla room: ${roomId}`);

      this.roomId = roomId;

      // Ottieni stream locale
      this.localStream = await mediaDevices.getUserMedia({
        video: isVideoEnabled ? webrtcConfig.videoConstraints : false,
        audio: isAudioEnabled ? webrtcConfig.audioConstraints : false
      });

      return new Promise((resolve, reject) => {
        // Richiedi di unirti alla room
        this.socket.emit('join-room', {
          roomId,
          userId: this.userId,
          isVideoEnabled,
          isAudioEnabled
        });

        // Aspetta la risposta del server
        this.socket.once('room-joined', async (data) => {
          try {
            console.log('✅ Unito alla room:', data);

            // Setup connessioni WebRTC per ogni partecipante esistente
            if (data.participants && data.participants.length > 0) {
              for (const participantId of data.participants) {
                await this._createPeerConnection(participantId);
              }
            }

            resolve(data);
          } catch (error) {
            console.error('❌ Errore setup room:', error);
            reject(error);
          }
        });

        this.socket.once('room-join-error', (error) => {
          console.error('❌ Errore unione room:', error);
          reject(new Error(error.message));
        });

        // Timeout
        setTimeout(() => {
          reject(new Error('Timeout unione room'));
        }, 10000);
      });

    } catch (error) {
      console.error('❌ Errore joinRoom:', error);
      throw error;
    }
  }

  // 🚪 Lascia la room
  async leaveRoom() {
    try {
      console.log('🚪 Lasciando la room...');

      // Chiudi tutti i producers
      for (const producer of this.producers.values()) {
        producer.close();
      }
      this.producers.clear();

      // Chiudi tutti i consumers
      for (const consumer of this.consumers.values()) {
        consumer.close();
      }
      this.consumers.clear();

      // Chiudi i transport
      if (this.sendTransport) {
        this.sendTransport.close();
        this.sendTransport = null;
      }

      if (this.recvTransport) {
        this.recvTransport.close();
        this.recvTransport = null;
      }

      // Notifica il server
      if (this.socket && this.roomId) {
        this.socket.emit('leave-room', {
          roomId: this.roomId,
          userId: this.userId
        });
      }

      this.roomId = null;
      this.participants.clear();

      console.log('✅ Room lasciata con successo');

    } catch (error) {
      console.error('❌ Errore leaveRoom:', error);
      throw error;
    }
  }

  // 🎤 Attiva/disattiva audio
  async toggleAudio(enabled) {
    try {
      const audioProducer = this.producers.get('audio');
      if (audioProducer) {
        if (enabled) {
          await audioProducer.resume();
        } else {
          await audioProducer.pause();
        }

        console.log(`🎤 Audio ${enabled ? 'attivato' : 'disattivato'}`);
        return true;
      }
      return false;
    } catch (error) {
      console.error('❌ Errore toggle audio:', error);
      return false;
    }
  }

  // 📹 Attiva/disattiva video
  async toggleVideo(enabled) {
    try {
      const videoProducer = this.producers.get('video');
      if (videoProducer) {
        if (enabled) {
          await videoProducer.resume();
        } else {
          await videoProducer.pause();
        }

        console.log(`📹 Video ${enabled ? 'attivato' : 'disattivato'}`);
        return true;
      }
      return false;
    } catch (error) {
      console.error('❌ Errore toggle video:', error);
      return false;
    }
  }

  // 📊 Ottieni statistiche della chiamata
  getCallStats() {
    return {
      roomId: this.roomId,
      participantsCount: this.participants.size,
      participants: Array.from(this.participants.values()),
      producers: this.producers.size,
      consumers: this.consumers.size,
      isConnected: this.isConnected,
      device: this.device ? {
        loaded: this.device.loaded,
        rtpCapabilities: this.device.rtpCapabilities
      } : null
    };
  }

  // 🔌 Disconnetti dal SFU
  disconnect() {
    try {
      console.log('🔌 Disconnessione dal SFU...');

      if (this.roomId) {
        this.leaveRoom();
      }

      if (this.socket) {
        this.socket.disconnect();
        this.socket = null;
      }

      this.device = null;
      this.isConnected = false;

      console.log('✅ Disconnesso dal SFU');

    } catch (error) {
      console.error('❌ Errore disconnessione SFU:', error);
    }
  }

  // 🎯 Setup eventi socket privati
  _setupSocketEvents() {
    // Nuovo partecipante si unisce
    this.socket.on('participant-joined', (data) => {
      console.log('👤 Nuovo partecipante:', data);
      this.participants.set(data.userId, data);

      if (this.onParticipantJoined) {
        this.onParticipantJoined(data);
      }
    });

    // Partecipante lascia
    this.socket.on('participant-left', (data) => {
      console.log('👋 Partecipante uscito:', data);
      this.participants.delete(data.userId);

      // Rimuovi consumer associato
      const consumer = this.consumers.get(data.userId);
      if (consumer) {
        consumer.close();
        this.consumers.delete(data.userId);
      }

      if (this.onParticipantLeft) {
        this.onParticipantLeft(data);
      }
    });

    // Nuovo consumer disponibile (stream remoto)
    this.socket.on('new-consumer', async (data) => {
      try {
        console.log('📺 Nuovo consumer:', data);
        await this._consumeStream(data);
      } catch (error) {
        console.error('❌ Errore consume stream:', error);
      }
    });

    // Consumer chiuso
    this.socket.on('consumer-closed', (data) => {
      console.log('❌ Consumer chiuso:', data);
      const consumer = this.consumers.get(data.consumerId);
      if (consumer) {
        consumer.close();
        this.consumers.delete(data.consumerId);

        if (this.onStreamRemoved) {
          this.onStreamRemoved(data);
        }
      }
    });

    // Errori
    this.socket.on('error', (error) => {
      console.error('❌ Errore SFU:', error);
      if (this.onError) {
        this.onError(error);
      }
    });

    // Disconnessione
    this.socket.on('disconnect', () => {
      console.log('🔌 Disconnesso dal SFU');
      this.isConnected = false;
    });
  }

  // 🚛 Crea transport per inviare e ricevere media
  async _createTransports() {
    try {
      console.log('🚛 Creando transport...');

      // Crea send transport
      const sendTransportData = await this._requestTransport('send');
      this.sendTransport = this.device.createSendTransport(sendTransportData);

      // Setup eventi send transport
      this.sendTransport.on('connect', async ({ dtlsParameters }, callback, errback) => {
        try {
          await this._connectTransport(this.sendTransport.id, dtlsParameters);
          callback();
        } catch (error) {
          errback(error);
        }
      });

      this.sendTransport.on('produce', async ({ kind, rtpParameters }, callback, errback) => {
        try {
          const { id } = await this._produceMedia(kind, rtpParameters);
          callback({ id });
        } catch (error) {
          errback(error);
        }
      });

      // Crea recv transport
      const recvTransportData = await this._requestTransport('recv');
      this.recvTransport = this.device.createRecvTransport(recvTransportData);

      // Setup eventi recv transport
      this.recvTransport.on('connect', async ({ dtlsParameters }, callback, errback) => {
        try {
          await this._connectTransport(this.recvTransport.id, dtlsParameters);
          callback();
        } catch (error) {
          errback(error);
        }
      });

      console.log('✅ Transport creati con successo');

    } catch (error) {
      console.error('❌ Errore creazione transport:', error);
      throw error;
    }
  }

  // 📡 Richiedi transport dal server
  async _requestTransport(direction) {
    return new Promise((resolve, reject) => {
      this.socket.emit('create-transport', {
        roomId: this.roomId,
        direction
      });

      this.socket.once('transport-created', (data) => {
        resolve(data);
      });

      this.socket.once('transport-error', (error) => {
        reject(new Error(error.message));
      });

      setTimeout(() => {
        reject(new Error('Timeout creazione transport'));
      }, 5000);
    });
  }

  // 🔗 Connetti transport
  async _connectTransport(transportId, dtlsParameters) {
    return new Promise((resolve, reject) => {
      this.socket.emit('connect-transport', {
        roomId: this.roomId,
        transportId,
        dtlsParameters
      });

      this.socket.once('transport-connected', () => {
        resolve();
      });

      this.socket.once('transport-connect-error', (error) => {
        reject(new Error(error.message));
      });

      setTimeout(() => {
        reject(new Error('Timeout connessione transport'));
      }, 5000);
    });
  }

  // 🎬 Inizia a produrre media (audio/video)
  async _startProducing(isVideoEnabled, isAudioEnabled) {
    try {
      console.log('🎬 Iniziando produzione media...');

      // Ottieni stream locale
      const stream = await navigator.mediaDevices.getUserMedia({
        video: isVideoEnabled ? webrtcConfig.videoConstraints : false,
        audio: isAudioEnabled ? webrtcConfig.audioConstraints : false
      });

      // Produci audio
      if (isAudioEnabled) {
        const audioTrack = stream.getAudioTracks()[0];
        if (audioTrack) {
          const audioProducer = await this.sendTransport.produce({
            track: audioTrack,
            codecOptions: {
              opusStereo: true,
              opusDtx: true
            }
          });

          this.producers.set('audio', audioProducer);
          console.log('🎤 Audio producer creato');
        }
      }

      // Produci video
      if (isVideoEnabled) {
        const videoTrack = stream.getVideoTracks()[0];
        if (videoTrack) {
          const videoProducer = await this.sendTransport.produce({
            track: videoTrack,
            codecOptions: {
              videoGoogleStartBitrate: 1000
            }
          });

          this.producers.set('video', videoProducer);
          console.log('📹 Video producer creato');
        }
      }

      console.log('✅ Produzione media avviata');
      return stream;

    } catch (error) {
      console.error('❌ Errore produzione media:', error);
      throw error;
    }
  }

  // 📡 Produce media sul server
  async _produceMedia(kind, rtpParameters) {
    return new Promise((resolve, reject) => {
      this.socket.emit('produce', {
        roomId: this.roomId,
        kind,
        rtpParameters
      });

      this.socket.once('produced', (data) => {
        resolve(data);
      });

      this.socket.once('produce-error', (error) => {
        reject(new Error(error.message));
      });

      setTimeout(() => {
        reject(new Error('Timeout produzione media'));
      }, 5000);
    });
  }

  // 📺 Consuma stream remoto
  async _consumeStream(consumerData) {
    try {
      console.log('📺 Consumando stream:', consumerData);

      const consumer = await this.recvTransport.consume({
        id: consumerData.id,
        producerId: consumerData.producerId,
        kind: consumerData.kind,
        rtpParameters: consumerData.rtpParameters
      });

      this.consumers.set(consumerData.id, consumer);

      // Notifica il server che il consumer è pronto
      this.socket.emit('consumer-resume', {
        roomId: this.roomId,
        consumerId: consumerData.id
      });

      // Notifica l'app del nuovo stream
      if (this.onStreamReceived) {
        this.onStreamReceived({
          userId: consumerData.userId,
          kind: consumerData.kind,
          track: consumer.track,
          consumer
        });
      }

      console.log(`✅ Stream ${consumerData.kind} consumato da ${consumerData.userId}`);

    } catch (error) {
      console.error('❌ Errore consume stream:', error);
      throw error;
    }
  }
}

export default new SFUService();
