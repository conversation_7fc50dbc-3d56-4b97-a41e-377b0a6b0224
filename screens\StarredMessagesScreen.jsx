import AsyncStorage from '@react-native-async-storage/async-storage';
import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  SafeAreaView,
  StatusBar,
  Platform,
  Alert,
  ActivityIndicator,
  Image,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { useNavigation } from '@react-navigation/native';
// import * as Clipboard from 'expo-clipboard';
import useAuthStore from '../store/authStore';
import useChatStore from '../store/useChatStore';
import { format } from 'date-fns';
import { it } from 'date-fns/locale';
import UserAvatar from '../components/UserAvatar';
import { useTheme } from '../hooks/useTheme';

const StarredMessagesScreen = () => {
  const navigation = useNavigation();
  const { user } = useAuthStore();
  const { chats } = useChatStore();
  const { theme } = useTheme();
  const [starredMessages, setStarredMessages] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchStarredMessages();
  }, [user]);

  const fetchStarredMessages = async () => {
    if (!user) return;

    try {
      setLoading(true);

      // Ottieni tutti i messaggi contrassegnati come importanti
      const messagesRef = AsyncStorage.collection("messages");
      const q = query(
        messagesRef,
        where('starred', 'array-contains', user.uid),
        orderBy('timestamp', 'desc')
      );

      const querySnapshot = await get();

      const messages = [];
      querySnapshot.forEach((doc) => {
        const messageData = doc.data();

        // Trova la chat corrispondente per ottenere informazioni aggiuntive
        const chatId = messageData.chatId;
        const chat = chats.find(c => c.id === chatId);

        messages.push({
          id: doc.id,
          ...messageData,
          chatName: chat?.name || 'Chat',
          chatImage: chat?.image || null,
          isGroupChat: chat?.isGroupChat || false,
          sender: messageData.senderName || 'Utente',
          timestamp: messageData.timestamp?.toDate() || new Date(),
        });
      });

      setStarredMessages(messages);
    } catch (error) {
      console.error('Errore durante il recupero dei messaggi importanti:', error);
      Alert.alert('Errore', 'Impossibile caricare i messaggi importanti');
    } finally {
      setLoading(false);
    }
  };

  const formatMessageDate = (date) => {
    return format(date, 'dd MMM yyyy, HH:mm', { locale: it });
  };

  const handleMessagePress = (message) => {
    // Naviga alla chat corrispondente e mostra il messaggio
    navigation.navigate('ChatScreen', {
      chatId: message.chatId,
      highlightMessageId: message.id,
    });
  };

  const handleMessageLongPress = (message) => {
    Alert.alert(
      'Opzioni',
      '',
      [
        {
          text: 'Rimuovi dai preferiti',
          style: 'destructive',
          onPress: async () => {
            try {
              // Aggiorna il messaggio nel database rimuovendo l'utente dall'array 'starred'
              const messageRef = doc;
              await updateDoc(messageRef, {
                starred: arrayRemove(user.uid)
              });

              // Aggiorna lo stato locale
              setStarredMessages(starredMessages.filter(m => m.id !== message.id));

              Alert.alert('Successo', 'Messaggio rimosso dai preferiti');
            } catch (error) {
              console.error('Errore durante la rimozione del messaggio dai preferiti:', error);
              Alert.alert('Errore', 'Impossibile rimuovere il messaggio dai preferiti');
            }
          },
        },
        {
          text: 'Inoltra',
          onPress: () => Alert.alert('Info', 'Funzionalità in arrivo'),
        },
        {
          text: 'Copia',
          onPress: () => {
            // Clipboard.setString(message.text);
            Alert.alert('Info', 'Funzionalità in arrivo');
          },
        },
        {
          text: 'Annulla',
          style: 'cancel',
        },
      ]
    );
  };

  const renderMessageItem = ({ item }) => (
    <TouchableOpacity
      style={[styles.messageItem, { backgroundColor: theme.colors.card }]}
      onPress={() => handleMessagePress(item)}
      onLongPress={() => handleMessageLongPress(item)}
      delayLongPress={500}
    >
      <View style={styles.messageHeader}>
        <Text style={[styles.messageSender, { color: theme.colors.primary }]}>
          {item.sender}
        </Text>
        <Text style={[styles.messageTime, { color: theme.colors.textSecondary }]}>
          {formatMessageDate(item.timestamp)}
        </Text>
      </View>

      <Text style={[styles.messageText, { color: theme.colors.text }]}>
        {item.text}
      </Text>

      <View style={styles.messageFooter}>
        <Ionicons name="star" size={16} color={theme.colors.primary} />
      </View>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <StatusBar barStyle={theme.dark ? 'light-content' : 'dark-content'} backgroundColor={theme.colors.background} />

      <LinearGradient
        colors={theme.gradients.primary}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 0 }}
        style={styles.header}
      >
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color="#FFFFFF" />
        </TouchableOpacity>

        <Text style={styles.headerTitle}>Messaggi importanti</Text>
      </LinearGradient>

      {starredMessages.length > 0 ? (
        <FlatList
          data={starredMessages}
          renderItem={renderMessageItem}
          keyExtractor={item => item.id}
          contentContainerStyle={styles.listContent}
        />
      ) : (
        <View style={styles.emptyContainer}>
          <Ionicons name="star-outline" size={64} color={theme.colors.textSecondary} />
          <Text style={[styles.emptyText, { color: theme.colors.text }]}>
            Nessun messaggio importante
          </Text>
          <Text style={[styles.emptySubtext, { color: theme.colors.textSecondary }]}>
            Tocca e tieni premuto su un messaggio, poi tocca la stella per aggiungerlo ai preferiti
          </Text>
        </View>
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingTop: Platform.OS === 'ios' ? 10 : 16,
    paddingBottom: 16,
    height: Platform.OS === 'ios' ? 90 : 70,
  },
  backButton: {
    padding: 8,
    marginRight: 16,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  listContent: {
    padding: 16,
  },
  messageItem: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
  },
  messageHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  messageSender: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  messageTime: {
    fontSize: 12,
  },
  messageText: {
    fontSize: 15,
    lineHeight: 20,
    marginBottom: 8,
  },
  messageFooter: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtext: {
    fontSize: 14,
    textAlign: 'center',
    paddingHorizontal: 32,
  },
});

export default StarredMessagesScreen;
