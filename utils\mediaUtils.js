import axios from 'axios';
import { API_URL } from '../config/api';

/**
 * Carica un file multimediale sul server
 * @param {string} uri - URI del file locale
 * @param {string} type - Tipo di file ('image', 'video', 'audio')
 * @param {string} folder - Cartella di destinazione sul server
 * @returns {Promise<string>} URL del file caricato
 */
export const uploadMedia = async (uri, type = 'image', folder = 'status') => {
  try {
    // Crea un oggetto FormData per il caricamento multipart
    const formData = new FormData();
    
    // Determina il tipo MIME e l'estensione del file
    let fileType, ext;
    switch (type) {
      case 'image':
        fileType = 'image/jpeg';
        ext = 'jpg';
        break;
      case 'video':
        fileType = 'video/mp4';
        ext = 'mp4';
        break;
      case 'audio':
        fileType = 'audio/mpeg';
        ext = 'mp3';
        break;
      default:
        fileType = 'application/octet-stream';
        ext = 'bin';
    }
    
    // Crea un nome file univoco
    const fileName = `${folder}_${Date.now()}.${ext}`;
    
    // Aggiungi il file al FormData
    formData.append('file', {
      uri,
      name: fileName,
      type: fileType,
    });
    
    // Aggiungi metadati
    formData.append('type', type);
    formData.append('folder', folder);
    
    // Invia la richiesta al server
    const response = await axios.post(`${API_URL}/cloud/upload`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    
    // Restituisci l'URL del file caricato
    return response.data.url;
  } catch (error) {
    console.error('Errore durante il caricamento del file:', error);
    throw new Error('Impossibile caricare il file');
  }
};

/**
 * Elimina un file multimediale dal server
 * @param {string} url - URL del file da eliminare
 * @returns {Promise<boolean>} Esito dell'operazione
 */
export const deleteMedia = async (url) => {
  try {
    // Estrai il nome del file dall'URL
    const fileName = url.split('/').pop();
    
    // Invia la richiesta di eliminazione
    await axios.delete(`${API_URL}/media/delete`, {
      data: { fileName },
    });
    
    return true;
  } catch (error) {
    console.error('Errore durante l\'eliminazione del file:', error);
    throw new Error('Impossibile eliminare il file');
  }
};

/**
 * Ottiene un blob da un URI locale
 * @param {string} uri - URI del file locale
 * @returns {Promise<Blob>} Blob del file
 */
export const getBlob = async (uri) => {
  try {
    const response = await fetch(uri);
    return await response.blob();
  } catch (error) {
    console.error('Errore durante la conversione in blob:', error);
    throw new Error('Impossibile convertire il file in blob');
  }
};
