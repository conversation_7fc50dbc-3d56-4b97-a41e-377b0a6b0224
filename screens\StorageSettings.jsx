import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Switch,
  Alert,
  SafeAreaView,
  ActivityIndicator,
  Platform
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import useSettingsStore from '../store/settingsStore';
import Header from '../components/Header';
import { getStorageDetails, clearMediaCache } from '../services/storageService';

const StorageSettings = () => {
  const navigation = useNavigation();
  const { settings, updateSettings } = useSettingsStore();
  const [isLoading, setIsLoading] = useState(false);
  const [storageInfo, setStorageInfo] = useState({
    total: 0,
    photos: 0,
    videos: 0,
    audio: 0,
    documents: 0,
    other: 0,
    backup: 0,
    stories: 0
  });
  const [quotaInfo, setQuotaInfo] = useState({
    used: 0,
    limit: 1073741824, // 1GB in bytes
    available: 1073741824
  });

  useEffect(() => {
    loadStorageInfo();
  }, []);

  const loadStorageInfo = async () => {
    setIsLoading(true);
    try {
      console.log('Caricamento informazioni di archiviazione...');
      const data = await getStorageDetails();
      console.log('Dati di archiviazione ricevuti:', data);
      setStorageInfo(data.details);
      setQuotaInfo(data.quota);
    } catch (error) {
      console.error('Errore nel caricamento delle informazioni di archiviazione:', error);
      Alert.alert(
        'Errore',
        'Impossibile caricare le informazioni di archiviazione. Riprova più tardi.'
      );
      // Fallback a dati simulati in caso di errore
      setStorageInfo({
        total: 0,
        photos: 0,
        videos: 0,
        audio: 0,
        documents: 0,
        other: 0,
        backup: 0,
        stories: 0
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Formatta i bytes in una stringa leggibile (MB o GB)
  const formatSize = (bytes) => {
    if (bytes >= 1073741824) {
      return `${(bytes / 1073741824).toFixed(2)} GB`;
    } else {
      return `${(bytes / 1048576).toFixed(2)} MB`;
    }
  };

  // Calcola la percentuale di spazio utilizzato
  const calculatePercentage = () => {
    if (quotaInfo.limit === 0) return 0;
    return ((quotaInfo.used / quotaInfo.limit) * 100).toFixed(1);
  };

  // Funzione per aggiornare le impostazioni di download automatico
  const updateAutoDownloadSetting = (mediaType, value) => {
    try {
      setIsLoading(true);

      // Crea una copia profonda delle impostazioni
      const updatedSettings = JSON.parse(JSON.stringify(settings));

      // Assicurati che la struttura esista
      if (!updatedSettings.chat) updatedSettings.chat = {};
      if (!updatedSettings.chat.autoDownload) updatedSettings.chat.autoDownload = {};

      // Aggiorna l'impostazione
      updatedSettings.chat.autoDownload[mediaType] = value;

      // Aggiorna le impostazioni
      updateSettings(updatedSettings);
    } catch (error) {
      Alert.alert('Errore', 'Impossibile aggiornare le impostazioni');
    } finally {
      setIsLoading(false);
    }
  };

  const handleNetworkUsagePress = () => {
    // Implementare la visualizzazione dell'utilizzo della rete
    Alert.alert('Info', 'Funzionalità in arrivo');
  };

  const handleStorageUsagePress = () => {
    // Aggiorna i dati di archiviazione
    loadStorageInfo();
    Alert.alert('Info', 'Dati di archiviazione aggiornati');
  };

  const handleClearCachePress = () => {
    Alert.alert(
      'Svuota cache',
      'Sei sicuro di voler svuotare la cache? Questa azione non può essere annullata.',
      [
        {
          text: 'Annulla',
          style: 'cancel'
        },
        {
          text: 'Svuota',
          onPress: async () => {
            try {
              setIsLoading(true);
              const result = await clearMediaCache();
              Alert.alert('Successo', `Cache svuotata con successo. ${result.message}`);
              // Aggiorna le statistiche di archiviazione
              await loadStorageInfo();
            } catch (error) {
              console.error('Errore durante la pulizia della cache:', error);
              Alert.alert('Errore', 'Impossibile svuotare la cache. Riprova più tardi.');
            } finally {
              setIsLoading(false);
            }
          }
        }
      ]
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <Header
        title="Archiviazione e dati"
        showBack
        onBack={() => navigation.goBack()}
      />

      <ScrollView style={styles.scrollContainer}>
        <View style={styles.storageOverview}>
          <View style={styles.titleContainer}>
            <Text style={styles.storageTitle}>
              Spazio utilizzato: {formatSize(quotaInfo.used)} di {formatSize(quotaInfo.limit)} ({calculatePercentage()}%)
            </Text>
            <TouchableOpacity onPress={loadStorageInfo} disabled={isLoading}>
              <Ionicons name="refresh" size={24} color="#1E88E5" />
            </TouchableOpacity>
          </View>

          {isLoading ? (
            <ActivityIndicator size="small" color="#1E88E5" style={styles.loader} />
          ) : (
            <View style={styles.storageBarContainer}>
              <View style={styles.storageBar}>
                {storageInfo.total > 0 ? (
                  <>
                    <View style={[styles.storageBarSegment, styles.photosSegment, { flex: storageInfo.photos / storageInfo.total }]} />
                    <View style={[styles.storageBarSegment, styles.videosSegment, { flex: storageInfo.videos / storageInfo.total }]} />
                    <View style={[styles.storageBarSegment, styles.audioSegment, { flex: storageInfo.audio / storageInfo.total }]} />
                    <View style={[styles.storageBarSegment, styles.documentsSegment, { flex: storageInfo.documents / storageInfo.total }]} />
                    <View style={[styles.storageBarSegment, styles.otherSegment, { flex: storageInfo.other / storageInfo.total }]} />
                    <View style={[styles.storageBarSegment, styles.backupSegment, { flex: storageInfo.backup / storageInfo.total }]} />
                    <View style={[styles.storageBarSegment, styles.storiesSegment, { flex: storageInfo.stories / storageInfo.total }]} />
                  </>
                ) : (
                  <View style={[styles.storageBarSegment, styles.emptySegment, { flex: 1 }]} />
                )}
              </View>

              <View style={styles.storageLabels}>
                <View style={styles.storageLabel}>
                  <View style={[styles.storageLabelColor, styles.photosSegment]} />
                  <Text style={styles.storageLabelText}>Foto: {storageInfo.photos.toFixed(1)} MB</Text>
                </View>
                <View style={styles.storageLabel}>
                  <View style={[styles.storageLabelColor, styles.videosSegment]} />
                  <Text style={styles.storageLabelText}>Video: {storageInfo.videos.toFixed(1)} MB</Text>
                </View>
                <View style={styles.storageLabel}>
                  <View style={[styles.storageLabelColor, styles.audioSegment]} />
                  <Text style={styles.storageLabelText}>Audio: {storageInfo.audio.toFixed(1)} MB</Text>
                </View>
                <View style={styles.storageLabel}>
                  <View style={[styles.storageLabelColor, styles.documentsSegment]} />
                  <Text style={styles.storageLabelText}>Documenti: {storageInfo.documents.toFixed(1)} MB</Text>
                </View>
                <View style={styles.storageLabel}>
                  <View style={[styles.storageLabelColor, styles.otherSegment]} />
                  <Text style={styles.storageLabelText}>Altro: {storageInfo.other.toFixed(1)} MB</Text>
                </View>
                <View style={styles.storageLabel}>
                  <View style={[styles.storageLabelColor, styles.backupSegment]} />
                  <Text style={styles.storageLabelText}>Backup: {storageInfo.backup.toFixed(1)} MB</Text>
                </View>
                <View style={styles.storageLabel}>
                  <View style={[styles.storageLabelColor, styles.storiesSegment]} />
                  <Text style={styles.storageLabelText}>Storie: {storageInfo.stories.toFixed(1)} MB</Text>
                </View>
              </View>
            </View>
          )}
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Download automatico</Text>

          <View style={styles.settingItem}>
            <View style={styles.settingIconContainer}>
              <LinearGradient
                colors={['#1E88E5', '#D81B60']}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 0 }}
                style={styles.iconGradient}
              >
                <Ionicons name="image-outline" size={22} color="#FFFFFF" />
              </LinearGradient>
            </View>
            <View style={styles.settingContent}>
              <Text style={styles.settingLabel}>Foto</Text>
              <Switch
                value={settings.chat?.autoDownload?.photos ?? true}
                onValueChange={(value) => updateAutoDownloadSetting('photos', value)}
                trackColor={{ false: '#767577', true: '#81b0ff' }}
                thumbColor={settings.chat?.autoDownload?.photos ? '#1E88E5' : '#f4f3f4'}
              />
            </View>
          </View>

          <View style={styles.settingItem}>
            <View style={styles.settingIconContainer}>
              <LinearGradient
                colors={['#1E88E5', '#D81B60']}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 0 }}
                style={styles.iconGradient}
              >
                <Ionicons name="videocam-outline" size={22} color="#FFFFFF" />
              </LinearGradient>
            </View>
            <View style={styles.settingContent}>
              <Text style={styles.settingLabel}>Video</Text>
              <Switch
                value={settings.chat?.autoDownload?.videos ?? true}
                onValueChange={(value) => updateAutoDownloadSetting('videos', value)}
                trackColor={{ false: '#767577', true: '#81b0ff' }}
                thumbColor={settings.chat?.autoDownload?.videos ? '#1E88E5' : '#f4f3f4'}
              />
            </View>
          </View>

          <View style={styles.settingItem}>
            <View style={styles.settingIconContainer}>
              <LinearGradient
                colors={['#1E88E5', '#D81B60']}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 0 }}
                style={styles.iconGradient}
              >
                <Ionicons name="musical-note-outline" size={22} color="#FFFFFF" />
              </LinearGradient>
            </View>
            <View style={styles.settingContent}>
              <Text style={styles.settingLabel}>Audio</Text>
              <Switch
                value={settings.chat?.autoDownload?.audio ?? true}
                onValueChange={(value) => updateAutoDownloadSetting('audio', value)}
                trackColor={{ false: '#767577', true: '#81b0ff' }}
                thumbColor={settings.chat?.autoDownload?.audio ? '#1E88E5' : '#f4f3f4'}
              />
            </View>
          </View>

          <View style={styles.settingItem}>
            <View style={styles.settingIconContainer}>
              <LinearGradient
                colors={['#1E88E5', '#D81B60']}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 0 }}
                style={styles.iconGradient}
              >
                <Ionicons name="document-outline" size={22} color="#FFFFFF" />
              </LinearGradient>
            </View>
            <View style={styles.settingContent}>
              <Text style={styles.settingLabel}>Documenti</Text>
              <Switch
                value={settings.chat?.autoDownload?.documents ?? true}
                onValueChange={(value) => updateAutoDownloadSetting('documents', value)}
                trackColor={{ false: '#767577', true: '#81b0ff' }}
                thumbColor={settings.chat?.autoDownload?.documents ? '#1E88E5' : '#f4f3f4'}
              />
            </View>
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Gestione automatica</Text>

          <View style={styles.settingItem}>
            <View style={styles.settingIconContainer}>
              <LinearGradient
                colors={['#1E88E5', '#D81B60']}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 0 }}
                style={styles.iconGradient}
              >
                <Ionicons name="time-outline" size={22} color="#FFFFFF" />
              </LinearGradient>
            </View>
            <View style={styles.settingContent}>
              <Text style={styles.settingLabel}>Elimina media dopo 7 giorni</Text>
              <Switch
                value={settings.autoDeleteMedia !== false}
                onValueChange={(value) => {
                  // Aggiorna le impostazioni
                  const updatedSettings = { ...settings, autoDeleteMedia: value };
                  updateSettings(updatedSettings);
                }}
                trackColor={{ false: '#767577', true: '#81b0ff' }}
                thumbColor={settings.autoDeleteMedia !== false ? '#1E88E5' : '#f4f3f4'}
              />
            </View>
          </View>

          <Text style={styles.settingDescription}>
            I media ricevuti nelle chat verranno eliminati automaticamente dopo 7 giorni per risparmiare spazio.
            Le foto del profilo e i media salvati nella galleria non verranno eliminati.
          </Text>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Utilizzo dati</Text>

          <TouchableOpacity
            style={styles.settingItem}
            onPress={handleNetworkUsagePress}
          >
            <View style={styles.settingIconContainer}>
              <LinearGradient
                colors={['#1E88E5', '#D81B60']}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 0 }}
                style={styles.iconGradient}
              >
                <Ionicons name="cellular-outline" size={22} color="#FFFFFF" />
              </LinearGradient>
            </View>
            <View style={styles.settingContent}>
              <Text style={styles.settingLabel}>Utilizzo rete</Text>
              <Ionicons name="chevron-forward" size={20} color="#AAAAAA" />
            </View>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.settingItem}
            onPress={handleStorageUsagePress}
          >
            <View style={styles.settingIconContainer}>
              <LinearGradient
                colors={['#1E88E5', '#D81B60']}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 0 }}
                style={styles.iconGradient}
              >
                <Ionicons name="folder-outline" size={22} color="#FFFFFF" />
              </LinearGradient>
            </View>
            <View style={styles.settingContent}>
              <Text style={styles.settingLabel}>Gestione spazio</Text>
              <Ionicons name="chevron-forward" size={20} color="#AAAAAA" />
            </View>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.settingItem}
            onPress={handleClearCachePress}
          >
            <View style={styles.settingIconContainer}>
              <LinearGradient
                colors={['#1E88E5', '#D81B60']}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 0 }}
                style={styles.iconGradient}
              >
                <Ionicons name="trash-outline" size={22} color="#FFFFFF" />
              </LinearGradient>
            </View>
            <View style={styles.settingContent}>
              <Text style={styles.settingLabel}>Svuota cache</Text>
              <Ionicons name="chevron-forward" size={20} color="#AAAAAA" />
            </View>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#121212',
  },
  scrollContainer: {
    flex: 1,
    padding: 16,
  },
  storageOverview: {
    backgroundColor: '#1E1E1E',
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
  },
  storageTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
    marginBottom: 16,
  },
  loader: {
    marginVertical: 20,
  },
  storageBarContainer: {
    marginBottom: 8,
  },
  storageBar: {
    height: 20,
    flexDirection: 'row',
    borderRadius: 10,
    overflow: 'hidden',
    backgroundColor: '#2A2A2A',
    marginBottom: 16,
  },
  storageBarSegment: {
    height: '100%',
  },
  photosSegment: {
    backgroundColor: '#1E88E5',
  },
  videosSegment: {
    backgroundColor: '#D81B60',
  },
  audioSegment: {
    backgroundColor: '#43A047',
  },
  documentsSegment: {
    backgroundColor: '#FB8C00',
  },
  otherSegment: {
    backgroundColor: '#8E24AA',
  },
  backupSegment: {
    backgroundColor: '#9C27B0',
  },
  storiesSegment: {
    backgroundColor: '#673AB7',
  },
  emptySegment: {
    backgroundColor: '#E0E0E0',
  },
  titleContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  storageLabels: {
    marginTop: 8,
  },
  storageLabel: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  storageLabelColor: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 8,
  },
  storageLabelText: {
    fontSize: 14,
    color: '#FFFFFF',
  },
  section: {
    marginBottom: 24,
    backgroundColor: '#1E1E1E',
    borderRadius: 12,
    overflow: 'hidden',
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#AAAAAA',
    marginVertical: 12,
    paddingHorizontal: 16,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#2A2A2A',
  },
  settingIconContainer: {
    marginRight: 16,
  },
  iconGradient: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
  },
  settingDescription: {
    fontSize: 12,
    color: '#AAAAAA',
    paddingHorizontal: 16,
    paddingBottom: 16,
    paddingTop: 4,
    alignItems: 'center',
  },
  settingContent: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  settingLabel: {
    fontSize: 16,
    color: '#FFFFFF',
  },
  settingDescription: {
    fontSize: 14,
    color: '#AAAAAA',
    marginTop: 4,
  },
});

export default StorageSettings;
