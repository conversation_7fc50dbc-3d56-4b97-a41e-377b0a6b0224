import React, { createContext, useState, useContext, useEffect } from 'react';
// Importiamo il servizio di autenticazione OTP
import authService from '../services/authService';

export const AuthContext = createContext();

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const initAuth = async () => {
      try {
        console.log('AuthContext: Inizializzazione del servizio di autenticazione');
        // Inizializza il servizio di autenticazione
        const isAuthenticated = await authService.initialize();

        if (isAuthenticated) {
          console.log('AuthContext: Utente già autenticato');
          setUser(authService.getCurrentUser());
        } else {
          console.log('AuthContext: Nessun utente autenticato');
          setUser(null);
        }
      } catch (error) {
        console.error('Errore in AuthContext:', error);
        setUser(null);
      } finally {
        setLoading(false);
      }
    };

    // Aggiungi un listener per i cambiamenti di autenticazione
    authService.addAuthChangeListener(({ isAuthenticated, user }) => {
      console.log('AuthContext: Stato autenticazione cambiato', isAuthenticated, user);
      setUser(user);
    });

    initAuth();

    // Cleanup
    return () => {
      // Rimuovi il listener quando il componente viene smontato
      authService.removeAuthChangeListener(({ isAuthenticated, user }) => {
        console.log('AuthContext: Listener rimosso');
      });
    };
  }, []);

  const value = {
    user,
    loading,
    authService,
  };

  return (
    <AuthContext.Provider value={value}>
      {!loading && children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
