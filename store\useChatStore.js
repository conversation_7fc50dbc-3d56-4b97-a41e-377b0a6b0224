import AsyncStorage from '@react-native-async-storage/async-storage';
import { create } from 'zustand';
import axios from 'axios';
import { API_URL } from '../config/api';
import { sendChatNotification } from '../services/NotificationService';
import { authService } from '../services/authService';
import useAuthStore from './authStore';
import { Audio } from 'expo-av';

const useChatStore = create((set, get) => ({
  chats: [],
  currentChat: null,
  messages: [],
  loading: false,
  error: null,
  ephemeralCleanupInterval: null, // 🔧 Timer per messaggi effimeri

  // Carica le chat dell'utente dal server HP
  loadChats: async (userId) => {
    console.log('🔄 ChatStore: loadChats chiamato per userId:', userId);
    try {
      set({ loading: true, error: null });

      // Ottieni il token corretto (stesso metodo di groupStore)
      const token = await AsyncStorage.getItem('@trendychat:token');
      if (!token) {
        throw new Error('Token di autenticazione non trovato');
      }

      console.log('🔗 Caricando chat private dall\'endpoint groups:', `${API_URL}/groups`);

      // Carica i gruppi (che includono le chat private)
      const response = await axios.get(`${API_URL}/groups`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      console.log('📡 Risposta server groups:', response.status);

      if (response.data && response.data.groups) {
        // Filtra solo le chat private (logica WhatsApp: 2 membri = chat privata, 1 membro = chat con se stesso)
        const privateChats = response.data.groups.filter(group => {
          const memberCount = group.members?.length || 0;
          const isPrivateChat = group.description === 'Chat privata' || group.name?.startsWith('Chat con ');
          console.log(`🔍 CHATSTORE: Gruppo ${group.id || group._id} ha ${memberCount} membri, isPrivateChat: ${isPrivateChat}`);
          return isPrivateChat && (memberCount === 1 || memberCount === 2);  // ✅ CHAT PRIVATE: 1 o 2 membri
        });

        // Converte i gruppi privati in formato chat compatibile con HomeScreen
        const formattedChats = await Promise.all(privateChats.map(async (group) => {
          let participantName = group.name.replace('Chat con ', ''); // Nome iniziale dal gruppo

          // Cerca l'avatar aggiornato dell'utente dal server
          let updatedAvatar = group.photoURL;
          let otherUserId = 'unknown_user'; // Default fallback

          try {
            // Trova l'ID dell'altro utente (non quello corrente)
            const currentUserId = userId; // Usa l'ID passato alla funzione loadChats

            // Gestione speciale per chat con se stessi
            if (group.members?.length === 1 && group.members[0] === currentUserId) {
              // Chat con se stesso - usa lo stesso ID
              otherUserId = currentUserId;
              console.log(`👤 Chat con se stesso: ${group.id}, user=${currentUserId}`);
            } else {
              // Chat normale - trova l'altro utente
              otherUserId = group.members?.find(memberId => memberId !== currentUserId) || 'unknown_user';
            }

            console.log(`👥 Chat ${group.id}: currentUser=${currentUserId}, otherUser=${otherUserId}, members=`, group.members);

            if (otherUserId && otherUserId !== 'unknown_user') {
              // Cerca l'utente aggiornato dal server usando l'endpoint corretto
              const userResponse = await axios.get(`${API_URL}/users`, {
                headers: { 'Authorization': `Bearer ${token}` }
              });

              if (userResponse.data && userResponse.data.users) {
                // Trova l'utente specifico nella lista
                const userData = userResponse.data.users.find(u => u.id === otherUserId);
                if (userData) {
                  updatedAvatar = userData.photoURL || userData.avatar || group.photoURL;
                  const updatedName = userData.displayName || userData.name || participantName;
                  console.log(`🔄 Dati utente aggiornati per ${participantName}:`, {
                    nome: updatedName,
                    avatar: updatedAvatar,
                    status: userData.status
                  });

                  // ✅ AGGIORNA SEMPRE IL NOME DAL SERVER (PRIORITÀ ASSOLUTA)
                  participantName = updatedName;
                  console.log(`🔄 Nome aggiornato da server: "${updatedName}" (era: "${group.name.replace('Chat con ', '')}")`);
                } else {
                  console.log(`⚠️ Utente ${otherUserId} non trovato nella lista, uso dati cached`);
                }
              }
            }
          } catch (error) {
            console.log(`⚠️ Impossibile aggiornare avatar per ${participantName}, uso quello cached`);
          }

          console.log('📊 Dati chat formattati:', {
            id: group.id || group._id,
            participantName: participantName,
            updatedAvatar: updatedAvatar,
            originalGroupName: group.name
          });

          return {
            id: group.id || group._id,
            name: participantName, // ✅ USA IL NOME AGGIORNATO INVECE DI group.name
            // Crea participants nel formato che si aspetta HomeScreen
            participants: [
              { id: 'currentUser', name: 'Tu' },
              {
                id: otherUserId, // ✅ USA L'ID REALE DELL'ALTRO UTENTE
                name: participantName, // ✅ NOME AGGIORNATO
                avatar: updatedAvatar,
                photoURL: updatedAvatar
              }
            ],
            participantName: participantName, // ✅ NOME AGGIORNATO
            participantAvatar: updatedAvatar,
            photoURL: updatedAvatar,  // Per compatibilità con HomeScreen
            isPrivateChat: true,
            lastMessage: group.lastMessage || '',
            lastMessageTime: group.lastMessageTime ? new Date(group.lastMessageTime) : new Date(),
            unreadCount: 0,
            isGroup: false,
            isFavorite: false,
            messages: []
          };
        }));

        set({ chats: formattedChats, loading: false });
        console.log(`✅ Caricate ${formattedChats.length} chat private dal server`);
      } else {
        set({ chats: [], loading: false });
        console.log('✅ Nessuna chat privata trovata');
      }
    } catch (error) {
      console.error('❌ Errore nel caricamento delle chat:', error);
      set({ error: error.message, loading: false });
      throw error;
    }
  },

  // Carica i messaggi di una chat (RIMOSSO - USA LA VERSIONE SOTTO)

  // Crea una nuova chat (COME GRUPPO PRIVATO SUL SERVER HP)
  createChat: async (userId, participantId, participantName, participantAvatar) => {
    console.log('🔄 ChatStore: createChat chiamato per chat privata');
    const { user } = useAuthStore.getState();
    if (!user) {
      console.log('❌ ChatStore: Utente non autenticato');
      return;
    }

    try {
      set({ loading: true, error: null });

      // Ottieni il token di autenticazione
      const token = await AsyncStorage.getItem('@trendychat:token');
      if (!token) {
        throw new Error('Token di autenticazione non trovato');
      }

      // Crea la chat come "gruppo privato" sul server HP (logica WhatsApp: 2 membri)
      const chatData = {
        name: `Chat con ${participantName}`, // Nome descrittivo
        description: 'Chat privata',
        photoURL: participantAvatar || '',
        members: [userId, participantId], // ✅ ESATTAMENTE 2 MEMBRI (logica WhatsApp)
        isPublic: false // Chat privata
      };

      console.log('🔗 Creando chat privata come gruppo:', `${API_URL}/groups`);

      // Crea la chat sul server HP usando l'endpoint groups
      const response = await axios.post(`${API_URL}/groups`, chatData, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      console.log('📡 Chat privata creata con successo:', response.status);

      if (response.data && response.data.group) {
        const newChat = {
          id: response.data.group.id || response.data.group._id,
          name: response.data.group.name,
          participants: response.data.group.members,
          participantName: participantName,
          participantAvatar: participantAvatar,
          isPrivateChat: true,
          messages: []
        };

        // Aggiorna lo stato locale
        set({
          currentChat: newChat,
          chats: [newChat, ...get().chats],
          loading: false
        });

        console.log('✅ Chat privata aggiunta localmente');
        return newChat.id;
      }
    } catch (error) {
      console.error('❌ Errore nella creazione della chat privata:', error);
      set({ error: 'Impossibile creare la chat privata' });
      throw error;
    } finally {
      set({ loading: false });
    }
  },

  // Invia un messaggio (REPLICATO DALLA CHAT GRUPPO CHE FUNZIONA PERFETTAMENTE)
  sendMessage: async (chatId, userId, message) => {
    console.log('🔄 ChatStore: sendMessage chiamato per chat:', chatId);
    const { user } = useAuthStore.getState();
    if (!user) {
      console.log('❌ ChatStore: Utente non autenticato');
      return;
    }

    try {
      set({ loading: true, error: null });

      // Ottieni il token di autenticazione (STESSO METODO DELLA CHAT GRUPPO)
      const token = await AsyncStorage.getItem('@trendychat:token');
      if (!token) {
        throw new Error('Token di autenticazione non trovato');
      }

      console.log('🔗 Inviando messaggio alla chat:', `${API_URL}/groups/${chatId}/messages`);

      // Verifica che message sia un oggetto o stringa
      let messageText = '';
      if (typeof message === 'string') {
        messageText = message;
      } else if (message && typeof message === 'object') {
        messageText = message.text || '';
      }

      // 🔧 CONTROLLO MESSAGGI EFFIMERI
      const { currentChat } = get();
      let expiresAt = null;

      if (currentChat?.ephemeralMessages?.enabled && currentChat?.ephemeralMessages?.duration) {
        expiresAt = get().calculateExpirationTime(currentChat.ephemeralMessages.duration);
        console.log('⏰ Messaggio effimero - scadrà il:', expiresAt);
      }

      // Prepara i dati del messaggio (FORMATO ESATTO DELLE API CHAT DEL SERVER HP)
      const messageData = {
        text: messageText,
        type: 'text',
        ...(expiresAt && { expiresAt: expiresAt.toISOString() }) // Aggiungi scadenza se abilitata
      };

      // Invia il messaggio al server HP (ENDPOINT CORRETTO)
      const response = await axios.post(`${API_URL}/groups/${chatId}/messages`, messageData, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      console.log('📡 Messaggio inviato con successo:', response.status);

      if (response.data && response.data.message) {
        // Aggiorna i messaggi localmente per anteprima immediata (COME GRUPPO)
        const newMessage = {
          id: response.data.message.id || response.data.message._id,
          text: response.data.message.text,
          user: {
            _id: user.id || user.uid,
            name: user.displayName || user.name,
            avatar: user.photoURL || user.avatar
          },
          createdAt: new Date(response.data.message.createdAt || response.data.message.timestamp || Date.now()),
          type: 'text',
          senderId: user.id || user.uid,
          ...(expiresAt && { expiresAt: expiresAt.toISOString() }) // 🔧 Aggiungi scadenza lato app
        };

        // Aggiorna i messaggi della chat corrente (ANTEPRIMA IMMEDIATA)
        set({ messages: [newMessage, ...get().messages] });

        console.log('✅ Messaggio aggiunto localmente alla chat');
      }
    } catch (error) {
      console.error('❌ Errore nell\'invio del messaggio:', error);
      set({ error: 'Impossibile inviare il messaggio' });
      throw error;
    } finally {
      set({ loading: false });
    }
  },

  // Carica i messaggi di una chat (USANDO API CHAT DEL SERVER HP)
  loadMessages: async (chatId) => {
    console.log('🔄 ChatStore: loadMessages chiamato per chat:', chatId);
    const { user } = useAuthStore.getState();
    if (!user) {
      console.log('❌ ChatStore: Utente non autenticato');
      return;
    }

    try {
      set({ loading: true, error: null });

      // Ottieni il token di autenticazione
      const token = await AsyncStorage.getItem('@trendychat:token');
      if (!token) {
        throw new Error('Token di autenticazione non trovato');
      }

      console.log('🔗 Caricando messaggi dalla chat:', `${API_URL}/groups/${chatId}/messages`);

      // Carica i messaggi dal server HP (ENDPOINT CORRETTO)
      const response = await axios.get(`${API_URL}/groups/${chatId}/messages`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      console.log('📡 Messaggi ricevuti dal server:', response.status);

      if (response.data && response.data.success) {
        const messages = response.data.messages || [];

        // Converte i messaggi nel formato dell'app (COMPLETO COME WHATSAPP)
        const formattedMessages = messages.map(msg => {
          console.log(`🔍 Chat privata - Processando messaggio ${msg.type}:`, msg.id);

          // 🛠️ SISTEMA DI RICOSTRUZIONE URL PER PRODUZIONE (IDENTICO AL GROUPSTORE)
          let reconstructedMessage = {
            id: msg.id || msg._id,
            text: msg.text,
            user: {
              _id: msg.senderId || msg.user?._id,
              name: msg.senderName || msg.user?.name || 'Utente',
              avatar: msg.user?.avatar || ''
            },
            createdAt: new Date(msg.timestamp || msg.createdAt || Date.now()),
            type: msg.type || 'text',
            senderId: msg.senderId,
            // Campi base
            audioUrl: msg.audioUrl,
            duration: msg.duration || 0,
            imageUrl: msg.imageUrl,
            videoUrl: msg.videoUrl,
            thumbnail: msg.thumbnail,
            documentUrl: msg.documentUrl,
            fileName: msg.fileName
          };

          // 🔧 RICOSTRUZIONE URL MANCANTI (IDENTICA AL GROUPSTORE CHE FUNZIONA)
          if (!msg.audioUrl && msg.type === 'audio') {
            // Ricostruisci URL audio dal timestamp del messaggio (IDENTICO AL GROUPSTORE)
            const timestamp = new Date(msg.timestamp).getTime();
            reconstructedMessage.audioUrl = `${API_URL}/files/file-${timestamp}-audio.m4a`;
            reconstructedMessage.duration = 5; // Durata di default
            console.log(`🔧 Ricostruito audioUrl: ${reconstructedMessage.audioUrl}`);
          }

          if (!msg.imageUrl && msg.type === 'image') {
            // Ricostruisci URL immagine dal timestamp del messaggio (IDENTICO AL GROUPSTORE)
            const timestamp = new Date(msg.timestamp).getTime();
            reconstructedMessage.imageUrl = `${API_URL}/files/file-${timestamp}-image.jpg`;
            console.log(`🔧 Ricostruito imageUrl: ${reconstructedMessage.imageUrl}`);
          }

          if (!msg.videoUrl && msg.type === 'video') {
            // Ricostruisci URL video dal timestamp del messaggio (IDENTICO AL GROUPSTORE)
            const timestamp = new Date(msg.timestamp).getTime();
            reconstructedMessage.videoUrl = `${API_URL}/files/file-${timestamp}-video.mp4`;
            reconstructedMessage.thumbnail = `${API_URL}/files/file-${timestamp}-thumb.jpg`;
            console.log(`🔧 Ricostruito videoUrl: ${reconstructedMessage.videoUrl}`);
          }

          return reconstructedMessage;
        });

        // 🔍 FILTRA MESSAGGI NASCOSTI (LOGICA WHATSAPP)
        const hiddenMessagesKey = `@trendychat:hidden_messages_${chatId}`;
        const existingHidden = await AsyncStorage.getItem(hiddenMessagesKey);
        const hiddenMessages = existingHidden ? JSON.parse(existingHidden) : [];

        // Filtra i messaggi nascosti per questo utente
        let visibleMessages = formattedMessages.filter(msg => !hiddenMessages.includes(msg.id));

        // 🔧 FILTRA MESSAGGI EFFIMERI SCADUTI (LATO APP)
        const { currentChat } = get();
        if (currentChat?.ephemeralMessages?.enabled) {
          const now = new Date();
          const beforeFilter = visibleMessages.length;

          visibleMessages = visibleMessages.filter(msg => {
            if (!msg.expiresAt) return true;

            const expirationTime = new Date(msg.expiresAt);
            const isExpired = now > expirationTime;

            if (isExpired) {
              console.log('🗑️ Messaggio effimero scaduto filtrato:', msg.id, 'scaduto il:', expirationTime);
            }

            return !isExpired;
          });

          console.log(`⏰ Messaggi effimeri scaduti filtrati: ${beforeFilter - visibleMessages.length}`);
        }

        console.log(`🔍 Messaggi nascosti per questa chat: ${hiddenMessages.length}`);
        console.log(`✅ Messaggi visibili: ${visibleMessages.length}/${formattedMessages.length}`);

        set({ messages: visibleMessages.reverse(), loading: false });
        console.log(`✅ Caricati ${visibleMessages.length} messaggi per la chat ${chatId}`);
      }
    } catch (error) {
      console.error('❌ Errore nel caricamento messaggi chat:', error);
      set({ error: 'Impossibile caricare i messaggi' });
    } finally {
      set({ loading: false });
    }
  },

  setCurrentChat: (chat) => set({ currentChat: chat }),
  clearState: () => set({ chats: [], currentChat: null, messages: [] }),

  // Aggiorna le impostazioni della chat
  updateChatSettings: async (chatId, settings) => {
    try {
      if (!chatId) {
        console.warn('updateChatSettings: chatId mancante');
        return;
      }

      try {
        // Aggiorna le impostazioni tramite server HP
        await axios.put(`${API_URL}/groups/${chatId}/settings`, settings, {
          headers: {
            'Authorization': `Bearer ${await AsyncStorage.getItem('authToken')}`
          }
        });

        // Aggiorna lo stato locale
        const { chats } = get();
        const updatedChats = chats.map(chat => {
          if (chat.id === chatId) {
            return {
              ...chat,
              ...settings
            };
          }
          return chat;
        });
        set({ chats: updatedChats });

        // Se la chat corrente è quella modificata, aggiorna anche currentChat
        const { currentChat } = get();
        if (currentChat && currentChat.id === chatId) {
          set({ currentChat: { ...currentChat, ...settings } });
        }

        return true;
      } catch (dbError) {
        console.error('Errore nell\'aggiornamento delle impostazioni:', dbError);
        return false;
      }
    } catch (error) {
      console.error('Errore nell\'aggiornamento delle impostazioni della chat:', error);
      return false;
    }
  },

  // Gestione dello stato di digitazione
  setTypingStatus: async (chatId, userId, isTyping) => {
    try {
      if (!chatId || !userId) {
        console.warn('setTypingStatus: chatId o userId mancanti');
        return;
      }

      try {
        // Ottieni il token di autenticazione (ID utente per il server HP)
        let token = null;

        // Prima prova a ottenere l'ID utente da authStore
        try {
          // Usa l'import già presente in cima al file
          const authState = useAuthStore.getState();

          if (authState && authState.user && authState.user.id) {
            token = authState.user.id;
          }
        } catch (storeError) {
          // Fallback silenzioso
        }

        // Se non trova l'ID utente, prova con AsyncStorage
        if (!token) {
          token = await AsyncStorage.getItem('@trendychat:token') ||
                  await AsyncStorage.getItem('authToken');

          if (token && token.startsWith('jwt_token_')) {
            token = token.replace('jwt_token_', '');
          }
        }

        // Stato di digitazione disabilitato - endpoint non disponibile sul server HP
        // if (token) {
        //   await axios.post(`${API_URL}/chats/${chatId}/typing`, {
        //     userId,
        //     isTyping
        //   }, {
        //     headers: {
        //       'Authorization': `Bearer ${token}`
        //     }
        //   });
        // }
      } catch (dbError) {
        console.error('Errore nell\'aggiornamento dello stato di digitazione:', dbError);
      }

      // Aggiorna sempre lo stato locale
      const { chats } = get();
      const updatedChats = chats.map(chat => {
        if (chat.id === chatId) {
          return {
            ...chat,
            typingUsers: {
              ...chat.typingUsers,
              [userId]: isTyping ? new Date() : null
            }
          };
        }
        return chat;
      });
      set({ chats: updatedChats });
    } catch (error) {
      console.error('Errore nell\'aggiornamento dello stato di digitazione:', error);
    }
  },

  // Controlla se un utente sta digitando
  isUserTyping: (chatId, userId) => {
    const { chats } = get();
    const chat = chats.find(c => c.id === chatId);

    if (!chat || !chat.typingUsers) return false;

    const typingTimestamp = chat.typingUsers[userId];
    if (!typingTimestamp) return false;

    // Considera l'utente come "digitante" solo se l'ultimo aggiornamento è avvenuto negli ultimi 5 secondi
    const now = new Date();
    const typingTime = typingTimestamp.toDate ? typingTimestamp.toDate() : new Date(typingTimestamp);

    return now - typingTime < 5000; // 5 secondi
  },

  // 🔧 MESSAGGI EFFIMERI - LOGICA PRODUTTIVA

  // Calcola timestamp di scadenza basato sulla durata
  calculateExpirationTime: (duration) => {
    const now = new Date();
    switch (duration) {
      case '24h':
        return new Date(now.getTime() + 24 * 60 * 60 * 1000);
      case '7d':
        return new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000);
      case '90d':
        return new Date(now.getTime() + 90 * 24 * 60 * 60 * 1000);
      default:
        return null; // Nessuna scadenza
    }
  },

  // Controlla se un messaggio è scaduto
  isMessageExpired: (message, chatSettings) => {
    if (!chatSettings?.ephemeralMessages?.enabled || !message.expiresAt) {
      return false;
    }

    const now = new Date();
    const expirationTime = new Date(message.expiresAt);
    return now > expirationTime;
  },

  // Rimuove messaggi scaduti dalla chat
  removeExpiredMessages: (chatId) => {
    const { messages, chats } = get();
    const chat = chats.find(c => c.id === chatId);

    if (!chat?.ephemeralMessages?.enabled) {
      return;
    }

    const now = new Date();
    const validMessages = messages.filter(message => {
      if (!message.expiresAt) return true;

      const expirationTime = new Date(message.expiresAt);
      const isExpired = now > expirationTime;

      if (isExpired) {
        console.log('🗑️ Messaggio scaduto rimosso:', message.id, 'scaduto il:', expirationTime);
      }

      return !isExpired;
    });

    if (validMessages.length !== messages.length) {
      set({ messages: validMessages });
      console.log(`🧹 Rimossi ${messages.length - validMessages.length} messaggi scaduti dalla chat ${chatId}`);
    }
  },

  // Avvia controllo periodico messaggi scaduti
  startEphemeralMessageCleanup: () => {
    const { ephemeralCleanupInterval } = get();

    // Evita duplicati
    if (ephemeralCleanupInterval) {
      clearInterval(ephemeralCleanupInterval);
    }

    // Controlla ogni 30 secondi
    const interval = setInterval(() => {
      const { currentChat } = get();
      if (currentChat?.id) {
        get().removeExpiredMessages(currentChat.id);
      }
    }, 30000);

    set({ ephemeralCleanupInterval: interval });
    console.log('⏰ Avviato controllo periodico messaggi effimeri');
  },

  // Ferma controllo periodico
  stopEphemeralMessageCleanup: () => {
    const { ephemeralCleanupInterval } = get();
    if (ephemeralCleanupInterval) {
      clearInterval(ephemeralCleanupInterval);
      set({ ephemeralCleanupInterval: null });
      console.log('⏹️ Fermato controllo periodico messaggi effimeri');
    }
  },

  // Invia un messaggio audio (AGGIORNATO PER SERVER HP)
  sendAudioMessage: async (chatId, userId, audioUri) => {
    console.log('🎤 ChatStore: sendAudioMessage chiamato per chat:', chatId);
    const { user } = useAuthStore.getState();
    if (!user) {
      console.log('❌ ChatStore: Utente non autenticato');
      return;
    }

    try {
      set({ loading: true, error: null });

      // Ottieni il token di autenticazione
      const token = await AsyncStorage.getItem('@trendychat:token');
      if (!token) {
        throw new Error('Token di autenticazione non trovato');
      }

      console.log('📤 Caricando file audio sul server HP...');

      // Crea FormData per upload del file audio
      const formData = new FormData();
      formData.append('file', {
        uri: audioUri,
        name: `audio_${Date.now()}.aac`, // Cambiato estensione per coerenza
        type: 'audio/aac', // Cambiato da audio/m4a a audio/aac per compatibilità server
      });

      // Upload del file audio al server HP (con timeout esteso per audio lunghi)
      const uploadResponse = await axios.post(`${API_URL}/cloud/upload`, formData, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'multipart/form-data',
        },
        timeout: 300000, // 5 minuti per audio lunghi
      });

      console.log('📡 File audio caricato:', uploadResponse.status);
      console.log('📡 Risposta server upload:', uploadResponse.data);

      if (!uploadResponse.data || !uploadResponse.data.file || !uploadResponse.data.file.url) {
        throw new Error('Errore nel caricamento del file audio');
      }

      const audioUrl = uploadResponse.data.file.url;

      // CALCOLA DURATA REALE DELL'AUDIO
      let audioDuration = 0;
      try {
        console.log('⏱️ Calcolando durata audio...');
        const { sound } = await Audio.Sound.createAsync({ uri: audioUri });
        const status = await sound.getStatusAsync();
        if (status.isLoaded && status.durationMillis) {
          audioDuration = Math.round(status.durationMillis / 1000);
          console.log('✅ Durata audio calcolata:', audioDuration, 'secondi');
        }
        await sound.unloadAsync();
      } catch (error) {
        console.log('⚠️ Errore calcolo durata, uso default:', error.message);
        audioDuration = 0;
      }

      // Prepara i dati del messaggio audio
      const messageData = {
        text: '🎤 Messaggio vocale',
        type: 'audio',
        audioUrl: audioUrl,
        mediaUrl: audioUrl, // ← AGGIUNTO: per compatibilità con sistema di cancellazione
        duration: audioDuration
      };

      console.log('📤 Inviando messaggio audio alla chat:', `${API_URL}/groups/${chatId}/messages`);

      // Invia il messaggio al server HP
      const response = await axios.post(`${API_URL}/groups/${chatId}/messages`, messageData, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      console.log('📡 Messaggio audio inviato con successo:', response.status);

      if (response.data && response.data.message) {
        // Aggiorna i messaggi localmente per anteprima immediata (COME GRUPPO)
        const newMessage = {
          id: response.data.message.id || response.data.message._id,
          text: '🎤 Messaggio vocale',
          type: 'audio',
          audioUrl: audioUrl,
          duration: audioDuration,
          user: {
            _id: user.id || user.uid,
            name: user.displayName || user.name,
            avatar: user.photoURL || user.avatar
          },
          createdAt: new Date(response.data.message.createdAt || response.data.message.timestamp || Date.now()),
          senderId: user.id || user.uid
        };

        // Aggiorna i messaggi della chat corrente (ANTEPRIMA IMMEDIATA)
        set({ messages: [newMessage, ...get().messages] });

        console.log('✅ Messaggio audio aggiunto localmente alla chat');
      }
    } catch (error) {
      console.error('❌ Errore nell\'invio del messaggio audio:', error);
      set({ error: 'Impossibile inviare il messaggio audio' });
      throw error;
    } finally {
      set({ loading: false });
    }
  },

  // Invia un'immagine (SEGUENDO PATTERN sendAudioMessage)
  sendImageMessage: async (chatId, userId, imageUri) => {
    console.log('📷 ChatStore: sendImageMessage chiamato per chat:', chatId);
    const { user } = useAuthStore.getState();
    if (!user) {
      console.log('❌ ChatStore: Utente non autenticato');
      return;
    }

    try {
      set({ loading: true, error: null });

      // Ottieni il token di autenticazione
      const token = await AsyncStorage.getItem('@trendychat:token');
      if (!token) {
        throw new Error('Token di autenticazione non trovato');
      }

      console.log('📤 Caricando file immagine sul server HP...');

      // Crea FormData per upload del file immagine
      const formData = new FormData();
      formData.append('file', {
        uri: imageUri,
        name: `image_${Date.now()}.jpg`,
        type: 'image/jpeg',
      });

      // Upload del file immagine al server HP
      const uploadResponse = await axios.post(`${API_URL}/cloud/upload`, formData, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'multipart/form-data',
        },
      });

      console.log('📡 File immagine caricato:', uploadResponse.status);

      if (!uploadResponse.data || !uploadResponse.data.file) {
        throw new Error('Errore nel caricamento del file immagine');
      }

      const imageUrl = uploadResponse.data.file.url;

      // Prepara i dati del messaggio immagine
      const messageData = {
        text: '📷 Immagine',
        type: 'image',
        imageUrl: imageUrl,
        mediaUrl: imageUrl // ← AGGIUNTO: per compatibilità con sistema di cancellazione
      };

      console.log('📤 Inviando messaggio immagine alla chat:', `${API_URL}/groups/${chatId}/messages`);

      // Invia il messaggio al server HP
      const response = await axios.post(`${API_URL}/groups/${chatId}/messages`, messageData, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      console.log('📡 Messaggio immagine inviato con successo:', response.status);

      if (response.data && response.data.message) {
        // Aggiorna i messaggi localmente per anteprima immediata (COME GRUPPO)
        const newMessage = {
          id: response.data.message.id || response.data.message._id,
          text: '📷 Immagine',
          type: 'image',
          imageUrl: imageUrl,
          user: {
            _id: user.id || user.uid,
            name: user.displayName || user.name,
            avatar: user.photoURL || user.avatar
          },
          createdAt: new Date(response.data.message.createdAt || response.data.message.timestamp || Date.now()),
          senderId: user.id || user.uid
        };

        // Aggiorna i messaggi della chat corrente (ANTEPRIMA IMMEDIATA)
        set({ messages: [newMessage, ...get().messages] });

        console.log('✅ Messaggio immagine aggiunto localmente alla chat');
      }
    } catch (error) {
      console.error('❌ Errore nell\'invio dell\'immagine:', error);
      set({ error: 'Impossibile inviare l\'immagine' });
      throw error;
    } finally {
      set({ loading: false });
    }
  },

  // Invia un video (SEGUENDO PATTERN sendAudioMessage)
  sendVideoMessage: async (chatId, userId, videoUri, thumbnailUri) => {
    console.log('🎥 ChatStore: sendVideoMessage chiamato per chat:', chatId);
    const { user } = useAuthStore.getState();
    if (!user) {
      console.log('❌ ChatStore: Utente non autenticato');
      return;
    }

    try {
      set({ loading: true, error: null });

      // Ottieni il token di autenticazione
      const token = await AsyncStorage.getItem('@trendychat:token');
      if (!token) {
        throw new Error('Token di autenticazione non trovato');
      }

      console.log('📤 Caricando file video sul server HP...');

      // Crea FormData per upload del file video
      const formData = new FormData();
      formData.append('file', {
        uri: videoUri,
        name: `video_${Date.now()}.mp4`,
        type: 'video/mp4',
      });

      // Upload del file video al server HP (con timeout esteso)
      const uploadResponse = await axios.post(`${API_URL}/cloud/upload`, formData, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'multipart/form-data',
        },
        timeout: 300000, // 5 minuti per video
      });

      console.log('📡 File video caricato:', uploadResponse.status);

      if (!uploadResponse.data || !uploadResponse.data.file) {
        throw new Error('Errore nel caricamento del file video');
      }

      const videoUrl = uploadResponse.data.file.url;

      // Prepara i dati del messaggio video
      const messageData = {
        text: '🎥 Video',
        type: 'video',
        videoUrl: videoUrl,
        mediaUrl: videoUrl, // ← AGGIUNTO: per compatibilità con sistema di cancellazione
        thumbnail: thumbnailUri // Usa la thumbnail generata (IDENTICO AL GROUPSTORE)
      };

      console.log('📤 Inviando messaggio video alla chat:', `${API_URL}/groups/${chatId}/messages`);

      // Invia il messaggio al server HP
      const response = await axios.post(`${API_URL}/groups/${chatId}/messages`, messageData, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      console.log('📡 Messaggio video inviato con successo:', response.status);

      if (response.data && response.data.message) {
        // Aggiorna i messaggi localmente per anteprima immediata (COME GRUPPO)
        const newMessage = {
          id: response.data.message.id || response.data.message._id,
          text: '🎥 Video',
          type: 'video',
          videoUrl: videoUrl,
          thumbnail: thumbnailUri, // Usa la thumbnail generata
          user: {
            _id: user.id || user.uid,
            name: user.displayName || user.name,
            avatar: user.photoURL || user.avatar
          },
          createdAt: new Date(response.data.message.createdAt || response.data.message.timestamp || Date.now()),
          senderId: user.id || user.uid
        };

        // Aggiorna i messaggi della chat corrente (ANTEPRIMA IMMEDIATA)
        set({ messages: [newMessage, ...get().messages] });

        console.log('✅ Messaggio video aggiunto localmente alla chat');
      }
    } catch (error) {
      console.error('❌ Errore nell\'invio del video:', error);
      set({ error: 'Impossibile inviare il video' });
      throw error;
    } finally {
      set({ loading: false });
    }
  },

  // Invia un documento (SEGUENDO PATTERN sendAudioMessage)
  sendDocumentMessage: async (chatId, userId, documentUri, documentName, documentSize, documentType) => {
    console.log('📄 ChatStore: sendDocumentMessage chiamato per chat:', chatId);
    const { user } = useAuthStore.getState();
    if (!user) {
      console.log('❌ ChatStore: Utente non autenticato');
      return;
    }

    try {
      set({ loading: true, error: null });

      // Ottieni il token di autenticazione
      const token = await AsyncStorage.getItem('@trendychat:token');
      if (!token) {
        throw new Error('Token di autenticazione non trovato');
      }

      console.log('📤 Caricando file documento sul server HP...');

      // Crea FormData per upload del file documento
      const formData = new FormData();
      formData.append('file', {
        uri: documentUri,
        name: documentName || `document_${Date.now()}`,
        type: documentType || 'application/octet-stream',
      });

      // Upload del file documento al server HP
      const uploadResponse = await axios.post(`${API_URL}/cloud/upload`, formData, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'multipart/form-data',
        },
      });

      console.log('📡 File documento caricato:', uploadResponse.status);

      if (!uploadResponse.data || !uploadResponse.data.file) {
        throw new Error('Errore nel caricamento del file documento');
      }

      const documentUrl = uploadResponse.data.file.url;

      // Prepara i dati del messaggio documento
      const messageData = {
        text: `📄 ${documentName || 'Documento'}`,
        type: 'document',
        documentUrl: documentUrl,
        mediaUrl: documentUrl, // ← AGGIUNTO: per compatibilità con sistema di cancellazione
        fileName: documentName
      };

      console.log('📤 Inviando messaggio documento alla chat:', `${API_URL}/chats/${chatId}/messages`);

      // Invia il messaggio al server HP
      const response = await axios.post(`${API_URL}/chats/${chatId}/messages`, messageData, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      console.log('📡 Messaggio documento inviato con successo:', response.status);

      if (response.data && response.data.message) {
        // Aggiorna i messaggi localmente per anteprima immediata (COME GRUPPO)
        const newMessage = {
          id: response.data.message.id || response.data.message._id,
          text: `📄 ${documentName || 'Documento'}`,
          type: 'document',
          documentUrl: documentUrl,
          fileName: documentName,
          user: {
            _id: user.id || user.uid,
            name: user.displayName || user.name,
            avatar: user.photoURL || user.avatar
          },
          createdAt: new Date(response.data.message.createdAt || response.data.message.timestamp || Date.now()),
          senderId: user.id || user.uid
        };

        // Aggiorna i messaggi della chat corrente (ANTEPRIMA IMMEDIATA)
        set({ messages: [newMessage, ...get().messages] });

        console.log('✅ Messaggio documento aggiunto localmente alla chat');
      }
    } catch (error) {
      console.error('❌ Errore nell\'invio del documento:', error);
      set({ error: 'Impossibile inviare il documento' });
      throw error;
    } finally {
      set({ loading: false });
    }
  },

  // Cancella messaggio per tutti (STILE TRENDYCHAT) - SUPPORTA TUTTI I MEDIA
  deleteMessageForEveryone: async (chatId, messageId) => {
    console.log('🗑️ TrendyChat: Cancellando messaggio per tutti:', messageId);
    const { user } = useAuthStore.getState();
    if (!user) {
      console.log('❌ TrendyChat: Utente non autenticato');
      return;
    }

    try {
      set({ loading: true, error: null });

      // Trova il messaggio da cancellare per vedere il tipo
      const currentMessages = get().messages;
      const messageToDelete = currentMessages.find(msg => msg.id === messageId);

      if (messageToDelete) {
        console.log('📋 Tipo messaggio da cancellare:', messageToDelete.type);
        console.log('📋 Contenuto messaggio:', {
          text: messageToDelete.text,
          imageUrl: messageToDelete.imageUrl,
          videoUrl: messageToDelete.videoUrl,
          audioUrl: messageToDelete.audioUrl,
          documentUrl: messageToDelete.documentUrl
        });
      }

      // Ottieni il token di autenticazione
      const token = await AsyncStorage.getItem('@trendychat:token');
      if (!token) {
        throw new Error('Token di autenticazione non trovato');
      }

      console.log('📤 Cancellando messaggio sul server HP...');

      // Cancella il messaggio sul server HP (funziona per tutti i tipi) - ENDPOINT CORRETTO
      const response = await axios.delete(`${API_URL}/groups/${chatId}/messages/${messageId}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      console.log('📡 Messaggio cancellato dal server:', response.status);

      if (response.status === 200) {
        // Rimuovi il messaggio localmente (funziona per tutti i tipi)
        const updatedMessages = currentMessages.filter(msg => msg.id !== messageId);
        set({ messages: updatedMessages });

        console.log('✅ Messaggio cancellato per tutti (tipo:', messageToDelete?.type || 'unknown', ')');
      }
    } catch (error) {
      console.error('❌ Errore nella cancellazione del messaggio:', error);
      set({ error: 'Impossibile cancellare il messaggio' });
      throw error;
    } finally {
      set({ loading: false });
    }
  },

  // Cancella messaggio solo per me (STILE TRENDYCHAT)
  deleteMessageForMe: async (chatId, messageId) => {
    console.log('👤 TrendyChat: Nascondendo messaggio solo per me:', messageId);

    try {
      // Rimuovi il messaggio solo localmente (non dal server)
      const currentMessages = get().messages;
      const updatedMessages = currentMessages.filter(msg => msg.id !== messageId);
      set({ messages: updatedMessages });

      // Salva la lista dei messaggi nascosti in AsyncStorage
      const hiddenMessagesKey = `@trendychat:hidden_messages_${chatId}`;
      const existingHidden = await AsyncStorage.getItem(hiddenMessagesKey);
      const hiddenMessages = existingHidden ? JSON.parse(existingHidden) : [];

      if (!hiddenMessages.includes(messageId)) {
        hiddenMessages.push(messageId);
        await AsyncStorage.setItem(hiddenMessagesKey, JSON.stringify(hiddenMessages));
      }

      console.log('✅ Messaggio nascosto solo per te');
    } catch (error) {
      console.error('❌ Errore nel nascondere il messaggio:', error);
      throw error;
    }
  },

  // Svuota tutti i messaggi della chat (SISTEMA UNIFICATO HP)
  clearChatMessages: async (chatId) => {
    console.log('🧹 ChatStore: Svuotando tutti i messaggi della chat:', chatId);
    const { user } = useAuthStore.getState();
    if (!user) {
      console.log('❌ ChatStore: Utente non autenticato');
      return;
    }

    try {
      set({ loading: true, error: null });

      // Ottieni il token di autenticazione
      const token = await AsyncStorage.getItem('@trendychat:token');
      if (!token) {
        throw new Error('Token di autenticazione non trovato');
      }

      console.log('📤 Svuotando tutti i messaggi dal server HP...');

      // Chiama l'endpoint del server HP per svuotare tutti i messaggi
      const response = await axios.delete(`${API_URL}/groups/${chatId}/messages`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      console.log('📡 Messaggi svuotati dal server:', response.status);

      if (response.status === 200) {
        // Svuota i messaggi localmente
        set({ messages: [] });

        // Aggiorna anche la chat nella lista se presente
        const { chats } = get();
        const updatedChats = chats.map(chat => {
          if (chat.id === chatId) {
            return {
              ...chat,
              lastMessage: null,
              lastMessageTime: null,
              messages: []
            };
          }
          return chat;
        });

        set({ chats: updatedChats });

        console.log('✅ Chat svuotata completamente - messaggi e media eliminati dal server');
        return true;
      }

    } catch (error) {
      console.error('❌ Errore nello svuotamento della chat:', error);
      set({ error: 'Impossibile svuotare la chat' });
      throw error;
    } finally {
      set({ loading: false });
    }
  },

  // Cancella chat completamente (usa logica unificata server)
  deleteChat: async (chatId) => {
    console.log('🗑️ ChatStore: Cancellando chat:', chatId);

    try {
      set({ loading: true, error: null });

      // Ottieni il token di autenticazione
      const token = await AsyncStorage.getItem('@trendychat:token');
      if (!token) {
        throw new Error('Token di autenticazione non trovato');
      }

      console.log('📤 Cancellando chat privata dal server (logica WhatsApp)...');

      // Usa la logica unificata del server - endpoint groups
      const response = await axios.delete(`${API_URL}/groups/${chatId}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      console.log('📡 Chat cancellata dal server:', response.status);

      if (response.status === 200) {
        // Rimuovi la chat dalla lista locale
        const { chats } = get();
        const updatedChats = chats.filter(chat => chat.id !== chatId);

        // Aggiorna lo stato
        set({
          chats: updatedChats,
          currentChat: null
        });

        console.log('✅ Chat cancellata completamente');
        return true;
      }
    } catch (error) {
      console.error('❌ Errore nella cancellazione della chat:', error);
      set({ error: 'Impossibile cancellare la chat' });
      throw error;
    } finally {
      set({ loading: false });
    }
  },
}));

export default useChatStore;

// Esporto le variabili vuote per mantenere la compatibilità
export const db = null;
export const storage = null;