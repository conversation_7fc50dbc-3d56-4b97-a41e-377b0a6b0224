import { create } from 'zustand';
import AsyncStorage from '@react-native-async-storage/async-storage'; // Solo per userId
import axios from 'axios';

const API_URL = 'http://192.168.1.66:3001/api';

const useTrendyCoinStore = create((set, get) => ({
  // Stato - 3 SALDI SEPARATI
  adsBalance: 0,          // Saldo Pubblicità (da payout Earn)
  liveBalance: 0,         // Saldo Live (da regali ricevuti)
  balance: 0,             // Saldo Disponibile (per prelievo finale)
  availableCoins: 0,      // Alias per compatibilità (deprecato)
  pendingCoins: 0,        // TrendyCoin in attesa di liquidazione
  totalEarned: 0,         // Totale guadagnato (storico)
  totalSpent: 0,          // Totale speso (storico)
  weeklyEarned: 0,        // Guadagnato questa settimana (da trasferire)
  todayEarned: 0,         // Guadagnato oggi
  lastPayout: null,       // Data ultimo pagamento
  nextPayout: null,       // Data prossimo pagamento
  loading: false,
  error: null,

  // Carica dati TrendyCoin dal server
  loadTrendyCoins: async () => {
    try {
      set({ loading: true, error: null });

      // Ottieni userId dal localStorage (sistema TrendyChat)
      const userData = await AsyncStorage.getItem('@trendychat:user');
      if (!userData) {
        throw new Error('Dati utente non trovati');
      }

      const user = JSON.parse(userData);
      const userId = user.id;

      // console.log('💰 TrendyCoinStore: Caricamento dati dal server HP...'); // Rimosso per evitare spam ogni 30s

      const response = await axios.get(`${API_URL}/user/trendycoins/${userId}`);

      if (response.status === 200 && response.data.success) {
        const data = response.data.data;

        set({
          adsBalance: Math.round((data.adsBalance || 0) * 10) / 10,
          liveBalance: Math.round((data.liveBalance || 0) * 10) / 10,
          balance: Math.round((data.balance || 0) * 10) / 10,
          availableCoins: Math.round((data.balance || 0) * 10) / 10, // Alias per compatibilità
          pendingCoins: 0, // Il server non ha pendingCoins
          totalEarned: Math.round((data.totalEarned || 0) * 10) / 10,
          totalSpent: Math.round((data.totalSpent || 0) * 10) / 10,
          weeklyEarned: Math.round((data.weeklyEarned || 0) * 10) / 10,
          todayEarned: 0, // Il server non ha todayEarned
          lastPayout: data.lastPayout ? new Date(data.lastPayout) : null,
          nextPayout: get().calculateNextPayout(),
          loading: false,
          error: null
        });

        // Salva cache locale per offline
        await AsyncStorage.setItem('@trendychat:trendycoins', JSON.stringify({
          adsBalance: Math.round((data.adsBalance || 0) * 10) / 10,
          liveBalance: Math.round((data.liveBalance || 0) * 10) / 10,
          balance: Math.round((data.balance || 0) * 10) / 10,
          weeklyEarned: Math.round((data.weeklyEarned || 0) * 10) / 10,
          lastSync: new Date().toISOString()
        }));

        // console.log('✅ TrendyCoin sincronizzati'); // Rimosso per evitare spam ogni 30s
      } else {
        throw new Error('Risposta server non valida');
      }
    } catch (error) {
      console.error('❌ Errore caricamento TrendyCoin:', error);

      // Prova a caricare cache locale
      try {
        const cachedData = await AsyncStorage.getItem('@trendychat:trendycoins');
        if (cachedData) {
          const cache = JSON.parse(cachedData);
          set({
            adsBalance: cache.adsBalance || 0,
            liveBalance: cache.liveBalance || 0,
            balance: cache.balance || 0,
            weeklyEarned: cache.weeklyEarned || 0,
            loading: false,
            error: 'Modalità offline - Dati dalla cache locale'
          });
          console.log('📱 Caricati dati dalla cache locale');
          return;
        }
      } catch (cacheError) {
        console.error('❌ Errore cache:', cacheError);
      }

      set({
        error: 'Impossibile caricare i TrendyCoin. Verifica la connessione.',
        loading: false
      });
    }
  },

  // Aggiungi TrendyCoin guadagnati (CON VALIDAZIONE ANTI-CHEAT)
  addEarnedCoins: async (coins, source = 'video_ads', videoWatchTime = 0, sessionId = null) => {
    try {
      // Ottieni userId dal localStorage (sistema TrendyChat)
      const userData = await AsyncStorage.getItem('@trendychat:user');
      if (!userData) {
        throw new Error('Dati utente non trovati');
      }

      const user = JSON.parse(userData);
      const userId = user.id;

      // Genera sessionId se non fornito
      if (!sessionId) {
        sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      }

      console.log(`💰 Aggiungendo ${coins} TrendyCoin da ${source}...`);
      console.log(`🛡️ VideoWatchTime: ${videoWatchTime}s, SessionId: ${sessionId}`);

      const requestData = {
        amount: coins,
        source,
        videoWatchTime,
        sessionId
      };

      const response = await axios.post(`${API_URL}/user/trendycoins/${userId}/earn`, requestData);

      if (response.status === 200 && response.data.success) {
        const data = response.data.data;

        set({
          balance: Math.round((data.balance || 0) * 10) / 10,
          availableCoins: Math.round((data.balance || 0) * 10) / 10, // Alias per compatibilità
          weeklyEarned: Math.round((data.weeklyEarned || 0) * 10) / 10,
          totalEarned: Math.round((data.totalEarned || 0) * 10) / 10,
          todayEarned: Math.round((get().todayEarned + coins) * 10) / 10, // Incrementa todayEarned
        });

        console.log(`✅ ${coins} TrendyCoin aggiunti - Nuovo balance: ${data.balance}`);
        console.log(`📊 Guadagni rimanenti oggi: ${data.dailyEarningsLeft || 'N/A'}`);

        return {
          success: true,
          data: data
        };
      }
    } catch (error) {
      console.error('❌ Errore aggiunta TrendyCoin:', error);

      // Gestisci errori specifici del server
      if (error.response && error.response.data) {
        const serverError = error.response.data;
        console.log(`🚨 Errore server: ${serverError.message}`);

        // Rilancia con messaggio specifico del server
        throw new Error(serverError.message || 'Errore del server');
      }

      throw error; // Propaga l'errore generico
    }
  },

  // Spendi TrendyCoin (solo da available)
  spendCoins: async (coins, purpose = 'live_gift') => {
    try {
      const { availableCoins } = get();

      if (availableCoins < coins) {
        throw new Error('TrendyCoin insufficienti');
      }

      // Ottieni userId dal localStorage (sistema TrendyChat)
      const userData = await AsyncStorage.getItem('@trendychat:user');
      if (!userData) {
        throw new Error('Dati utente non trovati');
      }

      const user = JSON.parse(userData);
      const userId = user.id;

      console.log(`💸 Spendendo ${coins} TrendyCoin per ${purpose}...`);

      const response = await axios.post(`${API_URL}/user/trendycoins/${userId}/spend`, {
        amount: coins,
        purpose
      });

      if (response.status === 200 && response.data.success) {
        const data = response.data.data;

        set({
          balance: data.balance || 0,
          availableCoins: data.balance || 0, // Alias per compatibilità
          totalSpent: data.totalSpent || 0,
        });

        console.log(`✅ ${coins} TrendyCoin spesi per ${purpose} - Nuovo balance: ${data.balance}`);
        return true;
      }
    } catch (error) {
      console.error('❌ Errore spesa TrendyCoin:', error);
      throw error;
    }
  },

  // Calcola prossima data di pagamento (domenica)
  calculateNextPayout: () => {
    const now = new Date();
    const nextSunday = new Date(now);

    // Trova la prossima domenica
    const daysUntilSunday = (7 - now.getDay()) % 7;
    nextSunday.setDate(now.getDate() + (daysUntilSunday === 0 ? 7 : daysUntilSunday));
    nextSunday.setHours(0, 0, 0, 0);

    return nextSunday;
  },

  // Controlla se è ora del pagamento settimanale
  checkWeeklyPayout: async () => {
    try {
      const { nextPayout, pendingCoins } = get();
      const now = new Date();

      if (nextPayout && now >= nextPayout && pendingCoins > 0) {
        console.log('💰 È ora del pagamento settimanale!');
        await get().processWeeklyPayout();
      }
    } catch (error) {
      console.error('❌ Errore controllo pagamento:', error);
    }
  },

  // Processa pagamento settimanale
  processWeeklyPayout: async () => {
    try {
      // Ottieni userId dal localStorage (sistema TrendyChat)
      const userData = await AsyncStorage.getItem('@trendychat:user');
      if (!userData) {
        throw new Error('Dati utente non trovati');
      }

      const user = JSON.parse(userData);
      const userId = user.id;

      console.log('💰 Processando pagamento settimanale...');

      const response = await axios.post(`${API_URL}/user/trendycoins/${userId}/payout`);

      if (response.status === 200 && response.data.success) {
        const data = response.data.data;

        set({
          adsBalance: Math.round((data.adsBalance || get().adsBalance) * 10) / 10,
          liveBalance: Math.round((data.liveBalance || get().liveBalance) * 10) / 10,
          balance: Math.round((data.balance || get().balance) * 10) / 10,
          availableCoins: Math.round((data.balance || get().balance) * 10) / 10, // Alias per compatibilità
          pendingCoins: 0,
          weeklyEarned: Math.round((data.weeklyEarned || 0) * 10) / 10,
          lastPayout: data.lastPayout ? new Date(data.lastPayout) : new Date(),
          nextPayout: get().calculateNextPayout(),
        });

        console.log(`✅ Pagamento settimanale completato: ${data.payoutAmount} TrendyCoin`);
        return data.payoutAmount;
      }
    } catch (error) {
      console.error('❌ Errore pagamento settimanale:', error);
      throw error;
    }
  },

  // Reset errore
  clearError: () => set({ error: null }),

  // Ricarica dati dal server (per refresh)
  refreshTrendyCoins: async () => {
    console.log('🔄 Ricaricando TrendyCoin dal server...');
    await get().loadTrendyCoins();
  },
}));

export default useTrendyCoinStore;
