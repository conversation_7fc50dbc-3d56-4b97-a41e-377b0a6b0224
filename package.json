{"name": "trendychat", "version": "1.0.0", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "node exclude-permissions.js && expo start", "android": "node exclude-permissions.js && expo start --android", "ios": "node exclude-permissions.js && expo start --ios", "web": "node exclude-permissions.js && expo start --web", "exclude-permissions": "node exclude-permissions.js", "prebuild": "node exclude-permissions.js"}, "dependencies": {"@bam.tech/react-native-image-resizer": "^3.0.11", "@expo/vector-icons": "^14.0.0", "@react-native-async-storage/async-storage": "^1.23.1", "@react-native-community/slider": "^4.5.6", "@react-native-google-signin/google-signin": "^14.0.1", "@react-navigation/bottom-tabs": "^6.6.1", "@react-navigation/native": "^6.1.18", "@react-navigation/native-stack": "^6.11.0", "axios": "^1.9.0", "crypto-js": "^4.2.0", "date-fns": "^2.30.0", "dotenv": "^16.5.0", "expo": "~52.0.0", "expo-av": "~15.0.2", "expo-barcode-scanner": "^13.0.1", "expo-build-properties": "^0.13.2", "expo-camera": "~16.0.18", "expo-contacts": "~14.0.5", "expo-document-picker": "~13.0.3", "expo-file-system": "^18.0.12", "expo-font": "^13.0.4", "expo-haptics": "^14.0.1", "expo-image-manipulator": "~13.0.6", "expo-image-picker": "~16.0.6", "expo-linear-gradient": "^14.0.2", "expo-location": "~18.0.10", "expo-media-library": "~17.0.6", "expo-notifications": "~0.29.14", "expo-secure-store": "^14.2.3", "expo-sharing": "^13.1.5", "expo-splash-screen": "^0.29.24", "expo-status-bar": "~2.0.1", "expo-video-thumbnails": "~9.0.3", "expo-web-browser": "^14.1.6", "glob": "^11.0.2", "google-auth-library": "^9.15.1", "googleapis": "^148.0.0", "jszip": "^3.10.1", "lottie-react-native": "^7.1.0", "mediasoup-client": "^3.11.0", "react": "18.3.1", "react-native": "^0.76.9", "react-native-animatable": "^1.3.3", "react-native-audio-recorder-player": "^3.6.12", "react-native-crypto-js": "^1.0.0", "react-native-fast-image": "^8.6.3", "react-native-fs": "^2.20.0", "react-native-gesture-handler": "~2.20.2", "react-native-get-random-values": "^1.11.0", "react-native-gifted-chat": "^2.4.0", "react-native-image-filter-kit": "^0.8.0", "react-native-linear-gradient": "^2.8.3", "react-native-modal": "^14.0.0-rc.1", "react-native-paper": "^5.13.1", "react-native-qrcode-svg": "^6.3.15", "react-native-reanimated": "~3.16.1", "react-native-safe-area-context": "^4.14.1", "react-native-screens": "~4.4.0", "react-native-sound": "^0.11.2", "react-native-svg": "^15.8.0", "react-native-toast-message": "^2.3.0", "react-native-video": "^6.13.0", "react-native-video-processing": "^1.7.2", "react-native-vision-camera": "^4.6.4", "react-native-webrtc": "^118.0.7", "react-native-webview": "^13.14.1", "react-native-worklets-core": "^1.5.0", "react-native-zip-archive": "^7.0.1", "rn-emoji-keyboard": "^1.7.0", "socket.io-client": "^4.8.1", "tslib": "^2.6.2", "zustand": "^4.4.1"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/plugin-proposal-export-namespace-from": "^7.18.9", "@types/react": "~18.3.12", "babel-plugin-module-resolver": "^5.0.2", "eas-cli": "^16.3.1", "react-native-svg-transformer": "^1.5.0", "typescript": "^5.3.3"}, "private": true, "expo": {"doctor": {"reactNativeDirectoryCheck": {"exclude": ["react-native-webrtc"], "listUnknownPackages": false}}}}