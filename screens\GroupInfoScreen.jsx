import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Image,
  ActivityIndicator,
  Alert,
  SafeAreaView,
  StatusBar,
  Platform,
  Dimensions,
  Switch,
  ScrollView,
} from 'react-native';
import { useRoute, useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import * as ImagePicker from 'expo-image-picker';
import AsyncStorage from '@react-native-async-storage/async-storage';
import axios from 'axios';
import useGroupStore from '../store/groupStore';
import useAuthStore from '../store/authStore';
import useWebRTCStore from '../store/webrtcStore';
import { io } from 'socket.io-client';

const API_URL = 'http://192.168.1.66:3001/api';

const { width, height } = Dimensions.get('window');

const GroupInfoScreen = () => {
  const route = useRoute();
  const navigation = useNavigation();
  const { groupId } = route.params;
  const { user } = useAuthStore();
  const { currentGroup, loading, error, loadGroup, updateGroup, leaveGroup, addMember, removeMember, deleteGroup } = useGroupStore();
  const { startCall } = useWebRTCStore();
  const [notifications, setNotifications] = useState(true);
  const [mediaVisibility, setMediaVisibility] = useState(true);

  // 📡 WEBSOCKET STATI GRUPPO INFO
  const [socket, setSocket] = useState(null);
  const [groupOnlineUsers, setGroupOnlineUsers] = useState([]);

  useEffect(() => {
    loadGroup(groupId);
    // 📡 CONNETTI WEBSOCKET PER STATI GRUPPO INFO
    connectGroupInfoWebSocket();
  }, [groupId]);

  // 📡 CONNESSIONE WEBSOCKET PER STATI GRUPPO INFO
  const connectGroupInfoWebSocket = () => {
    if (socket) {
      socket.disconnect();
    }

    const newSocket = io('http://192.168.1.66:3001');

    newSocket.on('connect', () => {

      // Autentica per gruppo info
      newSocket.emit('join_group', {
        groupId,
        userId: user?.id || user?.uid,
        userName: user?.displayName || 'Utente'
      });

      // Imposta te stesso come online
      setGroupOnlineUsers(prev => {
        const myUserId = user?.id || user?.uid;
        if (!prev.find(u => u.userId === myUserId)) {
          return [...prev, { userId: myUserId, userName: user?.displayName || 'Tu' }];
        }
        return prev;
      });
    });

    // Ricevi lista utenti online gruppo
    newSocket.on('group_online_users', (data) => {
      console.log('🟢 Ricevuto utenti online gruppo info:', data);
      if (data.groupId === groupId) {
        setGroupOnlineUsers(data.users || []);
      }
    });

    // Ricevi utente che entra nel gruppo
    newSocket.on('group_user_joined', (data) => {
      console.log('🚪 Utente entrato nel gruppo info:', data);
      if (data.groupId === groupId && data.userId !== (user?.id || user?.uid)) {
        setGroupOnlineUsers(prev => {
          if (!prev.find(u => u.userId === data.userId)) {
            return [...prev, { userId: data.userId, userName: data.userName }];
          }
          return prev;
        });
      }
    });

    // Ricevi utente che esce dal gruppo
    newSocket.on('group_user_left', (data) => {
      console.log('🚪 Utente uscito dal gruppo info:', data);
      if (data.groupId === groupId) {
        setGroupOnlineUsers(prev => prev.filter(u => u.userId !== data.userId));
      }
    });

    newSocket.on('disconnect', () => {
      console.log('🔴 WebSocket gruppo info disconnesso');
    });

    setSocket(newSocket);
  };

  // 🧹 CLEANUP WEBSOCKET GRUPPO INFO
  useEffect(() => {
    return () => {
      console.log('🚪 USCENDO DAL GRUPPO INFO:', groupId);

      if (socket) {
        console.log('🧹 Disconnettendo WebSocket gruppo info...');
        socket.emit('leave_group', {
          groupId,
          userId: user?.id || user?.uid,
          userName: user?.displayName || 'Utente'
        });
        socket.disconnect();
        console.log('📡 Evento leave_group info inviato');
      }
    };
  }, [socket, groupId, user]);

  // Controllo admin robusto - supporta sia user.uid che user.id
  const currentUserId = user?.id || user?.uid;
  const isAdmin = currentGroup?.admins?.includes(currentUserId);

  // Helper per ottenere il nome del membro dall'ID
  const getMemberNameById = (memberId) => {
    // Se è l'utente corrente
    if (memberId === currentUserId) {
      return user?.displayName || user?.name || 'Tu';
    }

    // Per ora usa l'ID abbreviato, in futuro si può implementare una cache dei nomi
    return null;
  };

  const handleChangeGroupPhoto = async () => {
    try {
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permesso negato', 'Permesso di accesso alla galleria negato');
        return;
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.8,
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        await updateGroup(groupId, { photoURL: result.assets[0].uri });
      }
    } catch (error) {
      console.error("Errore nella modifica dell'immagine:", error);
      Alert.alert('Errore', 'Impossibile modificare l\'immagine del gruppo');
    }
  };

  const handleLeaveGroup = () => {
    Alert.alert(
      'Lascia gruppo',
      'Sei sicuro di voler lasciare questo gruppo?',
      [
        { text: 'Annulla', style: 'cancel' },
        {
          text: 'Lascia',
          style: 'destructive',
          onPress: async () => {
            try {
              await leaveGroup(groupId);
              navigation.goBack();
            } catch (error) {
              Alert.alert('Errore', 'Impossibile lasciare il gruppo');
            }
          }
        },
      ]
    );
  };

  const handleAddMember = () => {
    navigation.navigate('AddGroupMembers', { groupId });
  };

  const handleRemoveMember = (memberId) => {
    if (!isAdmin) {
      Alert.alert('Errore', 'Solo gli amministratori possono rimuovere i membri');
      return;
    }

    Alert.alert(
      'Rimuovi membro',
      'Sei sicuro di voler rimuovere questo membro dal gruppo?',
      [
        { text: 'Annulla', style: 'cancel' },
        {
          text: 'Rimuovi',
          style: 'destructive',
          onPress: async () => {
            try {
              await removeMember(groupId, memberId);
            } catch (error) {
              Alert.alert('Errore', 'Impossibile rimuovere il membro');
            }
          }
        },
      ]
    );
  };

  // ✅ CHIAMATE RIMOSSE - Si fanno dalla chat principale

  // 👑 FUNZIONI AMMINISTRAZIONE
  const handleAddModerators = () => {
    console.log('👑 Aggiungi moderatori');

    // Filtra i membri che NON sono già admin
    // I membri sono stringhe (ID), non oggetti
    const nonAdminMemberIds = currentGroup?.members?.filter(memberId =>
      !currentGroup?.admins?.includes(memberId)
    ) || [];

    console.log('🔍 Membri non admin (IDs):', nonAdminMemberIds);
    console.log('🔍 Admin attuali:', currentGroup?.admins);

    if (nonAdminMemberIds.length === 0) {
      if (currentGroup?.members?.length === 1) {
        Alert.alert('Info', 'Sei l\'unico membro del gruppo. Aggiungi altri partecipanti per nominarli amministratori.');
      } else {
        Alert.alert('Info', 'Tutti i membri sono già amministratori del gruppo.');
      }
      return;
    }

    // Crea le opzioni per ActionSheet
    const options = nonAdminMemberIds.map(memberId => {
      // Cerca il nome del membro nella lista renderizzata o usa l'ID
      const memberName = getMemberNameById(memberId) || `Utente ${memberId.slice(-4)}`;

      console.log('🔍 Membro per admin:', { memberId, memberName });

      return {
        text: `Nomina ${memberName} amministratore`,
        onPress: () => handlePromoteToAdmin(memberId, memberName)
      };
    });

    options.push({ text: 'Annulla', style: 'cancel' });

    Alert.alert(
      'Aggiungi moderatori',
      'Seleziona chi vuoi nominare amministratore del gruppo:',
      options
    );
  };

  const handlePromoteToAdmin = async (memberId, memberName) => {
    try {
      console.log('👑 Promuovendo a admin:', memberId, memberName);

      // Ottieni il token di autenticazione
      const token = await AsyncStorage.getItem('@trendychat:token');
      if (!token) {
        throw new Error('Token di autenticazione non trovato');
      }

      // Chiama API per promuovere a admin
      const response = await axios.post(`${API_URL}/groups/${groupId}/promote-admin`, {
        memberId: memberId
      }, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.status === 200) {
        // Ricarica il gruppo per aggiornare la lista admin
        await loadGroup(groupId);

        Alert.alert('Successo', `${memberName} è stato nominato amministratore del gruppo.`);
      }
    } catch (error) {
      console.error('❌ Errore nella promozione ad admin:', error);
      Alert.alert('Errore', 'Impossibile nominare l\'amministratore. Riprova più tardi.');
    }
  };

  const handleAddParticipants = () => {
    console.log('👥 Aggiungi partecipanti');
    // Usa la stessa funzione di handleAddMember
    handleAddMember();
  };

  const handleEditGroupInfo = () => {
    console.log('✏️ Modifica info gruppo');

    // Naviga alla schermata di modifica gruppo production-ready
    navigation.navigate('EditGroup', { groupId });
  };

  const handleDeleteGroup = () => {
    console.log('🗑️ Elimina gruppo');
    Alert.alert(
      'Elimina gruppo',
      'Sei sicuro di voler eliminare questo gruppo? Questa azione non può essere annullata.',
      [
        {
          text: 'Annulla',
          style: 'cancel',
        },
        {
          text: 'Elimina',
          style: 'destructive',
          onPress: async () => {
            try {
              await deleteGroup(groupId);
              navigation.goBack();
            } catch (error) {
              Alert.alert('Errore', 'Impossibile eliminare il gruppo');
            }
          },
        },
      ]
    );
  };

  const renderMember = ({ item }) => {
    const isCurrentUser = item.id === user?.uid || item.id === user?.id;

    // Per l'utente corrente, usa sempre l'avatar aggiornato dal profilo
    const memberAvatar = isCurrentUser
      ? (user?.avatar || user?.photoURL || 'https://via.placeholder.com/50')
      : (item.photoURL || item.avatar || 'https://via.placeholder.com/50');

    // 🟢 CONTROLLA SE L'UTENTE È ONLINE DAL WEBSOCKET
    const isOnlineFromWebSocket = groupOnlineUsers.some(u => u.userId === item.id);
    const isOnline = isCurrentUser ? true : isOnlineFromWebSocket; // Tu sei sempre online



    return (
      <View style={styles.memberItem}>
        <Image
          source={{ uri: memberAvatar }}
          style={styles.memberImage}
        />

        <View style={styles.memberInfo}>
          <Text style={styles.memberName}>
            {isCurrentUser ? (user?.displayName || user?.name || 'Tu') : item.name} {isCurrentUser ? '(Tu)' : ''}
          </Text>
          <Text style={styles.memberStatus}>
            {isOnline ? 'Online' : 'Offline'}
            {currentGroup?.admins?.includes(item.id) && ' • Admin'}
          </Text>
        </View>

        {isAdmin && !isCurrentUser && (
          <TouchableOpacity
            style={styles.removeMemberButton}
            onPress={() => handleRemoveMember(item.id)}
          >
            <Ionicons name="remove-circle" size={24} color="#D81B60" />
          </TouchableOpacity>
        )}
      </View>
    );
  };

  if (loading) {
    return (
      <View style={styles.container}>
        <StatusBar barStyle="light-content" backgroundColor="#1E88E5" />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#1E88E5" />
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#1E88E5" />

      <LinearGradient
        colors={['#1E88E5', '#D81B60']}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 0 }}
        style={styles.header}
      >
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color="#FFFFFF" />
        </TouchableOpacity>

        <Text style={styles.headerTitle}>Info gruppo</Text>
      </LinearGradient>

      <ScrollView style={styles.scrollContainer}>
        <View style={styles.groupInfoContainer}>
          <TouchableOpacity
            style={styles.groupImageContainer}
            onPress={isAdmin ? handleChangeGroupPhoto : null}
          >
            <Image
              source={{ uri: currentGroup?.photoURL || 'https://via.placeholder.com/100' }}
              style={styles.groupImage}
            />
            {isAdmin && (
              <View style={styles.editImageButton}>
                <Ionicons name="camera" size={20} color="#FFFFFF" />
              </View>
            )}
          </TouchableOpacity>

          <Text style={styles.groupName}>{currentGroup?.name}</Text>
          <Text style={styles.groupMembers}>
            Gruppo • {currentGroup?.members?.length || 0} partecipanti
          </Text>

          {/* 📝 DESCRIZIONE GRUPPO IN STILE TRENDYCHAT */}
          {currentGroup?.description && (
            <View style={styles.groupDescriptionContainer}>
              <LinearGradient
                colors={['rgba(30, 136, 229, 0.1)', 'rgba(216, 27, 96, 0.1)']}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 0 }}
                style={styles.groupDescriptionGradient}
              >
                <Text style={styles.groupDescription}>{currentGroup.description}</Text>
              </LinearGradient>
            </View>
          )}
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Impostazioni</Text>

          <View style={styles.settingItem}>
            <LinearGradient
              colors={['#1E88E5', '#D81B60']}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 0 }}
              style={styles.settingIconContainer}
            >
              <Ionicons name="notifications" size={20} color="#FFFFFF" />
            </LinearGradient>
            <View style={styles.settingContent}>
              <Text style={styles.settingLabel}>Notifiche</Text>
              <Switch
                value={notifications}
                onValueChange={setNotifications}
                trackColor={{ false: '#767577', true: '#1E88E5' }}
                thumbColor={notifications ? '#FFFFFF' : '#f4f3f4'}
              />
            </View>
          </View>

          <View style={styles.settingItem}>
            <LinearGradient
              colors={['#1E88E5', '#D81B60']}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 0 }}
              style={styles.settingIconContainer}
            >
              <Ionicons name="images" size={20} color="#FFFFFF" />
            </LinearGradient>
            <View style={styles.settingContent}>
              <Text style={styles.settingLabel}>Visibilità media</Text>
              <Switch
                value={mediaVisibility}
                onValueChange={setMediaVisibility}
                trackColor={{ false: '#767577', true: '#1E88E5' }}
                thumbColor={mediaVisibility ? '#FFFFFF' : '#f4f3f4'}
              />
            </View>
          </View>
        </View>

        {/* 👑 SEZIONE AMMINISTRAZIONE - SOLO PER PROPRIETARIO/ADMIN */}
        {isAdmin && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Amministrazione</Text>

            <TouchableOpacity style={styles.settingItem} onPress={handleAddModerators}>
              <LinearGradient
                colors={['#1E88E5', '#D81B60']}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 0 }}
                style={styles.settingIconContainer}
              >
                <Ionicons name="shield-checkmark" size={20} color="#FFFFFF" />
              </LinearGradient>
              <View style={styles.settingContent}>
                <Text style={styles.settingLabel}>Aggiungi moderatori</Text>
                <Ionicons name="chevron-forward" size={20} color="#AAAAAA" />
              </View>
            </TouchableOpacity>

            <TouchableOpacity style={styles.settingItem} onPress={handleAddParticipants}>
              <LinearGradient
                colors={['#1E88E5', '#D81B60']}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 0 }}
                style={styles.settingIconContainer}
              >
                <Ionicons name="person-add" size={20} color="#FFFFFF" />
              </LinearGradient>
              <View style={styles.settingContent}>
                <Text style={styles.settingLabel}>Aggiungi partecipanti</Text>
                <Ionicons name="chevron-forward" size={20} color="#AAAAAA" />
              </View>
            </TouchableOpacity>

            <TouchableOpacity style={styles.settingItem} onPress={handleEditGroupInfo}>
              <LinearGradient
                colors={['#1E88E5', '#D81B60']}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 0 }}
                style={styles.settingIconContainer}
              >
                <Ionicons name="create" size={20} color="#FFFFFF" />
              </LinearGradient>
              <View style={styles.settingContent}>
                <Text style={styles.settingLabel}>Modifica info gruppo</Text>
                <Ionicons name="chevron-forward" size={20} color="#AAAAAA" />
              </View>
            </TouchableOpacity>

            {/* 🗑️ ELIMINA GRUPPO - SOLO PER PROPRIETARIO */}
            {currentGroup?.admins?.[0] === (user?.id || user?.uid) && (
              <TouchableOpacity style={styles.settingItem} onPress={handleDeleteGroup}>
                <LinearGradient
                  colors={['#D81B60', '#FF6B6B']}
                  start={{ x: 0, y: 0 }}
                  end={{ x: 1, y: 0 }}
                  style={styles.settingIconContainer}
                >
                  <Ionicons name="trash" size={20} color="#FFFFFF" />
                </LinearGradient>
                <View style={styles.settingContent}>
                  <Text style={[styles.settingLabel, { color: '#D81B60' }]}>Elimina gruppo</Text>
                  <Ionicons name="chevron-forward" size={20} color="#D81B60" />
                </View>
              </TouchableOpacity>
            )}
          </View>
        )}

        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>
              {currentGroup?.members?.length || 0} partecipanti
            </Text>
            {isAdmin && (
              <TouchableOpacity
                style={styles.addMemberButton}
                onPress={handleAddMember}
              >
                <Ionicons name="person-add" size={24} color="#1E88E5" />
              </TouchableOpacity>
            )}
          </View>

          <FlatList
            data={currentGroup?.members || []}
            renderItem={renderMember}
            keyExtractor={(item, index) => item.id || item._id || `member_${index}`}
            scrollEnabled={false}
          />
        </View>

        <TouchableOpacity
          style={styles.leaveGroupButton}
          onPress={handleLeaveGroup}
        >
          <LinearGradient
            colors={['#D81B60', '#FF6B6B']}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 0 }}
            style={styles.leaveIconContainer}
          >
            <Ionicons name="exit-outline" size={20} color="#FFFFFF" />
          </LinearGradient>
          <Text style={styles.leaveGroupText}>Lascia gruppo</Text>
        </TouchableOpacity>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#121212',
    paddingTop: Platform.OS === 'ios' ? 44 : StatusBar.currentHeight,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingTop: Platform.OS === 'ios' ? 10 : 5,
    paddingBottom: 12,
    height: 50,
  },
  backButton: {
    padding: 8,
    marginRight: 16,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  scrollContainer: {
    flex: 1,
  },
  groupInfoContainer: {
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#1E1E1E',
  },
  groupImageContainer: {
    position: 'relative',
    marginBottom: 16,
  },
  groupImage: {
    width: 100,
    height: 100,
    borderRadius: 50,
  },
  editImageButton: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    backgroundColor: '#1E88E5',
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#121212',
  },
  groupName: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 4,
  },
  groupMembers: {
    fontSize: 14,
    color: '#AAAAAA',
    marginBottom: 16,
  },
  // ✅ STILI AZIONI RIMOSSE - Non servono più
  section: {
    backgroundColor: '#1E1E1E',
    marginTop: 8,
    paddingVertical: 16,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    marginBottom: 8,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#1E88E5',
    paddingHorizontal: 16,
    marginBottom: 8,
  },
  addMemberButton: {
    padding: 8,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
  },
  settingIconContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  settingContent: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  settingLabel: {
    fontSize: 16,
    color: '#FFFFFF',
  },
  memberItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
  },
  memberImage: {
    width: 50,
    height: 50,
    borderRadius: 25,
    marginRight: 16,
  },
  memberInfo: {
    flex: 1,
  },
  memberName: {
    fontSize: 16,
    color: '#FFFFFF',
  },
  memberStatus: {
    fontSize: 14,
    color: '#AAAAAA',
    marginTop: 2,
  },
  removeMemberButton: {
    padding: 8,
  },
  leaveGroupButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    backgroundColor: '#1E1E1E',
    marginTop: 8,
    marginBottom: 20,
  },
  leaveIconContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  leaveGroupText: {
    fontSize: 16,
    color: '#D81B60',
    marginLeft: 8,
  },
  // 📝 STILI DESCRIZIONE GRUPPO TRENDYCHAT
  groupDescriptionContainer: {
    marginTop: 16,
    marginHorizontal: 20,
    alignItems: 'center',
  },
  groupDescriptionGradient: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 15,
    width: '100%',
    borderWidth: 1,
    borderColor: 'rgba(30, 136, 229, 0.3)',
  },
  groupDescription: {
    fontSize: 16,
    color: '#FFFFFF',
    textAlign: 'center',
    lineHeight: 22,
    fontStyle: 'italic',
  },
});

export default GroupInfoScreen;
