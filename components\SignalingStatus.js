// 📡 Componente Status Server Signaling per TrendyChat
// Mostra quale server signaling è attivo (Render o SuperMicron)

import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import callRouter from '../services/callRouter';

const SignalingStatus = ({ style }) => {
  const [serverInfo, setServerInfo] = useState(null);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    // Aggiorna le informazioni del server ogni 5 secondi
    const updateServerInfo = () => {
      try {
        const info = callRouter.getServerInfo();
        setServerInfo(info);
        setIsVisible(info && info.signaling && info.signaling.isConnected);
      } catch (error) {
        console.error('Errore aggiornamento server info:', error);
        setIsVisible(false);
      }
    };

    // Aggiornamento iniziale
    updateServerInfo();

    // Aggiornamento periodico
    const interval = setInterval(updateServerInfo, 5000);

    return () => clearInterval(interval);
  }, []);

  if (!isVisible || !serverInfo?.signaling) {
    return null;
  }

  const { signaling } = serverInfo;
  const serverType = signaling.server?.type || 'unknown';
  const isRender = serverType === 'render';
  const isSupermicron = serverType === 'supermicron';

  return (
    <View style={[styles.container, style]}>
      <View style={[
        styles.indicator,
        isRender && styles.renderIndicator,
        isSupermicron && styles.supermicronIndicator
      ]} />
      <Text style={styles.text}>
        {isRender && '🌐 Render'}
        {isSupermicron && '🏠 SuperMicron'}
        {!isRender && !isSupermicron && '❓ Unknown'}
      </Text>
      {signaling.reconnectAttempts > 0 && (
        <Text style={styles.reconnectText}>
          Riconnessioni: {signaling.reconnectAttempts}
        </Text>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    backgroundColor: 'rgba(0, 0, 0, 0.1)',
    borderRadius: 12,
    marginHorizontal: 4,
  },
  indicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 6,
  },
  renderIndicator: {
    backgroundColor: '#00D4AA', // Verde Render
  },
  supermicronIndicator: {
    backgroundColor: '#FF6B6B', // Rosso SuperMicron
  },
  text: {
    fontSize: 12,
    fontWeight: '600',
    color: '#333',
  },
  reconnectText: {
    fontSize: 10,
    color: '#666',
    marginLeft: 4,
  },
});

export default SignalingStatus;
