import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  TouchableWithoutFeedback,
  Dimensions,
  Platform,
  ScrollView,
  Switch
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import AsyncStorage from '@react-native-async-storage/async-storage';

const { width, height } = Dimensions.get('window');

const StatusSettingsModal = ({ visible, onClose }) => {
  const [settings, setSettings] = useState({
    storyDuration: '24h', // 24h, 12h, 6h, 3h
    mediaQuality: 'high', // high, medium, low
    autoDownload: 'wifi', // wifi, always, never
    saveToGallery: false,
    showViewers: true,
    allowReplies: true
  });

  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    try {
      const savedSettings = await AsyncStorage.getItem('storySettings');
      if (savedSettings) {
        setSettings(JSON.parse(savedSettings));
      }
    } catch (error) {
      console.error('Errore caricamento impostazioni:', error);
    }
  };

  const saveSettings = async (newSettings) => {
    try {
      await AsyncStorage.setItem('storySettings', JSON.stringify(newSettings));
      setSettings(newSettings);
      console.log('✅ Impostazioni storie salvate:', newSettings);
    } catch (error) {
      console.error('❌ Errore salvataggio impostazioni:', error);
    }
  };

  const updateSetting = (key, value) => {
    const newSettings = { ...settings, [key]: value };
    saveSettings(newSettings);
  };

  const durationOptions = [
    { id: '24h', label: '24 ore', description: 'Durata standard (consigliata)', icon: 'time-outline' },
    { id: '12h', label: '12 ore', description: 'Durata ridotta', icon: 'hourglass-outline' },
    { id: '6h', label: '6 ore', description: 'Durata breve', icon: 'timer-outline' },
    { id: '3h', label: '3 ore', description: 'Durata molto breve', icon: 'stopwatch-outline' }
  ];

  const qualityOptions = [
    { id: 'high', label: 'Alta qualità', description: 'Migliore qualità, più dati', icon: 'diamond-outline' },
    { id: 'medium', label: 'Qualità media', description: 'Bilanciata (consigliata)', icon: 'radio-button-on-outline' },
    { id: 'low', label: 'Qualità bassa', description: 'Risparmia dati', icon: 'remove-circle-outline' }
  ];

  const downloadOptions = [
    { id: 'wifi', label: 'Solo WiFi', description: 'Scarica solo con WiFi (consigliato)', icon: 'wifi-outline' },
    { id: 'always', label: 'Sempre', description: 'Scarica sempre automaticamente', icon: 'download-outline' },
    { id: 'never', label: 'Mai', description: 'Non scaricare mai automaticamente', icon: 'close-circle-outline' }
  ];

  if (!visible) return null;

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={onClose}
      statusBarTranslucent
    >
      <TouchableWithoutFeedback onPress={onClose}>
        <View style={styles.modalContainer}>
          <TouchableWithoutFeedback>
            <View style={styles.modalContent}>
              <LinearGradient
                colors={['#1E88E5', '#D81B60']}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 0 }}
                style={styles.header}
              >
                <Text style={styles.headerTitle}>Impostazioni storie</Text>
                <TouchableOpacity style={styles.closeButton} onPress={onClose}>
                  <Ionicons name="close" size={24} color="#FFFFFF" />
                </TouchableOpacity>
              </LinearGradient>

              <ScrollView style={styles.scrollContainer}>
                {/* DURATA STORIE */}
                <View style={styles.categoryContainer}>
                  <Text style={styles.categoryTitle}>Durata storie</Text>
                  <Text style={styles.categoryDescription}>
                    Scegli quanto tempo rimangono visibili le tue storie
                  </Text>

                  {durationOptions.map((option) => (
                    <TouchableOpacity
                      key={option.id}
                      style={[
                        styles.optionItem,
                        settings.storyDuration === option.id && styles.selectedOption
                      ]}
                      onPress={() => updateSetting('storyDuration', option.id)}
                    >
                      <View style={styles.optionIconContainer}>
                        <LinearGradient
                          colors={['#1E88E5', '#D81B60']}
                          style={styles.optionIconGradient}
                        >
                          <Ionicons name={option.icon} size={18} color="#FFFFFF" />
                        </LinearGradient>
                      </View>
                      <View style={styles.optionTextContainer}>
                        <Text style={styles.optionLabel}>{option.label}</Text>
                        <Text style={styles.optionDescription}>{option.description}</Text>
                      </View>
                      <View style={styles.radioContainer}>
                        <LinearGradient
                          colors={settings.storyDuration === option.id 
                            ? ['#1E88E5', '#D81B60'] 
                            : ['#2A2A2A', '#2A2A2A']
                          }
                          style={styles.radioButton}
                        >
                          {settings.storyDuration === option.id && (
                            <Ionicons name="checkmark" size={16} color="#FFFFFF" />
                          )}
                        </LinearGradient>
                      </View>
                    </TouchableOpacity>
                  ))}
                </View>

                {/* QUALITÀ MEDIA */}
                <View style={styles.categoryContainer}>
                  <Text style={styles.categoryTitle}>Qualità media</Text>
                  <Text style={styles.categoryDescription}>
                    Scegli la qualità per foto e video delle storie
                  </Text>

                  {qualityOptions.map((option) => (
                    <TouchableOpacity
                      key={option.id}
                      style={[
                        styles.optionItem,
                        settings.mediaQuality === option.id && styles.selectedOption
                      ]}
                      onPress={() => updateSetting('mediaQuality', option.id)}
                    >
                      <View style={styles.optionIconContainer}>
                        <LinearGradient
                          colors={['#1E88E5', '#D81B60']}
                          style={styles.optionIconGradient}
                        >
                          <Ionicons name={option.icon} size={18} color="#FFFFFF" />
                        </LinearGradient>
                      </View>
                      <View style={styles.optionTextContainer}>
                        <Text style={styles.optionLabel}>{option.label}</Text>
                        <Text style={styles.optionDescription}>{option.description}</Text>
                      </View>
                      <View style={styles.radioContainer}>
                        <LinearGradient
                          colors={settings.mediaQuality === option.id 
                            ? ['#1E88E5', '#D81B60'] 
                            : ['#2A2A2A', '#2A2A2A']
                          }
                          style={styles.radioButton}
                        >
                          {settings.mediaQuality === option.id && (
                            <Ionicons name="checkmark" size={16} color="#FFFFFF" />
                          )}
                        </LinearGradient>
                      </View>
                    </TouchableOpacity>
                  ))}
                </View>

                {/* AUTO-DOWNLOAD */}
                <View style={styles.categoryContainer}>
                  <Text style={styles.categoryTitle}>Download automatico</Text>
                  <Text style={styles.categoryDescription}>
                    Quando scaricare automaticamente le storie degli altri
                  </Text>

                  {downloadOptions.map((option) => (
                    <TouchableOpacity
                      key={option.id}
                      style={[
                        styles.optionItem,
                        settings.autoDownload === option.id && styles.selectedOption
                      ]}
                      onPress={() => updateSetting('autoDownload', option.id)}
                    >
                      <View style={styles.optionIconContainer}>
                        <LinearGradient
                          colors={['#1E88E5', '#D81B60']}
                          style={styles.optionIconGradient}
                        >
                          <Ionicons name={option.icon} size={18} color="#FFFFFF" />
                        </LinearGradient>
                      </View>
                      <View style={styles.optionTextContainer}>
                        <Text style={styles.optionLabel}>{option.label}</Text>
                        <Text style={styles.optionDescription}>{option.description}</Text>
                      </View>
                      <View style={styles.radioContainer}>
                        <LinearGradient
                          colors={settings.autoDownload === option.id 
                            ? ['#1E88E5', '#D81B60'] 
                            : ['#2A2A2A', '#2A2A2A']
                          }
                          style={styles.radioButton}
                        >
                          {settings.autoDownload === option.id && (
                            <Ionicons name="checkmark" size={16} color="#FFFFFF" />
                          )}
                        </LinearGradient>
                      </View>
                    </TouchableOpacity>
                  ))}
                </View>

                {/* OPZIONI AGGIUNTIVE */}
                <View style={styles.categoryContainer}>
                  <Text style={styles.categoryTitle}>Opzioni aggiuntive</Text>

                  <View style={styles.switchItem}>
                    <View style={styles.switchIconContainer}>
                      <LinearGradient
                        colors={['#4CAF50', '#45A049']}
                        style={styles.optionIconGradient}
                      >
                        <Ionicons name="save-outline" size={18} color="#FFFFFF" />
                      </LinearGradient>
                    </View>
                    <View style={styles.switchTextContainer}>
                      <Text style={styles.optionLabel}>Salva nella galleria</Text>
                      <Text style={styles.optionDescription}>Salva automaticamente le tue storie</Text>
                    </View>
                    <Switch
                      value={settings.saveToGallery}
                      onValueChange={(value) => updateSetting('saveToGallery', value)}
                      trackColor={{ false: '#2A2A2A', true: '#1E88E5' }}
                      thumbColor={settings.saveToGallery ? '#D81B60' : '#AAAAAA'}
                    />
                  </View>

                  <View style={styles.switchItem}>
                    <View style={styles.switchIconContainer}>
                      <LinearGradient
                        colors={['#FF9800', '#F57C00']}
                        style={styles.optionIconGradient}
                      >
                        <Ionicons name="eye-outline" size={18} color="#FFFFFF" />
                      </LinearGradient>
                    </View>
                    <View style={styles.switchTextContainer}>
                      <Text style={styles.optionLabel}>Mostra visualizzatori</Text>
                      <Text style={styles.optionDescription}>Permetti di vedere chi ha visto le tue storie</Text>
                    </View>
                    <Switch
                      value={settings.showViewers}
                      onValueChange={(value) => updateSetting('showViewers', value)}
                      trackColor={{ false: '#2A2A2A', true: '#1E88E5' }}
                      thumbColor={settings.showViewers ? '#D81B60' : '#AAAAAA'}
                    />
                  </View>

                  <View style={styles.switchItem}>
                    <View style={styles.switchIconContainer}>
                      <LinearGradient
                        colors={['#9C27B0', '#7B1FA2']}
                        style={styles.optionIconGradient}
                      >
                        <Ionicons name="chatbubble-outline" size={18} color="#FFFFFF" />
                      </LinearGradient>
                    </View>
                    <View style={styles.switchTextContainer}>
                      <Text style={styles.optionLabel}>Consenti risposte</Text>
                      <Text style={styles.optionDescription}>Permetti agli altri di rispondere alle tue storie</Text>
                    </View>
                    <Switch
                      value={settings.allowReplies}
                      onValueChange={(value) => updateSetting('allowReplies', value)}
                      trackColor={{ false: '#2A2A2A', true: '#1E88E5' }}
                      thumbColor={settings.allowReplies ? '#D81B60' : '#AAAAAA'}
                    />
                  </View>
                </View>
              </ScrollView>
            </View>
          </TouchableWithoutFeedback>
        </View>
      </TouchableWithoutFeedback>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
  },
  modalContent: {
    width: width * 0.9,
    maxHeight: height * 0.9,
    backgroundColor: '#1E1E1E',
    borderRadius: 20,
    overflow: 'hidden',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 16,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  closeButton: {
    padding: 8,
  },
  scrollContainer: {
    maxHeight: height * 0.8,
  },
  categoryContainer: {
    marginBottom: 24,
    paddingHorizontal: 16,
  },
  categoryTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 4,
  },
  categoryDescription: {
    fontSize: 14,
    color: '#AAAAAA',
    marginBottom: 16,
    lineHeight: 20,
  },
  optionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#2A2A2A',
  },
  selectedOption: {
    backgroundColor: 'rgba(30, 136, 229, 0.1)',
    borderRadius: 12,
    marginHorizontal: -8,
    paddingHorizontal: 8,
  },
  optionIconContainer: {
    marginRight: 16,
  },
  optionIconGradient: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  optionTextContainer: {
    flex: 1,
  },
  optionLabel: {
    fontSize: 16,
    color: '#FFFFFF',
    marginBottom: 2,
  },
  optionDescription: {
    fontSize: 12,
    color: '#AAAAAA',
  },
  radioContainer: {
    marginLeft: 12,
  },
  radioButton: {
    width: 24,
    height: 24,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  switchItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#2A2A2A',
  },
  switchIconContainer: {
    marginRight: 16,
  },
  switchTextContainer: {
    flex: 1,
  },
});

export default StatusSettingsModal;
