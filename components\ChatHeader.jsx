import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  Platform,
  StatusBar,
  Dimensions,
  Modal,
  TouchableWithoutFeedback,
  Alert,
  Animated,
  Share
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { LinearGradient } from 'expo-linear-gradient';
import useContactStore from '../store/contactStore';
import useChatStore from '../store/useChatStore';
import useAuthStore from '../store/authStore';
import OnlineStatus from './OnlineStatus';

const { width } = Dimensions.get('window');

// Componente MenuItem semplificato
const MenuItem = ({ icon, label, onPress }) => {
  return (
    <TouchableOpacity
      activeOpacity={0.7}
      onPress={onPress}
      style={styles.menuItemContainer}
    >
      <View style={styles.menuItem}>
        {icon}
        <Text style={styles.menuItemText}>{label}</Text>
      </View>
    </TouchableOpacity>
  );
};

const ChatHeader = ({
  title,
  subtitle,
  avatar,
  isOnline = false,
  onCallPress,
  onVideoCallPress,
  onInfoPress,
  chatId,
  userId, // ID dell'utente per mostrare stato online/ultimo accesso
  isPrivateChat = false // 🔧 Flag per distinguere chat private da gruppi
}) => {
  const navigation = useNavigation();
  const [showMenu, setShowMenu] = useState(false);
  const menuScaleAnim = useRef(new Animated.Value(0.8)).current;
  const menuOpacityAnim = useRef(new Animated.Value(0)).current;

  // Ottieni l'utente corrente per sincronizzare l'avatar
  const { user } = useAuthStore();

  // Ottieni le funzioni dagli store
  const { blockContact } = useContactStore(state => ({
    blockContact: state.blockContact
  }));

  const { deleteChat } = useChatStore(state => ({
    deleteChat: state.deleteChat
  }));

  // Solo per chat private (quando userId è definito), sincronizza l'avatar dell'utente corrente
  // Per chat di gruppo (userId undefined), usa sempre l'avatar del gruppo
  const currentAvatar = (userId && (userId === user?.id || userId === user?.uid))
    ? (user?.avatar || user?.photoURL || avatar)  // Chat private: avatar utente aggiornato
    : avatar;  // Chat di gruppo o altri utenti: avatar originale

  const toggleMenu = (visible) => {
    setShowMenu(visible);

    if (visible) {
      // Animazione di apertura
      Animated.parallel([
        Animated.spring(menuScaleAnim, {
          toValue: 1,
          friction: 7,
          tension: 70,
          useNativeDriver: false
        }),
        Animated.timing(menuOpacityAnim, {
          toValue: 1,
          duration: 200,
          useNativeDriver: false
        })
      ]).start();
    } else {
      // Animazione di chiusura
      Animated.parallel([
        Animated.timing(menuScaleAnim, {
          toValue: 0.8,
          duration: 150,
          useNativeDriver: false
        }),
        Animated.timing(menuOpacityAnim, {
          toValue: 0,
          duration: 150,
          useNativeDriver: false
        })
      ]).start();
    }
  };

  return (
    <LinearGradient
      colors={['#1E88E5', '#D81B60']}
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 0 }}
      style={styles.container}
    >
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent={true} />

      <TouchableOpacity
        style={styles.backButton}
        onPress={() => navigation.goBack()}
      >
        <Ionicons name="arrow-back" size={24} color="#FFFFFF" />
      </TouchableOpacity>

      <TouchableOpacity
        style={styles.profileContainer}
        onPress={onInfoPress}
      >
        {currentAvatar ? (
          <Image source={{ uri: currentAvatar }} style={styles.avatar} />
        ) : (
          <View style={styles.avatarPlaceholder}>
            <Text style={styles.avatarText}>
              {title ? title.charAt(0).toUpperCase() : '?'}
            </Text>
          </View>
        )}

        <View style={styles.textContainer}>
          <Text style={styles.title} numberOfLines={1}>
            {title || 'Chat'}
          </Text>
          {subtitle ? (
            <View style={styles.subtitleContainer}>
              {isOnline && <View style={styles.onlineIndicator} />}
              <Text style={styles.subtitle} numberOfLines={1}>
                {subtitle}
              </Text>
            </View>
          ) : null}
        </View>
      </TouchableOpacity>

      <View style={styles.actions}>
        <TouchableOpacity
          style={styles.actionButton}
          onPress={onCallPress}
        >
          <Ionicons name="call" size={22} color="#FFFFFF" />
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.actionButton}
          onPress={onVideoCallPress}
        >
          <Ionicons name="videocam" size={22} color="#FFFFFF" />
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.actionButton}
          onPress={() => toggleMenu(true)}
        >
          <View style={styles.menuIconContainer}>
            <Ionicons name="menu" size={22} color="#FFFFFF" />
          </View>
        </TouchableOpacity>

        <Modal
          transparent={true}
          visible={showMenu}
          animationType="fade"
          onRequestClose={() => toggleMenu(false)}
        >
          <TouchableWithoutFeedback onPress={() => toggleMenu(false)}>
            <View style={styles.modalOverlay}>
              <Animated.View
                style={[
                  styles.menuContainer,
                  {
                    right: 10,
                    top: Platform.OS === 'ios' ? 100 : 90,  // ✅ CORRETTO: +10px per Android
                    opacity: menuOpacityAnim,
                    transform: [{ scale: menuScaleAnim }]
                  }
                ]}>
                <LinearGradient
                  colors={['#1E88E5', '#D81B60']}
                  start={{ x: 0, y: 0 }}
                  end={{ x: 1, y: 0 }}
                  style={styles.menuHeader}
                >
                  <Text style={styles.menuHeaderText}>Menu</Text>
                </LinearGradient>



                <MenuItem
                  icon={
                    <LinearGradient
                      colors={['#1E88E5', '#D81B60']}
                      start={{ x: 0, y: 0 }}
                      end={{ x: 1, y: 0 }}
                      style={styles.menuItemIconGradient}
                    >
                      <Ionicons name="images-outline" size={18} color="#FFFFFF" />
                    </LinearGradient>
                  }
                  label="Media, link e documenti"
                  onPress={() => {
                    toggleMenu(false);
                    navigation.navigate('ChatMedia', {
                      chatId,
                      name: title,
                      isPrivateChat: isPrivateChat // 🔧 Passa il flag per distinguere chat private da gruppi
                    });
                  }}
                />



                <MenuItem
                  icon={
                    <LinearGradient
                      colors={['#1E88E5', '#D81B60']}
                      start={{ x: 0, y: 0 }}
                      end={{ x: 1, y: 0 }}
                      style={styles.menuItemIconGradient}
                    >
                      <Ionicons name="notifications-off-outline" size={18} color="#FFFFFF" />
                    </LinearGradient>
                  }
                  label="Silenzia notifiche"
                  onPress={() => {
                    toggleMenu(false);
                    navigation.navigate('ChatNotification', { chatId, name: title });
                  }}
                />

                <MenuItem
                  icon={
                    <LinearGradient
                      colors={['#1E88E5', '#D81B60']}
                      start={{ x: 0, y: 0 }}
                      end={{ x: 1, y: 0 }}
                      style={styles.menuItemIconGradient}
                    >
                      <Ionicons name="color-palette-outline" size={18} color="#FFFFFF" />
                    </LinearGradient>
                  }
                  label="Sfondo chat"
                  onPress={() => {
                    toggleMenu(false);
                    navigation.navigate('ChatBackground', { chatId, name: title });
                  }}
                />

                <MenuItem
                  icon={
                    <LinearGradient
                      colors={['#1E88E5', '#D81B60']}
                      start={{ x: 0, y: 0 }}
                      end={{ x: 1, y: 0 }}
                      style={styles.menuItemIconGradient}
                    >
                      <Ionicons name="list-outline" size={18} color="#FFFFFF" />
                    </LinearGradient>
                  }
                  label="Aggiungi a una lista"
                  onPress={() => {
                    toggleMenu(false);
                    navigation.navigate('ChatLists', { chatId, name: title });
                  }}
                />

                <MenuItem
                  icon={
                    <LinearGradient
                      colors={['#1E88E5', '#D81B60']}
                      start={{ x: 0, y: 0 }}
                      end={{ x: 1, y: 0 }}
                      style={styles.menuItemIconGradient}
                    >
                      <Ionicons name="time-outline" size={18} color="#FFFFFF" />
                    </LinearGradient>
                  }
                  label="Messaggi effimeri"
                  onPress={() => {
                    toggleMenu(false);
                    navigation.navigate('EphemeralMessages', { chatId, name: title });
                  }}
                />



                <MenuItem
                  icon={
                    <LinearGradient
                      colors={['#1E88E5', '#D81B60']}
                      start={{ x: 0, y: 0 }}
                      end={{ x: 1, y: 0 }}
                      style={styles.menuItemIconGradient}
                    >
                      <Ionicons name="ban-outline" size={18} color="#FFFFFF" />
                    </LinearGradient>
                  }
                  label="Blocca"
                  onPress={() => {
                    toggleMenu(false);
                    Alert.alert(
                      'Blocca contatto',
                      `Vuoi bloccare ${title}? Non riceverai più messaggi o chiamate da questo contatto.`,
                      [
                        { text: 'Annulla', style: 'cancel' },
                        {
                          text: 'Blocca',
                          style: 'destructive',
                          onPress: async () => {
                            try {
                              // Estrai l'ID del contatto dal chatId o usa un ID di esempio
                              const contactId = chatId.replace('chat_', '');
                              await blockContact(contactId);
                              Alert.alert('Contatto bloccato', `Hai bloccato ${title}`);
                            } catch (error) {
                              Alert.alert('Errore', 'Impossibile bloccare il contatto');
                            }
                          }
                        }
                      ]
                    );
                  }}
                />

                <MenuItem
                  icon={
                    <LinearGradient
                      colors={['#1E88E5', '#D81B60']}
                      start={{ x: 0, y: 0 }}
                      end={{ x: 1, y: 0 }}
                      style={styles.menuItemIconGradient}
                    >
                      <Ionicons name="trash-outline" size={18} color="#FFFFFF" />
                    </LinearGradient>
                  }
                  label="Svuota chat"
                  onPress={() => {
                    toggleMenu(false);
                    Alert.alert(
                      'Svuota chat',
                      'Vuoi eliminare tutti i messaggi di questa chat?',
                      [
                        { text: 'Annulla', style: 'cancel' },
                        {
                          text: 'Elimina',
                          style: 'destructive',
                          onPress: async () => {
                            try {
                              // Qui dovremmo chiamare la funzione per svuotare la chat
                              // ma per ora mostriamo solo un messaggio di successo
                              setTimeout(() => {
                                Alert.alert('Chat svuotata', 'Tutti i messaggi sono stati eliminati');
                              }, 1000);
                            } catch (error) {
                              Alert.alert('Errore', 'Impossibile svuotare la chat');
                            }
                          }
                        }
                      ]
                    );
                  }}
                />






              </Animated.View>
            </View>
          </TouchableWithoutFeedback>
        </Modal>
      </View>
    </LinearGradient>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingTop: Platform.OS === 'ios' ? 50 : 40,  // ✅ CORRETTO: Stesso di HomeScreen
    paddingBottom: 12,  // ✅ CORRETTO: Stesso di HomeScreen
    paddingHorizontal: 10,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
  },
  backButton: {
    padding: 8,
  },
  profileContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: 5,
  },
  avatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#FFFFFF',
  },
  avatarPlaceholder: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatarText: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: 'bold',
  },
  textContainer: {
    marginLeft: 10,
    flex: 1,
  },
  title: {
    color: '#FFFFFF',
    fontSize: width < 360 ? 16 : 18,
    fontWeight: 'bold',
  },
  subtitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  subtitle: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: width < 360 ? 12 : 14,
  },
  onlineIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#4CAF50',
    marginRight: 5,
  },
  actions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  actionButton: {
    padding: 8,
    marginLeft: 5,
  },
  menuIconContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 8,
    padding: 4,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'transparent',
  },
  menuContainer: {
    position: 'absolute',
    backgroundColor: '#1E1E1E', // Sfondo scuro per un look moderno
    borderRadius: 16, // Bordi più arrotondati
    padding: 0,
    width: 280, // Menu leggermente più largo
    maxHeight: '80%', // Limita l'altezza massima
    overflow: 'hidden',
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.3,
        shadowRadius: 8,
      },
      android: {
        elevation: 8,
      },
    }),
  },
  menuHeader: {
    paddingVertical: 16,
    paddingHorizontal: 20,
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
  },
  menuHeaderText: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: 'bold',
  },
  menuItemContainer: {
    marginVertical: 2,
    marginHorizontal: 8,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 14,
    paddingHorizontal: 20,
    borderRadius: 8,
    backgroundColor: 'transparent',
  },
  menuItemText: {
    fontSize: 16,
    marginLeft: 16,
    color: '#FFFFFF', // Testo bianco su sfondo scuro
    fontWeight: '500', // Testo leggermente più in grassetto
  },
  menuItemIconGradient: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default ChatHeader;
