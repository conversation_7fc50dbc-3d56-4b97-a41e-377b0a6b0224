import AsyncStorage from '../utils/asyncStorageConfig';
import { API_URL } from '../config/api';
import useAuthStore from '../store/authStore';

/**
 * Servizio per la gestione dei gruppi
 */
class GroupService {
  constructor() {
    // Rimuovi /api da ${API_URL}/api/groups perché API_URL già include /api
    this.baseUrl = `${API_URL}/groups`;
    console.log('URL base per i gruppi:', this.baseUrl);
  }

  /**
   * Ottiene il token di autenticazione
   * @returns {Promise<string>} Token di autenticazione
   */
  async getAuthToken() {
    try {
      // Importa il servizio di autenticazione
      const authService = require('./authService').default;

      // Ottieni il token direttamente dal servizio di autenticazione
      let token = authService.getToken();
      console.log('Token da authService:', token ? 'Trovato' : 'Non trovato');

      // Se il token contiene il prefisso jwt_token_, rimuovilo
      if (token && token.startsWith('jwt_token_')) {
        token = token.replace('jwt_token_', '');
        console.log('Prefisso jwt_token_ rimosso dal token');
      }

      return token;
    } catch (error) {
      console.error('Errore nel recupero del token di autenticazione:', error);
      return null;
    }
  }

  /**
   * Carica un'immagine per un gruppo
   * @param {string} imageUri - URI dell'immagine
   * @param {string} userId - ID dell'utente
   * @returns {Promise<Object>} Risultato dell'operazione
   */
  async uploadGroupImage(imageUri, userId) {
    try {
      console.log('Caricamento immagine gruppo sul server HP:', imageUri);
      console.log('ID utente:', userId);

      // Verifica che l'URI dell'immagine sia valido
      if (!imageUri || typeof imageUri !== 'string') {
        throw new Error('URI dell\'immagine non valido');
      }

      // Ottieni il token di autenticazione
      const token = await this.getAuthToken();
      if (!token) {
        throw new Error('Token di autenticazione non disponibile');
      }

      // Importa il servizio cloud
      const cloudService = require('./cloudService').default;
      
      // Assicurati che cloudService sia inizializzato con il token
      cloudService.setToken(token);
      
      // Carica l'immagine sul server con metadati espliciti per il tipo di file
      const uploadResult = await cloudService.uploadFile(
        imageUri,
        userId,
        'group',
        true, // Le immagini dei gruppi sono pubbliche
        {
          type: 'group',
          userId: userId,
          isGroupPhoto: true
        }
      );
      
      if (!uploadResult.success) {
        throw new Error(uploadResult.error || 'Errore durante il caricamento dell\'immagine');
      }
      
      console.log('Immagine gruppo caricata con successo:', uploadResult.file);
      
      return {
        success: true,
        imageUrl: uploadResult.file.url,
        localUri: imageUri,
        data: uploadResult // Includi tutti i dati della risposta
      };
    } catch (error) {
      console.error('Errore nel caricamento dell\'immagine del gruppo:', error);

      // In caso di errore generale, restituisci l'URI locale come fallback
      return {
        success: false,
        error: error.message,
        imageUrl: imageUri,
        localUri: imageUri
      };
    }
  }

  /**
   * Crea un nuovo gruppo
   * @param {Object} groupData - Dati del gruppo
   * @returns {Promise<Object>} Risultato dell'operazione
   */
  async createGroup(groupData) {
    try {
      console.log('Creazione gruppo sul server HP:', groupData);

      // Ottieni il token di autenticazione
      const token = await this.getAuthToken();
      console.log('Token di autenticazione:', token ? 'Disponibile' : 'Non disponibile');

      if (!token) {
        throw new Error('Token di autenticazione non disponibile');
      }

      // Ottieni l'ID utente dal servizio di autenticazione
      const authService = require('./authService').default;
      const currentUser = authService.getCurrentUser();
      const userId = currentUser?.id || currentUser?._id;

      if (!userId) {
        console.warn('ID utente non disponibile, potrebbe causare problemi con la creazione del gruppo');
      }

      console.log('ID utente corrente:', userId);

      let imageUrl = null;

      // Fase 1: Caricamento dell'immagine (se presente)
      if (groupData.photoUri) {
        try {
          console.log('Caricamento immagine del gruppo...');

          // Utilizziamo il metodo uploadGroupImage
          const uploadResult = await this.uploadGroupImage(groupData.photoUri, userId);

          if (uploadResult.success) {
            imageUrl = uploadResult.imageUrl;
            console.log('Immagine caricata con successo:', imageUrl);
          } else {
            console.warn('Errore nel caricamento dell\'immagine:', uploadResult.error);
            // Continuiamo comunque con la creazione del gruppo
            // Usiamo l'URI locale come fallback
            imageUrl = groupData.photoUri;
          }
        } catch (imageError) {
          console.error('Errore nel caricamento dell\'immagine:', imageError);
          // Continuiamo comunque con la creazione del gruppo
          imageUrl = groupData.photoUri;
        }
      }

      // Fase 2: Creazione del gruppo
      console.log('Invio richiesta per creare il gruppo:', this.baseUrl);

      // Prepara i dati per la creazione del gruppo
      const groupFormData = new FormData();
      groupFormData.append('name', groupData.name);
      groupFormData.append('description', groupData.description || '');

      // Se abbiamo un URL dell'immagine, lo aggiungiamo
      if (imageUrl) {
        groupFormData.append('photoURL', imageUrl);
      }

      // Aggiungi l'ID utente come creatore e amministratore
      if (userId) {
        groupFormData.append('createdBy', userId);
        groupFormData.append('admins', JSON.stringify([userId]));
        groupFormData.append('members', JSON.stringify([userId]));
      }

      // Invia la richiesta al server
      const response = await fetch(this.baseUrl, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'multipart/form-data',
        },
        body: groupFormData
      });

      // Log della risposta
      console.log('Risposta dal server - Status:', response.status);

      const responseText = await response.text();
      console.log('Risposta dal server (testo):', responseText.substring(0, 200) + (responseText.length > 200 ? '...' : ''));

      let data;
      try {
        data = JSON.parse(responseText);
      } catch (parseError) {
        console.error('Errore nel parsing della risposta JSON:', parseError);

        // Se la risposta non è JSON ma il server ha risposto con successo, creiamo un gruppo locale
        if (response.ok) {
          const localGroup = {
            id: `group_${Date.now()}`,
            name: groupData.name,
            description: groupData.description || '',
            photoURL: imageUrl,
            createdAt: new Date().toISOString(),
            members: userId ? [userId] : [],
            admins: userId ? [userId] : [],
            messages: []
          };

          return {
            success: true,
            group: localGroup,
            isLocal: true,
            message: 'Gruppo creato con successo (risposta non JSON)'
          };
        }

        throw new Error('Errore nel parsing della risposta JSON');
      }

      if (!response.ok) {
        throw new Error(data.error?.message || data.message || data.error || 'Errore nella creazione del gruppo');
      }

      // Crea l'oggetto gruppo con i dati ricevuti dal server
      const serverGroup = data.group || data;

      // Assicurati che l'oggetto gruppo abbia tutti i campi necessari
      const group = {
        id: serverGroup.id || serverGroup._id || `group_${Date.now()}`,
        name: serverGroup.name || groupData.name,
        description: serverGroup.description || groupData.description || '',
        photoURL: serverGroup.photoURL || imageUrl,
        createdAt: serverGroup.createdAt || new Date().toISOString(),
        members: serverGroup.members || (userId ? [userId] : []),
        admins: serverGroup.admins || (userId ? [userId] : []),
        messages: serverGroup.messages || []
      };

      console.log('Gruppo creato con successo:', group.name);

      return {
        success: true,
        group: group,
        isLocal: false // Indica che è un gruppo del server
      };
    } catch (error) {
      console.error('Errore nella creazione del gruppo:', error);

      // Ottieni l'ID utente dal servizio di autenticazione
      const authService = require('./authService').default;
      const currentUser = authService.getCurrentUser();
      const userId = currentUser?.id || currentUser?._id;

      // Anche in caso di errore generale, crea un gruppo locale
      const localGroup = {
        id: `group_${Date.now()}`,
        name: groupData.name,
        description: groupData.description || '',
        photoURL: groupData.photoUri, // Usa l'URI locale
        createdAt: new Date().toISOString(),
        members: userId ? [userId] : [],
        admins: userId ? [userId] : [],
        messages: []
      };

      console.log('Creato gruppo locale (dopo errore):', localGroup.name);

      return {
        success: true,
        group: localGroup,
        isLocal: true, // Indica che è un gruppo locale
        error: error.message
      };
    }
  }
}

export default new GroupService();
