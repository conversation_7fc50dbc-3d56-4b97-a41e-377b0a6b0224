import AsyncStorage from '@react-native-async-storage/async-storage';
import { create } from 'zustand';
import useAuthStore from './authStore';
import storyService from '../services/storyService';

const useStoryStore = create((set, get) => ({
  stories: [],
  currentStory: null,
  loading: false,
  error: null,

  // Carica le storie
  loadStories: async () => {
    try {
      set({ loading: true, error: null });

      // Ottieni l'utente corrente
      const { user } = useAuthStore.getState();
      if (!user) {
        console.warn('Nessun utente autenticato, impossibile caricare le storie');
        set({ stories: [], loading: false });
        return;
      }

      console.log('Caricamento storie per l\'utente:', user.id || user._id);

      // Prima tentiamo di caricare le storie dal server
      try {
        console.log('Tentativo di caricamento delle storie dal server...');
        const token = await storyService.getAuthToken();

        if (!token) {
          console.warn('Token di autenticazione non disponibile, caricamento solo da storage locale');
          throw new Error('Token non disponibile');
        }

        console.log('Token disponibile, invio richiesta al server...');

        // Timeout più lungo per il caricamento (30 secondi)
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 30000);

        // Se abbiamo un token, tentiamo di caricare le storie dal server
        const response = await fetch(`${storyService.baseUrl}`, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
          signal: controller.signal
        });

        // Pulisci il timeout
        clearTimeout(timeoutId);

        console.log('Risposta dal server - Status:', response.status);

        if (response.ok) {
          try {
            const responseText = await response.text();
            const data = JSON.parse(responseText);

            if (data.success && Array.isArray(data.stories)) {
              // Formatta le storie dal server
              const serverStories = data.stories.map(story => ({
                id: story.id || story._id,
                content: story.content || '',
                mediaUrl: story.mediaUrl,
                mediaType: story.mediaType || 'image',
                user: story.user || user.id,
                createdAt: story.createdAt || new Date().toISOString(),
                expiresAt: story.expiresAt || new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
                viewers: story.viewers || []
              }));

              console.log('Storie caricate dal server:', serverStories.length);

              // Carica anche le storie locali
              const storiesData = await AsyncStorage.getItem('stories');
              let localStories = [];

              if (storiesData) {
                try {
                  localStories = JSON.parse(storiesData);
                  console.log('Storie caricate da AsyncStorage:', localStories.length);

                  // Filtra le storie locali che non sono presenti sul server
                  const serverIds = serverStories.map(s => s.id);
                  const uniqueLocalStories = localStories.filter(
                    local => !serverIds.includes(local.id) && local.id.startsWith('story_')
                  );

                  console.log('Storie locali uniche:', uniqueLocalStories.length);

                  // Unisci le storie del server con quelle locali uniche
                  const allStories = [...serverStories, ...uniqueLocalStories];

                  // Salva tutte le storie in AsyncStorage
                  await AsyncStorage.setItem('stories', JSON.stringify(allStories));

                  // Aggiorna lo stato
                  set({ stories: allStories, loading: false });
                  return;
                } catch (parseError) {
                  console.error('Errore nel parsing delle storie locali:', parseError);
                  // Se c'è un errore nel parsing, usa solo le storie del server
                  await AsyncStorage.setItem('stories', JSON.stringify(serverStories));
                  set({ stories: serverStories, loading: false });
                  return;
                }
              } else {
                // Se non ci sono storie locali, usa solo quelle del server
                await AsyncStorage.setItem('stories', JSON.stringify(serverStories));
                set({ stories: serverStories, loading: false });
                return;
              }
            } else {
              console.warn('La risposta del server non contiene un array di storie:', data);
              // Continua con il caricamento da AsyncStorage
            }
          } catch (parseError) {
            console.error('Errore nel parsing della risposta JSON per le storie:', parseError);
            // Continua con il caricamento da AsyncStorage
          }
        } else {
          console.warn('Errore nella risposta del server:', response.status);
          // Continua con il caricamento da AsyncStorage
        }
      } catch (serverError) {
        console.error('Errore nel caricamento delle storie dal server:', serverError);
        // Continua con il caricamento da AsyncStorage
      }

      // Se il caricamento dal server fallisce, carica da AsyncStorage
      console.log('Caricamento delle storie da AsyncStorage...');
      try {
        const storiesData = await AsyncStorage.getItem('stories');
        if (storiesData) {
          const stories = JSON.parse(storiesData);
          console.log('Storie caricate da AsyncStorage:', stories.length);
          set({ stories, loading: false });
        } else {
          // Se non ci sono storie salvate, inizializza con un array vuoto
          await AsyncStorage.setItem('stories', JSON.stringify([]));
          set({ stories: [], loading: false });
        }
      } catch (storageError) {
        console.error('Errore nel caricamento delle storie da AsyncStorage:', storageError);
        set({ stories: [], loading: false, error: storageError.message });
      }
    } catch (error) {
      console.error('Errore nel caricamento delle storie:', error);
      set({ error: error.message, loading: false });
    }
  },

  // Crea una nuova storia
  createStory: async (content, mediaUri, mediaType = 'image') => {
    const { user } = useAuthStore.getState();
    if (!user) return;

    try {
      set({ loading: true, error: null });

      // Utilizziamo il servizio per creare la storia
      const result = await storyService.createStory({
        content,
        mediaUri,
        mediaType
      });

      if (!result.success) {
        throw new Error(result.error || 'Errore nella creazione della storia');
      }

      // Aggiungi la storia alla lista locale
      const newStories = [...get().stories, result.story];
      set({ stories: newStories, loading: false });

      // Salva la storia in locale
      try {
        const storiesData = await AsyncStorage.getItem('stories');
        const stories = storiesData ? JSON.parse(storiesData) : [];
        stories.push(result.story);
        await AsyncStorage.setItem('stories', JSON.stringify(stories));
      } catch (storageError) {
        console.error('Errore nel salvataggio della storia in AsyncStorage:', storageError);
      }

      return result.story.id;
    } catch (error) {
      console.error('Errore nella creazione della storia:', error);
      set({ error: error.message, loading: false });
      return null;
    }
  },

  // Elimina una storia
  deleteStory: async (storyId) => {
    try {
      set({ loading: true, error: null });

      // Rimuovi la storia dalla lista locale
      const newStories = get().stories.filter(story => story.id !== storyId);
      set({ stories: newStories, loading: false });

      // Rimuovi la storia da AsyncStorage
      try {
        const storiesData = await AsyncStorage.getItem('stories');
        if (storiesData) {
          const stories = JSON.parse(storiesData);
          const updatedStories = stories.filter(story => story.id !== storyId);
          await AsyncStorage.setItem('stories', JSON.stringify(updatedStories));
        }
      } catch (storageError) {
        console.error('Errore nella rimozione della storia da AsyncStorage:', storageError);
      }

      return true;
    } catch (error) {
      console.error('Errore nell\'eliminazione della storia:', error);
      set({ error: error.message, loading: false });
      return false;
    }
  },

  // Imposta la storia corrente
  setCurrentStory: (story) => {
    set({ currentStory: story });
  },

  // Imposta errore
  setError: (error) => set({ error }),
  
  // Pulisci errore
  clearError: () => set({ error: null }),
}));

export default useStoryStore;
