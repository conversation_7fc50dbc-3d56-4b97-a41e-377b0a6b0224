import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  TouchableOpacity,
  ActivityIndicator,
  Dimensions,
  Alert,
  StatusBar,
  Animated,
  Platform,
  TextInput,
  FlatList,
} from 'react-native';
import TextStatusViewer from '../components/status/TextStatusViewer';
import StatusViewers from '../components/status/StatusViewers';
import StatusReplyInput from '../components/status/StatusReplyInput';
import { useRoute, useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { Video } from 'expo-av';
import useStatusStore from '../store/statusStore';
import useAuthStore from '../store/authStore';

const { width, height } = Dimensions.get('window');

const StatusViewScreen = () => {
  const route = useRoute();
  const navigation = useNavigation();
  const { statusId, statuses: passedStatuses, initialIndex } = route.params;
  const { user } = useAuthStore();
  const { statuses, loading, error, viewStatus, deleteStatus, replyToStatus, getStatusReplies, getStatusViewers } = useStatusStore();
  const [currentStatus, setCurrentStatus] = useState(null);
  const [replies, setReplies] = useState([]);
  const [showReply, setShowReply] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);
  const [showViewers, setShowViewers] = useState(false);
  const videoRef = useRef(null);
  const progressAnim = useRef(new Animated.Value(0)).current;
  const progressAnimation = useRef(null);

  useEffect(() => {
    let status = null;

    // Se abbiamo statuses passati direttamente (per myStatus), usali
    if (passedStatuses && passedStatuses.length > 0) {
      status = passedStatuses[initialIndex || 0];
      // Converti le stringhe di data in oggetti Date se necessario
      if (typeof status.createdAt === 'string') {
        status.createdAt = new Date(status.createdAt);
      }
      if (typeof status.expiresAt === 'string') {
        status.expiresAt = new Date(status.expiresAt);
      }
    } else {
      // Altrimenti cerca negli statuses dello store
      status = statuses.find(s => s.id === statusId);
    }

    if (status) {
      setCurrentStatus(status);
      if (statusId) {
        viewStatus(statusId);
      }
      startProgressAnimation();
      loadReplies();
    }
  }, [statusId, statuses, passedStatuses, initialIndex]);

  const loadReplies = async () => {
    try {
      if (statusId) {
        const statusReplies = await getStatusReplies(statusId);
        setReplies(statusReplies);
      } else {
        // Se non abbiamo statusId (myStatus), non caricare risposte
        setReplies([]);
      }
    } catch (error) {
      console.error('Errore nel caricamento delle risposte:', error);
    }
  };

  const startProgressAnimation = () => {
    progressAnim.setValue(0);
    progressAnimation.current = Animated.timing(progressAnim, {
      toValue: 1,
      duration: 15000, // 15 secondi per visualizzare lo stato
      useNativeDriver: false,
    });
    progressAnimation.current.start(({ finished }) => {
      if (finished) {
        navigation.goBack();
      }
    });
  };

  const pauseProgressAnimation = () => {
    if (progressAnimation.current) {
      progressAnimation.current.stop();
    }
  };

  const resumeProgressAnimation = () => {
    startProgressAnimation();
  };

  const handleClose = () => {
    if (progressAnimation.current) {
      progressAnimation.current.stop();
    }
    navigation.goBack();
  };

  const handleDeleteStatus = () => {
    Alert.alert(
      'Elimina stato',
      'Sei sicuro di voler eliminare questo stato?',
      [
        { text: 'Annulla', style: 'cancel' },
        {
          text: 'Elimina',
          style: 'destructive',
          onPress: async () => {
            try {
              await deleteStatus(statusId);
              navigation.goBack();
            } catch (error) {
              Alert.alert('Errore', 'Impossibile eliminare lo stato');
            }
          }
        },
      ]
    );
  };

  const handleReply = async (text) => {
    try {
      if (!text.trim()) return;

      await replyToStatus(statusId, text);
      setShowReply(false);
      resumeProgressAnimation();
      loadReplies();

      // Mostra un feedback all'utente
      Alert.alert('Risposta inviata', 'La tua risposta è stata inviata con successo');
    } catch (error) {
      console.error('Errore nell\'invio della risposta:', error);
      Alert.alert('Errore', 'Impossibile inviare la risposta');
    }
  };

  const handleVideoPlaybackStatusUpdate = (status) => {
    if (status.isPlaying !== isPlaying) {
      setIsPlaying(status.isPlaying);
      if (status.isPlaying) {
        pauseProgressAnimation();
      } else {
        resumeProgressAnimation();
      }
    }
  };

  if (loading || !currentStatus) {
    return (
      <View style={styles.loadingContainer}>
        <StatusBar barStyle="light-content" backgroundColor="#000000" />
        <ActivityIndicator size="large" color="#1E88E5" />
      </View>
    );
  }

  const renderReplies = () => {
    if (!replies || replies.length === 0) return null;

    return (
      <View style={styles.repliesContainer}>
        <Text style={styles.repliesTitle}>Risposte</Text>
        <FlatList
          data={replies}
          keyExtractor={(item, index) => `reply-${index}`}
          renderItem={({ item }) => (
            <View style={styles.replyItem}>
              <Image
                source={{ uri: item.userPhotoURL || 'https://via.placeholder.com/40' }}
                style={styles.replyAvatar}
              />
              <View style={styles.replyContent}>
                <Text style={styles.replyName}>{item.userName || 'Utente'}</Text>
                <Text style={styles.replyText}>{item.text}</Text>
                <Text style={styles.replyTime}>
                  {new Date(item.timestamp?.toDate ? item.timestamp.toDate() : item.timestamp).toLocaleTimeString()}
                </Text>
              </View>
            </View>
          )}
          contentContainerStyle={styles.repliesList}
        />
      </View>
    );
  };

  return (
    <View style={styles.container}>
      <StatusBar hidden />

      <TouchableOpacity
        style={styles.closeButton}
        onPress={handleClose}
      >
        <Ionicons name="close" size={24} color="#FFFFFF" />
      </TouchableOpacity>

      <View style={styles.header}>
        <Image
          source={{ uri: currentStatus.userPhotoURL || 'https://via.placeholder.com/40' }}
          style={styles.userImage}
        />
        <View style={styles.userInfo}>
          <Text style={styles.userName}>{currentStatus.userName || 'Utente'}</Text>
          <TouchableOpacity onPress={() => setShowViewers(true)}>
            <Text style={styles.timestamp}>
              {new Date(currentStatus.createdAt?.toDate ? currentStatus.createdAt.toDate() : currentStatus.createdAt).toLocaleTimeString()}
              {currentStatus.viewers && currentStatus.viewers.length > 0 && ` · ${currentStatus.viewers.length} ${currentStatus.viewers.length === 1 ? 'visualizzazione' : 'visualizzazioni'}`}
            </Text>
          </TouchableOpacity>
        </View>
      </View>

      {currentStatus.type === 'text' ? (
        <TextStatusViewer status={currentStatus} />
      ) : currentStatus.type === 'image' ? (
        <Image
          source={{ uri: currentStatus.mediaUrl }}
          style={styles.mediaContent}
          resizeMode="contain"
        />
      ) : currentStatus.type === 'video' && (
        <TouchableOpacity
          style={styles.mediaContent}
          onPress={() => {
            if (videoRef.current) {
              if (isPlaying) {
                videoRef.current.pauseAsync();
              } else {
                videoRef.current.playAsync();
              }
            }
          }}
        >
          <Video
            ref={videoRef}
            source={{ uri: currentStatus.mediaUrl }}
            style={styles.mediaContent}
            resizeMode="contain"
            shouldPlay={false}
            isLooping={true}
            onPlaybackStatusUpdate={handleVideoPlaybackStatusUpdate}
          />
          {!isPlaying && (
            <View style={styles.playButton}>
              <Ionicons name="play" size={32} color="#FFFFFF" />
            </View>
          )}
        </TouchableOpacity>
      )}

      <View style={styles.footer}>
        <TouchableOpacity
          style={styles.replyButton}
          onPress={() => {
            pauseProgressAnimation();
            setShowReply(true);
          }}
        >
          <Ionicons name="arrow-up" size={24} color="#FFFFFF" />
          <Text style={styles.replyButtonText}>Rispondi</Text>
        </TouchableOpacity>
      </View>

      {showReply && (
        <StatusReplyInput
          onSend={handleReply}
          onFocus={() => pauseProgressAnimation()}
          onBlur={() => resumeProgressAnimation()}
        />
      )}

      {renderReplies()}

      <StatusViewers
        statusId={statusId}
        visible={showViewers}
        onClose={() => setShowViewers(false)}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#000',
  },
  closeButton: {
    position: 'absolute',
    top: 40,
    right: 20,
    zIndex: 1,
  },
  closeButtonText: {
    color: '#fff',
    fontSize: 24,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  userImage: {
    width: 40,
    height: 40,
    borderRadius: 20,
  },
  userInfo: {
    marginLeft: 12,
  },
  userName: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  timestamp: {
    color: '#fff',
    fontSize: 12,
    opacity: 0.8,
  },
  mediaContent: {
    width: width,
    height: height,
  },
  playButton: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: [{ translateX: -25 }, { translateY: -25 }],
  },
  playButtonText: {
    fontSize: 50,
  },
  progressContainer: {
    position: 'absolute',
    top: 20,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 10,
  },
  progressBar: {
    height: 3,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    borderRadius: 3,
    overflow: 'hidden',
    marginHorizontal: 2,
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#FFFFFF',
  },
  footer: {
    position: 'absolute',
    bottom: 20,
    left: 0,
    right: 0,
    alignItems: 'center',
  },
  replyButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
  },
  replyButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    marginLeft: 8,
  },
  repliesContainer: {
    position: 'absolute',
    bottom: 80,
    left: 0,
    right: 0,
    maxHeight: 200,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
  },
  repliesTitle: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
    padding: 16,
    paddingBottom: 8,
  },
  repliesList: {
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  replyItem: {
    flexDirection: 'row',
    marginBottom: 12,
  },
  replyAvatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#CCCCCC',
  },
  replyContent: {
    flex: 1,
    marginLeft: 12,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 12,
    padding: 8,
  },
  replyName: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: 'bold',
  },
  replyText: {
    color: '#FFFFFF',
    fontSize: 14,
    marginTop: 4,
  },
  replyTime: {
    color: 'rgba(255, 255, 255, 0.7)',
    fontSize: 12,
    marginTop: 4,
    alignSelf: 'flex-end',
  },
});

export default StatusViewScreen;