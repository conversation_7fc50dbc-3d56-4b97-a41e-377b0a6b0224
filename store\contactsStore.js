import AsyncStorage from '@react-native-async-storage/async-storage';
import { create } from 'zustand';
import * as Contacts from 'expo-contacts';
import axios from 'axios';
import useAuthStore from './authStore';
import { API_URL } from '../config/api';

const useContactsStore = create((set, get) => ({
  contacts: [],
  loading: false,
  error: null,

  // Carica i contatti
  loadContacts: async () => {
    try {
      set({ loading: true, error: null });

      const { user } = useAuthStore.getState();
      if (!user) throw new Error('Utente non autenticato');

      // Ottieni i contatti dal dispositivo
      const { status } = await Contacts.requestPermissionsAsync();
      if (status !== 'granted') {
        throw new Error('Permesso di accesso ai contatti negato');
      }

      const { data } = await Contacts.getContactsAsync({
        fields: [Contacts.Fields.PhoneNumbers, Contacts.Fields.Emails],
      });

      // Estrai numeri di telefono e email
      const phoneNumbers = data
        .filter(contact => contact.phoneNumbers && contact.phoneNumbers.length > 0)
        .map(contact => ({
          id: contact.id,
          name: contact.name,
          phoneNumber: contact.phoneNumbers[0].number.replace(/\s+/g, ''),
        }));

      const emails = data
        .filter(contact => contact.emails && contact.emails.length > 0)
        .map(contact => ({
          id: contact.id,
          name: contact.name,
          email: contact.emails[0].email.toLowerCase(),
        }));

      // Cerca utenti REALI sul server HP che corrispondono ai contatti
      console.log('🔄 Cercando utenti registrati sul server HP...');

      // Prepara i numeri di telefono per la richiesta
      const phoneNumbersToCheck = phoneNumbers.map(c => c.phoneNumber).slice(0, 50);

      let appUsers = [];

      try {
        // Ottieni il token di autenticazione
        const token = await AsyncStorage.getItem('@trendychat:token');

        // Chiama il server HP per ottenere tutti gli utenti registrati dal database MongoDB
        console.log('🔗 Chiamando endpoint:', `${API_URL}/users`);
        const response = await axios.get(`${API_URL}/users`, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });

        console.log('📡 Risposta server ricevuta:', response.status);

        // Gestisci diverse strutture di risposta dal server
        let users = [];
        if (response.data && response.data.users) {
          users = response.data.users;
        } else if (response.data && Array.isArray(response.data)) {
          users = response.data;
        } else if (response.data) {
          users = [response.data];
        }

        appUsers = users.map(user => ({
          id: user.id || user._id,
          displayName: user.displayName || user.name,
          photoURL: user.photoURL || user.avatar,
          status: user.status || 'Hey, sto usando TrendyChat!',
          phoneNumber: user.phoneNumber,
          email: user.email,
          online: user.online || false,
          lastSeen: user.lastSeen || new Date().toISOString(),
        }));

        console.log(`✅ Trovati ${appUsers.length} utenti registrati su TrendyChat`);
        console.log('📋 Primi 3 utenti:', appUsers.slice(0, 3));
      } catch (error) {
        console.error('❌ Errore nel caricamento contatti dal server:', error);
        // Se il server non risponde, usa alcuni contatti di fallback
        appUsers = [];
      }

      // Usa solo i contatti REALI dal server HP
      const allContacts = appUsers;

      // INCLUDI l'utente corrente per permettere self-broadcast (come WhatsApp)
      const currentUserAsContact = {
        id: user.id,
        displayName: user.displayName + ' (Tu)',
        photoURL: user.photoURL,
        status: user.status,
        phoneNumber: user.phoneNumber,
        email: user.email,
        online: true,
        lastSeen: new Date().toISOString(),
      };

      // Aggiungi l'utente corrente all'inizio della lista
      const contactsWithSelf = [currentUserAsContact, ...allContacts.filter(contact => contact.id !== user.id)];

      set({ contacts: contactsWithSelf, loading: false });
    } catch (error) {
      console.error('Errore nel caricamento dei contatti:', error);
      set({ error: error.message, loading: false });
    }
  },



  // Blocca un contatto
  blockContact: async (contactId) => {
    try {
      set({ loading: true, error: null });

      const { user } = useAuthStore.getState();
      if (!user) throw new Error('Utente non autenticato');

      // Aggiorna lo store di autenticazione
      const updatedUser = {
        ...user,
        blockedContacts: [...(user.blockedContacts || []), contactId],
      };

      // Salva i contatti bloccati in AsyncStorage
      await AsyncStorage.setItem('blockedContacts', JSON.stringify(updatedUser.blockedContacts));

      useAuthStore.setState({ user: updatedUser });

      set({ loading: false });
    } catch (error) {
      console.error('Errore nel blocco del contatto:', error);
      set({ error: error.message, loading: false });
    }
  },

  // Sblocca un contatto
  unblockContact: async (contactId) => {
    try {
      set({ loading: true, error: null });

      const { user } = useAuthStore.getState();
      if (!user) throw new Error('Utente non autenticato');

      // Aggiorna lo store di autenticazione
      const updatedUser = {
        ...user,
        blockedContacts: (user.blockedContacts || []).filter(id => id !== contactId),
      };

      // Salva i contatti bloccati in AsyncStorage
      await AsyncStorage.setItem('blockedContacts', JSON.stringify(updatedUser.blockedContacts));

      useAuthStore.setState({ user: updatedUser });

      set({ loading: false });
    } catch (error) {
      console.error('Errore nello sblocco del contatto:', error);
      set({ error: error.message, loading: false });
    }
  },

  // Imposta errore
  setError: (error) => set({ error }),

  // Pulisci errore
  clearError: () => set({ error: null }),
}));

export default useContactsStore;
