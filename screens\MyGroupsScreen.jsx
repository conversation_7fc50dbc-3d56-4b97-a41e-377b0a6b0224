import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Image,
  ActivityIndicator,
  Alert,
  SafeAreaView,
  StatusBar,
  Platform,
  RefreshControl,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import useGroupStore from '../store/groupStore';
import useAuthStore from '../store/authStore';

const MyGroupsScreen = () => {
  const navigation = useNavigation();
  const { user } = useAuthStore();
  const { groups, loading, error, loadGroups, deleteGroup } = useGroupStore();
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    console.log('🔄 MyGroupsScreen: Caricamento gruppi utente...');
    loadGroups();
  }, []);

  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      await loadGroups();
    } catch (error) {
      console.error('❌ Errore refresh gruppi:', error);
    } finally {
      setRefreshing(false);
    }
  };

  const handleGroupPress = (group) => {
    console.log('📱 Aprendo gruppo:', group.name);
    navigation.navigate('GroupChat', {
      groupId: group.id,
      name: group.name,
      avatar: group.photoURL
    });
  };

  const handleGroupInfo = (group) => {
    console.log('ℹ️ Info gruppo:', group.name);
    navigation.navigate('GroupInfo', {
      groupId: group.id,
      name: group.name,
      avatar: group.photoURL
    });
  };

  const handleCreateGroup = () => {
    console.log('🆕 Creazione nuovo gruppo');
    navigation.navigate('NewGroup');
  };

  const handleDeleteGroup = (group) => {
    console.log('🗑️ MyGroupsScreen: Richiesta cancellazione gruppo:', group.name);

    Alert.alert(
      'Elimina Gruppo',
      `Sei sicuro di voler eliminare il gruppo "${group.name}"?\n\nQuesta azione eliminerà:\n• Il gruppo\n• Tutti i messaggi\n• Tutti i media condivisi\n\nQuesta azione non può essere annullata.`,
      [
        {
          text: 'Annulla',
          style: 'cancel',
        },
        {
          text: 'Elimina',
          style: 'destructive',
          onPress: async () => {
            try {
              console.log('🗑️ MyGroupsScreen: Confermata cancellazione gruppo:', group.id);
              await deleteGroup(group.id);
              Alert.alert('Successo', `Il gruppo "${group.name}" è stato eliminato completamente.`);
            } catch (error) {
              console.error('❌ Errore cancellazione gruppo:', error);
              Alert.alert('Errore', 'Impossibile eliminare il gruppo. Riprova più tardi.');
            }
          },
        },
      ]
    );
  };

  const renderGroupItem = ({ item }) => (
    <TouchableOpacity
      style={styles.groupItem}
      onPress={() => handleGroupPress(item)}
      onLongPress={() => handleDeleteGroup(item)}
    >
      <View style={styles.groupImageContainer}>
        <LinearGradient
          colors={['#1E88E5', '#D81B60']}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 0 }}
          style={styles.imageGradientBorder}
        >
          <Image
            source={{
              uri: item.photoURL || 'https://via.placeholder.com/60/1E88E5/FFFFFF?text=G'
            }}
            style={styles.groupImage}
          />
        </LinearGradient>
      </View>

      <View style={styles.groupInfo}>
        <Text style={styles.groupName} numberOfLines={1}>
          {item.name}
        </Text>
        <Text style={styles.groupMembers} numberOfLines={1}>
          {item.members?.length || 0} membri
        </Text>
        {item.lastMessage && (
          <Text style={styles.lastMessage} numberOfLines={1}>
            {item.lastMessage}
          </Text>
        )}
      </View>

      <View style={styles.groupActions}>
        <TouchableOpacity
          style={styles.actionButton}
          onPress={() => handleGroupInfo(item)}
        >
          <Ionicons name="information-circle-outline" size={20} color="#AAAAAA" />
        </TouchableOpacity>
      </View>
    </TouchableOpacity>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <LinearGradient
        colors={['#1E88E5', '#D81B60']}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 0 }}
        style={styles.emptyIconContainer}
      >
        <Ionicons name="people-outline" size={48} color="#FFFFFF" />
      </LinearGradient>

      <Text style={styles.emptyTitle}>Nessun gruppo</Text>
      <Text style={styles.emptyDescription}>
        Non hai ancora creato o partecipato a nessun gruppo.
      </Text>

      <TouchableOpacity
        style={styles.createGroupButton}
        onPress={handleCreateGroup}
      >
        <LinearGradient
          colors={['#1E88E5', '#D81B60']}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 0 }}
          style={styles.createGroupGradient}
        >
          <Ionicons name="add" size={20} color="#FFFFFF" />
          <Text style={styles.createGroupText}>Crea il tuo primo gruppo</Text>
        </LinearGradient>
      </TouchableOpacity>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#1E88E5" />

      {/* Header con stile TrendyChat */}
      <LinearGradient
        colors={['#1E88E5', '#D81B60']}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 0 }}
        style={styles.header}
      >
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color="#FFFFFF" />
        </TouchableOpacity>

        <Text style={styles.headerTitle}>I miei gruppi</Text>

        <TouchableOpacity
          style={styles.addButton}
          onPress={handleCreateGroup}
        >
          <Ionicons name="add" size={24} color="#FFFFFF" />
        </TouchableOpacity>
      </LinearGradient>

      {/* Lista gruppi */}
      <View style={styles.content}>
        {loading && !refreshing ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#1E88E5" />
            <Text style={styles.loadingText}>Caricamento gruppi...</Text>
          </View>
        ) : (
          <FlatList
            data={groups}
            keyExtractor={(item) => item.id}
            renderItem={renderGroupItem}
            ListEmptyComponent={renderEmptyState}
            refreshControl={
              <RefreshControl
                refreshing={refreshing}
                onRefresh={handleRefresh}
                colors={['#1E88E5']}
                tintColor="#1E88E5"
              />
            }
            contentContainerStyle={groups.length === 0 ? styles.emptyList : styles.list}
            showsVerticalScrollIndicator={false}
          />
        )}
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#121212',
    paddingTop: StatusBar.currentHeight || 0,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
    height: 60,
  },
  backButton: {
    padding: 8,
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  addButton: {
    padding: 8,
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    flex: 1,
    backgroundColor: '#121212',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#AAAAAA',
  },
  list: {
    padding: 16,
  },
  emptyList: {
    flex: 1,
  },
  groupItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#1E1E1E',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: '#2A2A2A',
  },
  groupImageContainer: {
    marginRight: 16,
  },
  imageGradientBorder: {
    width: 62,
    height: 62,
    borderRadius: 31,
    padding: 2,
    justifyContent: 'center',
    alignItems: 'center',
  },
  groupImage: {
    width: 58,
    height: 58,
    borderRadius: 29,
    backgroundColor: '#2A2A2A',
  },
  groupInfo: {
    flex: 1,
  },
  groupName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 4,
  },
  groupMembers: {
    fontSize: 14,
    color: '#AAAAAA',
    marginBottom: 2,
  },
  lastMessage: {
    fontSize: 12,
    color: '#888888',
  },
  groupActions: {
    marginLeft: 12,
  },
  actionButton: {
    padding: 8,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  emptyIconContainer: {
    width: 96,
    height: 96,
    borderRadius: 48,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 24,
  },
  emptyTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 12,
  },
  emptyDescription: {
    fontSize: 16,
    color: '#AAAAAA',
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 32,
  },
  createGroupButton: {
    borderRadius: 25,
    overflow: 'hidden',
  },
  createGroupGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingVertical: 12,
  },
  createGroupText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginLeft: 8,
  },
});

export default MyGroupsScreen;
