/**
 * Configurazione per estendere AsyncStorage con funzionalità di tipo Firestore
 * Questo file serve a configurare AsyncStorage per utilizzare mockStorage per le operazioni di tipo Firestore
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import mockStorage from './asyncStorageMock';

// Estendi AsyncStorage con i metodi di mockStorage
AsyncStorage.collection = mockStorage.collection.bind(mockStorage);

// Funzioni di utilità per simulare Firestore
export const query = (ref, ...conditions) => ({
  ...ref,
  conditions
});

export const where = (field, op, value) => ({ field, op, value });

export const onSnapshot = (q, callback) => {
  // Simulazione di una chiamata asincrona
  setTimeout(() => {
    callback({
      docs: []
    });
  }, 100);
  
  // Restituisci una funzione di unsubscribe fittizia
  return () => {};
};

export const doc = (collection, id) => collection.doc(id);

export const updateDoc = async (docRef, data) => {
  return docRef.update(data);
};

export const addDoc = async (collectionRef, data) => {
  const id = `doc_${Date.now()}`;
  await collectionRef.doc(id).set(data);
  return { id };
};

export const arrayUnion = (value) => ({ __op: 'arrayUnion', value });
export const arrayRemove = (value) => ({ __op: 'arrayRemove', value });
export const serverTimestamp = () => new Date();

export default AsyncStorage;
