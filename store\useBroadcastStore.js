import { create } from 'zustand';
import axios from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';

// URL base API TrendyChat (stesso del server HP)
const API_URL = 'http://192.168.1.66:3001/api';

const useBroadcastStore = create((set, get) => ({
  // Stato TrendyChat Broadcast
  broadcasts: [],
  currentBroadcast: null,
  loading: false,
  error: null,

  // Azioni TrendyChat Style

  // Carica tutte le liste broadcast dell'utente
  loadBroadcasts: async () => {
    try {
      set({ loading: true, error: null });
      console.log('🔄 TrendyChat BroadcastStore: Caricando liste broadcast...');

      const token = await AsyncStorage.getItem('userToken');
      if (!token) {
        throw new Error('Token non trovato');
      }

      const response = await axios.get(`${API_URL}/broadcasts`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      console.log('📡 TrendyChat: Liste broadcast ricevute:', response.data);

      if (response.data.success) {
        set({ 
          broadcasts: response.data.broadcasts || [],
          loading: false 
        });
        console.log('✅ TrendyChat: Caricate', response.data.broadcasts?.length || 0, 'liste broadcast');
      } else {
        throw new Error(response.data.message || 'Errore caricamento broadcast');
      }

    } catch (error) {
      console.error('❌ TrendyChat BroadcastStore: Errore caricamento:', error);
      set({ 
        error: error.message || 'Errore caricamento liste broadcast',
        loading: false 
      });
    }
  },

  // Crea una nuova lista broadcast TrendyChat
  createBroadcast: async (name, description, recipients) => {
    try {
      set({ loading: true, error: null });
      console.log('📤 TrendyChat: Creando lista broadcast:', name);

      const token = await AsyncStorage.getItem('userToken');
      if (!token) {
        throw new Error('Token non trovato');
      }

      const response = await axios.post(`${API_URL}/broadcasts`, {
        name: name.trim(),
        description: description?.trim() || '',
        recipients: recipients
      }, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      console.log('📡 TrendyChat: Risposta creazione broadcast:', response.data);

      if (response.data.success) {
        const newBroadcast = response.data.broadcast;
        
        // Aggiorna lo stato locale TrendyChat
        set(state => ({
          broadcasts: [newBroadcast, ...state.broadcasts],
          loading: false
        }));

        console.log('✅ TrendyChat: Lista broadcast creata:', newBroadcast.id);
        return newBroadcast;
      } else {
        throw new Error(response.data.message || 'Errore creazione broadcast');
      }

    } catch (error) {
      console.error('❌ TrendyChat: Errore creazione broadcast:', error);
      set({ 
        error: error.message || 'Errore creazione lista broadcast',
        loading: false 
      });
      throw error;
    }
  },

  // Ottieni dettagli di una lista broadcast
  getBroadcast: async (broadcastId) => {
    try {
      set({ loading: true, error: null });
      console.log('🔍 TrendyChat: Caricando broadcast:', broadcastId);

      const token = await AsyncStorage.getItem('userToken');
      if (!token) {
        throw new Error('Token non trovato');
      }

      const response = await axios.get(`${API_URL}/broadcasts/${broadcastId}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.data.success) {
        set({ 
          currentBroadcast: response.data.broadcast,
          loading: false 
        });
        console.log('✅ TrendyChat: Broadcast caricato:', response.data.broadcast.id);
        return response.data.broadcast;
      } else {
        throw new Error(response.data.message || 'Errore caricamento broadcast');
      }

    } catch (error) {
      console.error('❌ TrendyChat: Errore caricamento broadcast:', error);
      set({ 
        error: error.message || 'Errore caricamento lista broadcast',
        loading: false 
      });
      throw error;
    }
  },

  // Elimina una lista broadcast
  deleteBroadcast: async (broadcastId) => {
    try {
      set({ loading: true, error: null });
      console.log('🗑️ TrendyChat: Eliminando broadcast:', broadcastId);

      const token = await AsyncStorage.getItem('userToken');
      if (!token) {
        throw new Error('Token non trovato');
      }

      const response = await axios.delete(`${API_URL}/broadcasts/${broadcastId}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.data.success) {
        // Rimuovi dallo stato locale TrendyChat
        set(state => ({
          broadcasts: state.broadcasts.filter(b => b.id !== broadcastId),
          currentBroadcast: state.currentBroadcast?.id === broadcastId 
            ? null 
            : state.currentBroadcast,
          loading: false
        }));

        console.log('✅ TrendyChat: Broadcast eliminato:', broadcastId);
      } else {
        throw new Error(response.data.message || 'Errore eliminazione broadcast');
      }

    } catch (error) {
      console.error('❌ TrendyChat: Errore eliminazione broadcast:', error);
      set({ 
        error: error.message || 'Errore eliminazione lista broadcast',
        loading: false 
      });
      throw error;
    }
  },

  // Utility TrendyChat
  setCurrentBroadcast: (broadcast) => {
    set({ currentBroadcast: broadcast });
  },

  clearError: () => {
    set({ error: null });
  },

  reset: () => {
    set({
      broadcasts: [],
      currentBroadcast: null,
      loading: false,
      error: null
    });
  }
}));

export default useBroadcastStore;
