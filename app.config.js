// 🔧 CONFIGURAZIONE SOLO PER SERVER HP + SISTEMA IBRIDO

module.exports = {
  id: 'd7108999-f899-4c91-bbe3-741355340522',
  owner: 'sunparadise78',
  name: 'Trendy<PERSON><PERSON>',
  slug: 'trendychat',
  version: '1.0.0',
  orientation: 'portrait',
  icon: './assets/icon.png',
  userInterfaceStyle: 'dark',
  // Splash screen disabilitata temporaneamente per risolvere problemi di build
  // splash: {
  //   image: './assets/icon.png',
  //   resizeMode: 'contain',
  //   backgroundColor: '#000000'
  // },
  assetBundlePatterns: [
    '**/*'
  ],
  ios: {
    supportsTablet: true,
    bundleIdentifier: 'com.trendychat.app',
    infoPlist: {
      NSCameraUsageDescription: "TrendyChat richiede l'accesso alla fotocamera per le videochiamate e l'invio di foto",
      NSMicrophoneUsageDescription: "TrendyChat richiede l'accesso al microfono per le chiamate audio e i messaggi vocali",
      NSPhotoLibraryUsageDescription: "TrendyChat richiede l'accesso alla galleria per condividere foto e video",
      NSContactsUsageDescription: "TrendyChat richiede l'accesso ai contatti per trovare i tuoi amici",
      NSPhotoLibraryAddUsageDescription: "TrendyChat richiede l'accesso per salvare foto e video nella tua galleria",
      NSBluetoothAlwaysUsageDescription: "TrendyChat richiede l'accesso al Bluetooth per le chiamate audio",
      NSLocationWhenInUseUsageDescription: "TrendyChat richiede l'accesso alla posizione per condividere la tua ubicazione",
      NSLocationAlwaysAndWhenInUseUsageDescription: "TrendyChat richiede l'accesso alla posizione per condividere la tua ubicazione"
    }
  },
  android: {
    adaptiveIcon: {
      foregroundImage: './assets/icon.png',
      backgroundColor: '#000000'
    },
    package: 'com.trendychat.app',
    permissions: [
      "android.permission.RECORD_AUDIO",
      "android.permission.CAMERA",
      "android.permission.READ_CONTACTS",
      "android.permission.WRITE_CONTACTS",
      "android.permission.READ_EXTERNAL_STORAGE",
      "android.permission.WRITE_EXTERNAL_STORAGE",
      "android.permission.VIBRATE",
      "android.permission.BLUETOOTH",
      "android.permission.MODIFY_AUDIO_SETTINGS",
      "android.permission.INTERNET",
      "android.permission.ACCESS_NETWORK_STATE",
      "android.permission.ACCESS_WIFI_STATE",
      "android.permission.ACCESS_FINE_LOCATION",
      "android.permission.ACCESS_COARSE_LOCATION"
    ]
  },
  web: {
    favicon: './assets/icon.png'
  },

  extra: {
    // 🔧 CONFIGURAZIONE SERVER HP + SISTEMA IBRIDO
    serverHP: "http://192.168.1.66:3001",
    hybridSignaling: {
      primary: "wss://trendychat-signaling.onrender.com",
      fallback: "wss://signaling.trendychat.it",
      sfu: "wss://sfu.trendychat.it",
      turn: "turn.trendychat.it:3478"
    },
    eas: {
      projectId: "d7108999-f899-4c91-bbe3-741355340522"
    }
  },
  plugins: [
    "expo-image-picker",
    "expo-notifications",
    "expo-contacts",
    "expo-font",
    "expo-camera",
    "expo-av",
    "expo-location",
    "expo-dev-client",

    // Temporaneamente rimosso per debug
    // [
    //   "react-native-vision-camera",
    //   {
    //     "cameraPermissionText": "TrendyChat richiede l'accesso alla fotocamera per le dirette live",
    //     "enableMicrophonePermission": true,
    //     "microphonePermissionText": "TrendyChat richiede l'accesso al microfono per le dirette live"
    //   }
    // ],
    [
      "expo-build-properties",
      {
        "android": {
          "compileSdkVersion": 35,
          "targetSdkVersion": 34,
          "buildToolsVersion": "35.0.0"
        },
        "ios": {
          "deploymentTarget": "15.1"
        }
      }
    ]
  ],
  // Configurazione per caricare i font di @expo/vector-icons
  packagerOpts: {
    config: {
      resolver: {
        assetExts: ["ttf", "otf"]
      }
    }
  },
  newArchEnabled: true,
  runtimeVersion: {
    policy: "sdkVersion"
  },
  updates: {
    url: "https://u.expo.dev/d7108999-f899-4c91-bbe3-741355340522"
  }
};