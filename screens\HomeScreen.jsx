import React, { useEffect, useState, useRef } from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  TouchableWithoutFeedback,
  StyleSheet,
  ActivityIndicator,
  Image,
  StatusBar,
  Animated,
  Platform,
  Dimensions,
  SafeAreaView,
  Alert,
  Modal,
  TextInput,
  ScrollView,
  ActionSheetIOS,
} from 'react-native';
import contactSearchService from '../services/contactSearchService';

const { width, height } = Dimensions.get('window');
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import useAuthStore from '../store/authStore';
import useChatStore from '../store/useChatStore';
import colors from '../theme/colors';
import { format, isToday, isYesterday } from 'date-fns';
import { it } from 'date-fns/locale';
import ChatBot from '../components/ChatBot';
import MenuModal from '../components/MenuModal';

const HomeScreen = () => {
  const navigation = useNavigation();
  const { logout, user } = useAuthStore();
  const chatStore = useChatStore();
  const { loadChats, createChat, deleteChat, chats } = chatStore;  // ✅ USA CHATS DAL STORE + ELIMINA
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [refreshing, setRefreshing] = useState(false);
  const [isChatBotVisible, setIsChatBotVisible] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredChats, setFilteredChats] = useState([]);
  const [activeFilter, setActiveFilter] = useState('all'); // 'all', 'unread', 'favorites', 'groups'
  const [showArchived, setShowArchived] = useState(false);
  const [archivedChats, setArchivedChats] = useState([]);

  // Carica le chat reali dal server
  useEffect(() => {
    const loadRealChats = async () => {
      if (user?.id) {
        try {
          setLoading(true);
          await loadChats(user.id);
          console.log('✅ Chat caricate dal server');
        } catch (error) {
          console.error('❌ Errore caricamento chat:', error);
        } finally {
          setLoading(false);
        }
      }
    };

    loadRealChats();
  }, [user?.id]);

  // 🔄 RICARICA AUTOMATICA QUANDO LA SCHERMATA TORNA IN FOCUS (PRESERVA ARCHIVIATE)
  useFocusEffect(
    React.useCallback(() => {
      console.log('🏠 HomeScreen: Schermata in focus, ricaricando chat...');
      if (user?.id) {
        // 🔧 PRESERVA CHAT ARCHIVIATE: ricarica dal server ma mantieni archiviazione locale
        const preserveArchivedChats = async () => {
          const archivedChatIds = archivedChats.map(c => c.id);
          console.log('🗂️ Chat archiviate da preservare:', archivedChatIds);

          await loadChats(user.id);

          // Rimuovi dalle chat normali quelle che sono archiviate
          if (archivedChatIds.length > 0) {
            const chatStore = useChatStore.getState();
            const filteredChats = chatStore.chats.filter(chat => !archivedChatIds.includes(chat.id));
            useChatStore.setState({ chats: filteredChats });
            console.log('🧹 Rimosse chat archiviate dalla lista normale dopo ricaricamento');
          }
        };

        preserveArchivedChats();
      }
    }, [user?.id, archivedChats])
  );

  // Stato per i risultati della ricerca utenti
  const [userSearchResults, setUserSearchResults] = useState([]);
  const [isSearchingUsers, setIsSearchingUsers] = useState(false);
  const [showUserResults, setShowUserResults] = useState(false);

  // Filtra le chat in base alla query di ricerca e ai filtri attivi
  useEffect(() => {
    // 🧹 PULIZIA DUPLICATI NELLE CHAT NORMALI
    if (!showArchived && chats.length > 0) {
      const uniqueChats = chats.filter((chat, index, self) =>
        index === self.findIndex(c => c.id === chat.id)
      );
      if (uniqueChats.length !== chats.length) {
        console.log('🧹 Rimuovendo duplicati dalle chat normali');
        useChatStore.setState({ chats: uniqueChats });
        return; // Esci per evitare di processare chat duplicate
      }
    }

    // Inizia con tutte le chat o le chat archiviate
    let filtered = showArchived ? archivedChats : chats;
    console.log('🔍 Filtro chat - showArchived:', showArchived);
    console.log('🔍 Chat normali disponibili:', chats.length);
    console.log('🔍 Chat archiviate disponibili:', archivedChats.length);
    console.log('🔍 Chat selezionate per filtro:', filtered.length);

    // Applica il filtro di ricerca se presente
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase().trim();
      filtered = filtered.filter(chat => {
        const otherUser = chat.participants.find(p => p.id !== 'currentUser');
        const name = otherUser?.name?.toLowerCase() || '';
        const lastMessage = chat.lastMessage?.toLowerCase() || '';
        const phone = otherUser?.phone || '';

        return name.includes(query) || lastMessage.includes(query) || phone.includes(query);
      });
    }

    // Applica i filtri di categoria
    if (activeFilter !== 'all') {
      console.log('🔍 Applicando filtro:', activeFilter);
      console.log('🔍 Chat da filtrare:', filtered.map(c => ({
        id: c.id,
        name: c.name,
        unreadCount: c.unreadCount,
        isFavorite: c.isFavorite,
        isGroup: c.isGroup
      })));

      filtered = filtered.filter(chat => {
        switch (activeFilter) {
          case 'unread':
            const hasUnread = chat.unreadCount > 0;
            console.log(`📧 Chat ${chat.id}: unreadCount=${chat.unreadCount}, passa filtro=${hasUnread}`);
            return hasUnread;
          case 'favorites':
            const isFav = chat.isFavorite;
            console.log(`⭐ Chat ${chat.id}: isFavorite=${chat.isFavorite}, passa filtro=${isFav}`);
            return isFav;
          case 'groups':
            const isGrp = chat.isGroup;
            console.log(`👥 Chat ${chat.id}: isGroup=${chat.isGroup}, passa filtro=${isGrp}`);
            return isGrp;
          default:
            return true;
        }
      });

      console.log('🔍 Chat dopo filtro:', filtered.length);
    }

    setFilteredChats(filtered);
    console.log('✅ Chat filtrate finali:', filtered.length);
    console.log('📋 IDs chat filtrate:', filtered.map(c => c.id));
    if (showArchived && filtered.length > 0) {
      console.log('🔍 STRUTTURA CHAT ARCHIVIATA:', JSON.stringify(filtered[0], null, 2));
    }
  }, [searchQuery, chats, activeFilter, showArchived, archivedChats]);

  // Cerca utenti per numero di telefono quando la query cambia
  useEffect(() => {
    const searchUsers = async () => {
      // Verifica se la query è un numero di telefono
      if (searchQuery.trim() && contactSearchService.isPhoneNumber(searchQuery)) {
        setIsSearchingUsers(true);
        setShowUserResults(true);

        try {
          const results = await contactSearchService.searchUsers(searchQuery);
          setUserSearchResults(results);
        } catch (error) {
          console.error('Errore nella ricerca degli utenti:', error);
          setUserSearchResults([]);
        } finally {
          setIsSearchingUsers(false);
        }
      } else {
        setShowUserResults(false);
        setUserSearchResults([]);
      }
    };

    // Esegui la ricerca con un piccolo ritardo per evitare troppe chiamate API
    const timeoutId = setTimeout(searchUsers, 500);

    return () => clearTimeout(timeoutId);
  }, [searchQuery]);

  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      // Ricarica le chat reali dal server
      await loadChats();
      console.log('✅ Chat ricaricate dal server');
    } catch (error) {
      console.error('❌ Errore durante il refresh:', error);
    } finally {
      setRefreshing(false);
    }
  };

  const handleLogout = async () => {
    try {
      await logout();
    } catch (error) {
      console.error('Errore durante il logout:', error);
    }
  };

  const ChatItem = ({ item, onPress, onLongPress }) => {
    const otherUser = item.participants.find(p => p.id !== 'currentUser');
    const isOnline = otherUser?.online;
    const lastMessageTime = item.lastMessageTime;
    const scaleAnim = useRef(new Animated.Value(1)).current;

    // Formatta l'orario dell'ultimo messaggio
    const formatMessageTime = (timestamp) => {
      if (!timestamp) return '';

      const date = timestamp;

      if (isToday(date)) {
        return format(date, 'HH:mm');
      } else if (isYesterday(date)) {
        return 'Ieri';
      } else {
        return format(date, 'dd/MM/yyyy');
      }
    };

    const formattedTime = formatMessageTime(lastMessageTime);

    // Animazione al tocco
    const handlePressIn = () => {
      Animated.spring(scaleAnim, {
        toValue: 0.97,
        friction: 5,
        tension: 40,
        useNativeDriver: true
      }).start();
    };

    const handlePressOut = () => {
      Animated.spring(scaleAnim, {
        toValue: 1,
        friction: 3,
        tension: 40,
        useNativeDriver: true
      }).start();
    };

    return (
      <TouchableOpacity
        activeOpacity={1}
        onPress={() => onPress(item)}
        onLongPress={() => onLongPress(item)}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
      >
        <Animated.View style={[
          styles.chatItem,
          item.unreadCount > 0 && styles.unreadChat,
          { transform: [{ scale: scaleAnim }] }
        ]}>
          <View style={styles.avatarContainer}>
            {item.photoURL ? (
              <Image
                source={{ uri: item.photoURL }}
                style={styles.avatar}
              />
            ) : (
              <LinearGradient
                colors={['#1E88E5', '#D81B60']}
                style={[styles.avatar, styles.defaultAvatar]}
              >
                <Text style={styles.avatarText}>
                  {(otherUser?.name || 'U').charAt(0).toUpperCase()}
                </Text>
              </LinearGradient>
            )}
            {isOnline && <View style={styles.onlineIndicator} />}
          </View>

          <View style={styles.chatInfo}>
            <View style={styles.chatHeader}>
              <Text style={[styles.chatName, item.unreadCount > 0 && styles.unreadText]}>
                {otherUser?.name || 'Utente'}
              </Text>
              {formattedTime && (
                <Text style={styles.timeText}>
                  {formattedTime}
                </Text>
              )}
            </View>

            <View style={styles.messagePreview}>
              <Text style={[styles.lastMessage, item.unreadCount > 0 && styles.unreadText]} numberOfLines={1}>
                {item.lastMessage || 'Nessun messaggio'}
              </Text>
              {item.unreadCount > 0 && (
                <View style={styles.unreadBadge}>
                  <Text style={styles.unreadCount}>{item.unreadCount}</Text>
                </View>
              )}
            </View>
          </View>
        </Animated.View>
      </TouchableOpacity>
    );
  };

  const handleChatPress = (chat) => {
    const otherUser = chat.participants.find(p => p.id !== 'currentUser');

    // 🔍 DEBUG: Log per verificare la logica
    console.log('🔍 HOMESCREEN: Chat cliccata:', chat.id);
    console.log('🔍 HOMESCREEN: È un gruppo?', chat.id.startsWith('group_'));

    // ✅ DISTINGUI TRA CHAT PRIVATE E GRUPPI USANDO isPrivateChat
    if (chat.isPrivateChat === true) {
      // 💬 CHAT PRIVATA: Usa ChatRoomScreen
      console.log('💬 HOMESCREEN: Navigando a ChatRoom (chat privata)');
      navigation.navigate('ChatRoom', {
        chatId: chat.id,
        name: otherUser?.name || 'Utente',
        avatar: otherUser?.avatar || otherUser?.photoURL || chat.photoURL,
        isOnline: otherUser?.online || false,
        userId: otherUser?.id
      });
    } else {
      // 👥 GRUPPO: Usa GroupChatScreen (menu veloce)
      console.log('👥 HOMESCREEN: Navigando a GroupChat (gruppo reale)');
      navigation.navigate('GroupChat', {
        groupId: chat.id,
        name: chat.name || otherUser?.name || 'Gruppo',
        avatar: chat.photoURL || otherUser?.avatar || otherUser?.photoURL
      });
    }
  };

  const handleChatLongPress = (chat) => {
    console.log('🔍 Long press su chat:', chat.id);

    // ActionSheet per tutte le piattaforme (stile WhatsApp)
    const options = ['Archivia chat', 'Aggiungi ai preferiti', 'Silenzia notifiche', 'Elimina chat', 'Annulla'];
    const destructiveButtonIndex = 3; // Elimina chat
    const cancelButtonIndex = 4; // Annulla

    if (Platform.OS === 'ios') {
      ActionSheetIOS.showActionSheetWithOptions(
        {
          options,
          destructiveButtonIndex,
          cancelButtonIndex,
          title: 'Opzioni chat'
        },
        (buttonIndex) => {
          handleActionSheetPress(buttonIndex, chat);
        }
      );
    } else {
      // Per Android - logica dinamica per archivio
      const isArchived = showArchived; // Se siamo nella vista archiviate

      Alert.alert(
        'Opzioni chat',
        '',
        [
          {
            text: 'Elimina chat',
            style: 'destructive',
            onPress: () => handleChatAction('delete', chat)
          },
          {
            text: isArchived ? 'Rimuovi dall\'archivio' : 'Archivia chat',
            onPress: () => handleChatAction(isArchived ? 'unarchive' : 'archive', chat)
          },
          {
            text: 'Aggiungi ai preferiti',
            onPress: () => handleChatAction('favorite', chat)
          },
          {
            text: 'Annulla',
            style: 'cancel'
          }
        ]
      );
    }
  };

  const handleActionSheetPress = (buttonIndex, chat) => {
    switch (buttonIndex) {
      case 0: // Archivia
        handleChatAction('archive', chat);
        break;
      case 1: // Preferiti
        handleChatAction('favorite', chat);
        break;
      case 2: // Silenzia
        handleChatAction('mute', chat);
        break;
      case 3: // Elimina
        handleChatAction('delete', chat);
        break;
      case 4: // Annulla
        break;
    }
  };

  const handleChatAction = async (action, chat) => {
    console.log(`🔄 Azione ${action} su chat:`, chat.id);

    switch (action) {
      case 'archive':
        // Archivia chat: rimuovi dalla lista principale e aggiungi all'archivio
        handleArchiveChat(chat.id);
        break;
      case 'unarchive':
        // Rimuovi dall'archivio: riporta nella lista principale
        handleUnarchiveChat(chat.id);
        break;
      case 'favorite':
        Alert.alert('Successo', 'Chat aggiunta ai preferiti');
        break;
      case 'mute':
        Alert.alert('Successo', 'Notifiche silenziate');
        break;
      case 'delete':
        try {
          await deleteChat(chat.id);
          Alert.alert('Successo', 'Chat eliminata');
        } catch (error) {
          console.error('❌ Errore eliminazione:', error);
          Alert.alert('Errore', 'Impossibile eliminare la chat');
        }
        break;
    }
  };

  // Funzione handleNewChat rimossa perché le opzioni sono state spostate nel menu

  const handleSearch = (text) => {
    setSearchQuery(text);
  };

  const clearSearch = () => {
    setSearchQuery('');
    setShowUserResults(false);
    setUserSearchResults([]);
  };

  // Gestisce il click su un utente trovato
  const handleUserPress = async (user) => {
    console.log('👤 HomeScreen: Creando chat con utente:', user.displayName);

    // Verifica se esiste già una chat con questo utente
    const existingChat = chats.find(chat => {
      const otherUser = chat.participants.find(p => p.id !== 'currentUser');
      return otherUser?.id === user._id;
    });

    if (existingChat) {
      // Se esiste già una chat, naviga a quella
      console.log('✅ Chat esistente trovata, navigando...');
      handleChatPress(existingChat);
    } else {
      try {
        // Crea una nuova chat sul server usando ChatStore
        console.log('🔄 Creando nuova chat sul server...');
        const currentUserId = user?.id || 'user_unknown';  // Utente corrente
        const participantId = user.id || user._id;         // Utente trovato

        const chatId = await createChat(
          currentUserId,
          participantId,
          user.displayName,
          user.photoURL
        );

        console.log('✅ Chat creata sul server con ID:', chatId);

        // Ricarica le chat per mostrare la nuova chat
        await loadChats(user?.id);

        // Naviga alla nuova chat con i dati dell'utente
        navigation.navigate('ChatRoom', {
          chatId: chatId,
          name: user.displayName || 'Utente',
          avatar: user.photoURL,
          isOnline: user.online || false,
          userId: user.id || user._id,
          phoneNumber: user.phoneNumber
        });
      } catch (error) {
        console.error('❌ Errore nella creazione della chat:', error);
        Alert.alert('Errore', 'Impossibile creare la chat');
      }
    }

    // Pulisci la ricerca
    clearSearch();
  };

  const handleFilterChange = (filter) => {
    setActiveFilter(filter);
  };

  const toggleArchived = () => {
    console.log('🔄 Toggle archiviate - stato attuale:', showArchived);
    console.log('📁 Chat archiviate disponibili:', archivedChats.length);
    console.log('📋 Lista chat archiviate:', archivedChats.map(c => ({ id: c.id, name: c.name })));

    // 🔍 DEBUG: Controlla se ci sono chat duplicate nell'archivio
    const duplicateIds = archivedChats.map(c => c.id).filter((id, index, arr) => arr.indexOf(id) !== index);
    if (duplicateIds.length > 0) {
      console.log('⚠️ DUPLICATI TROVATI NELL\'ARCHIVIO:', duplicateIds);
    }

    // 🔧 PULIZIA DUPLICATI TEMPORANEA
    if (archivedChats.length > 0) {
      const uniqueChats = archivedChats.filter((chat, index, self) =>
        index === self.findIndex(c => c.id === chat.id)
      );
      if (uniqueChats.length !== archivedChats.length) {
        console.log('🧹 Rimuovendo duplicati dall\'archivio');
        setArchivedChats(uniqueChats);
      }
    }

    setShowArchived(!showArchived);
    console.log('✅ Nuovo stato showArchived:', !showArchived);
  };

  const handleArchiveChat = (chatId) => {
    // 🔧 ARCHIVIAZIONE WHATSAPP - NASCONDE DALLA HOME
    const chatToArchive = chats.find(chat => chat.id === chatId);
    if (chatToArchive) {
      console.log('📁 Archiviando chat:', chatId);

      // 1. Aggiungi all'archivio locale (evita duplicati)
      setArchivedChats(prev => {
        const exists = prev.find(c => c.id === chatId);
        if (exists) {
          console.log('⚠️ Chat già archiviata, non aggiungo duplicato');
          return prev;
        }
        return [...prev, { ...chatToArchive, isArchived: true }];
      });

      // 2. Rimuovi dalla lista principale (NASCONDE DALLA HOME)
      useChatStore.setState({
        chats: chats.filter(chat => chat.id !== chatId)
      });

      // 3. NON ricaricare dal server - mantieni archiviazione locale
      // loadChats(user?.id); // ❌ RIMOSSO: annullava l'archiviazione

      Alert.alert('Successo', `Chat archiviata`);
      console.log('✅ Chat nascosta dalla home e aggiunta all\'archivio');
    }
  };

  const handleUnarchiveChat = (chatId) => {
    // 🔧 RIPRISTINO WHATSAPP - RIPORTA NELLA HOME (SENZA DUPLICATI)
    const chatToUnarchive = archivedChats.find(chat => chat.id === chatId);
    if (chatToUnarchive) {
      console.log('📤 Ripristinando chat dall\'archivio:', chatId);

      // 1. Rimuovi dall'archivio
      setArchivedChats(prev => {
        const filtered = prev.filter(chat => chat.id !== chatId);
        console.log('🗑️ Rimossa chat dall\'archivio. Prima:', prev.length, 'Dopo:', filtered.length);
        return filtered;
      });

      // 2. Riporta nella lista principale (EVITA DUPLICATI)
      const chatStore = useChatStore.getState();
      const existingChat = chatStore.chats.find(c => c.id === chatId);

      if (!existingChat) {
        // Solo se non esiste già, aggiungila
        const restoredChat = { ...chatToUnarchive, isArchived: false };
        useChatStore.setState({
          chats: [...chatStore.chats, restoredChat]
        });
        console.log('✅ Chat aggiunta alla home (non era presente)');
      } else {
        console.log('⚠️ Chat già presente nella home, non aggiungo duplicato');
      }

      // 3. NON ricaricare dal server - mantieni ripristino locale
      // loadChats(user?.id); // ❌ RIMOSSO: annullava il ripristino

      Alert.alert('Successo', 'Chat ripristinata');
      console.log('✅ Chat ripristinata nella home');
    }
  };

  const [showMenu, setShowMenu] = useState(false);
  const aiIconAnim = useRef(new Animated.Value(1)).current;
  const aiRotateAnim = useRef(new Animated.Value(0)).current;

  // Animazione per l'icona dell'assistente
  const animateAiIcon = () => {
    // Animazione di pulsazione
    Animated.sequence([
      Animated.timing(aiIconAnim, {
        toValue: 1.2,
        duration: 300,
        useNativeDriver: true
      }),
      Animated.timing(aiIconAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true
      })
    ]).start();

    // Animazione di rotazione
    Animated.timing(aiRotateAnim, {
      toValue: 1,
      duration: 600,
      useNativeDriver: true
    }).start(() => {
      aiRotateAnim.setValue(0);
    });
  };

  const handleAiPress = () => {
    animateAiIcon();
    setIsChatBotVisible(true);
  };

  const handleSettings = () => {
    setShowMenu(true);
  };

  const handleCallHistory = () => {
    console.log('📞 PULSANTE CHIAMATE CLICCATO!');
    console.log('📞 Navigando alla cronologia chiamate...');
    navigation.navigate('CallHistory');
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent={true} />

      <LinearGradient
        colors={['#1E88E5', '#D81B60']}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 0 }}
        style={styles.header}
      >
        <View style={styles.headerTop}>
          <View style={styles.titleContainer}>
            <Text style={styles.title}>TrendyChat</Text>
            <TouchableOpacity
              style={styles.aiButton}
              onPress={handleAiPress}
              activeOpacity={0.7}
            >
              <Animated.View
                style={[styles.aiIconContainer, {
                  transform: [
                    { scale: aiIconAnim },
                    { rotate: aiRotateAnim.interpolate({
                      inputRange: [0, 1],
                      outputRange: ['0deg', '360deg']
                    })}
                  ]
                }]}
              >
                <LinearGradient
                  colors={['rgba(255,255,255,0.3)', 'rgba(255,255,255,0.1)']}
                  style={styles.aiIconGradient}
                >
                  <Ionicons name="cloud" size={24} color="#FFFFFF" />
                </LinearGradient>
              </Animated.View>
            </TouchableOpacity>
          </View>
          <View style={styles.headerButtons}>
            <TouchableOpacity
              style={styles.headerButton}
              onPress={handleSettings}
            >
              <View style={styles.menuIconContainer}>
                <Ionicons name="menu" size={22} color="#FFFFFF" />
              </View>
            </TouchableOpacity>
          </View>
        </View>

        <View style={styles.searchContainer}>
          <View style={styles.searchBar}>
            <Ionicons name="search" size={20} color="#666666" style={styles.searchIcon} />
            <TextInput
              style={styles.searchInput}
              placeholder="Chiedi a TrendyChat o cerca..."
              placeholderTextColor="#666666"
              value={searchQuery}
              onChangeText={handleSearch}
              returnKeyType="search"
            />
            {searchQuery.length > 0 && (
              <TouchableOpacity onPress={clearSearch} style={styles.clearButton}>
                <Ionicons name="close-circle" size={20} color="#666666" />
              </TouchableOpacity>
            )}
          </View>
        </View>
      </LinearGradient>

      <View style={styles.filtersAndArchivedContainer}>
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          style={styles.filterContainer}
          contentContainerStyle={styles.filterContent}
        >
          <TouchableOpacity
            style={[styles.filterButton, activeFilter === 'all' && styles.activeFilterButton]}
            onPress={() => handleFilterChange('all')}
          >
            <Text style={[styles.filterText, activeFilter === 'all' && styles.activeFilterText]}>Tutte</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.filterButton, activeFilter === 'unread' && styles.activeFilterButton]}
            onPress={() => handleFilterChange('unread')}
          >
            <View style={styles.filterTextContainer}>
              <Text style={[styles.filterText, activeFilter === 'unread' && styles.activeFilterText]}>Da leggere</Text>
              {chats.filter(c => c.unreadCount > 0).length > 0 && (
                <View style={styles.filterBadge}>
                  <Text style={styles.filterBadgeText}>{chats.filter(c => c.unreadCount > 0).length}</Text>
                </View>
              )}
            </View>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.filterButton, activeFilter === 'favorites' && styles.activeFilterButton]}
            onPress={() => handleFilterChange('favorites')}
          >
            <Text style={[styles.filterText, activeFilter === 'favorites' && styles.activeFilterText]}>Preferiti</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.filterButton, activeFilter === 'groups' && styles.activeFilterButton]}
            onPress={() => handleFilterChange('groups')}
          >
            <Text style={[styles.filterText, activeFilter === 'groups' && styles.activeFilterText]}>Gruppi</Text>
          </TouchableOpacity>
        </ScrollView>
      </View>

      {!showArchived && (
        <TouchableOpacity style={styles.archivedButton} onPress={toggleArchived}>
          <LinearGradient
            colors={['#1E88E5', '#D81B60']}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 0 }}
            style={styles.archivedIconContainer}
          >
            <Ionicons name="archive-outline" size={22} color="#FFFFFF" style={styles.archivedIcon} />
          </LinearGradient>
          <View style={styles.archivedInfo}>
            <Text style={styles.archivedText}>Archiviate</Text>
            {archivedChats.length > 0 && (
              <Text style={styles.archivedCount}>{archivedChats.length}</Text>
            )}
          </View>
          <Ionicons name="chevron-forward" size={18} color="#666666" />
        </TouchableOpacity>
      )}

      {showArchived && (
        <LinearGradient
          colors={['#1E88E5', '#D81B60']}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 0 }}
          style={styles.archivedHeader}
        >
          <TouchableOpacity style={styles.archivedHeaderButton} onPress={toggleArchived}>
            <Ionicons name="arrow-back" size={24} color="#FFFFFF" style={styles.archivedBackIcon} />
            <Text style={styles.archivedHeaderText}>Archiviate</Text>
          </TouchableOpacity>
        </LinearGradient>
      )}

      {/* Risultati della ricerca utenti */}
      {showUserResults && searchQuery.trim() && (
        <View style={styles.userSearchResultsContainer}>
          {isSearchingUsers ? (
            <View style={styles.userSearchLoadingContainer}>
              <ActivityIndicator size="small" color="#1E88E5" />
              <Text style={styles.userSearchLoadingText}>Ricerca utenti...</Text>
            </View>
          ) : userSearchResults.length > 0 ? (
            <>
              <Text style={styles.userSearchResultsTitle}>Utenti trovati</Text>
              {userSearchResults.map(user => (
                <TouchableOpacity
                  key={user.id}
                  style={styles.userSearchResultItem}
                  onPress={() => handleUserPress(user)}
                >
                  {user.photoURL ? (
                    <Image source={{ uri: user.photoURL }} style={styles.userSearchAvatar} />
                  ) : (
                    <LinearGradient
                      colors={['#1E88E5', '#D81B60']}
                      style={[styles.userSearchAvatar, styles.defaultAvatar]}
                    >
                      <Text style={styles.avatarText}>
                        {(user.displayName || 'U').charAt(0).toUpperCase()}
                      </Text>
                    </LinearGradient>
                  )}
                  <View style={styles.userSearchInfo}>
                    <Text style={styles.userSearchName}>{user.displayName}</Text>
                    <Text style={styles.userSearchPhone}>{user.phoneNumber}</Text>
                  </View>
                  <TouchableOpacity
                    style={styles.userSearchChatButton}
                    onPress={() => handleUserPress(user)}
                  >
                    <Ionicons name="chatbubble-outline" size={20} color="#1E88E5" />
                  </TouchableOpacity>
                </TouchableOpacity>
              ))}
            </>
          ) : contactSearchService.isPhoneNumber(searchQuery) ? (
            <View style={styles.userSearchEmptyContainer}>
              <Ionicons name="person-outline" size={40} color="#AAAAAA" />
              <Text style={styles.userSearchEmptyText}>
                Nessun utente trovato con il numero {searchQuery}
              </Text>
              <Text style={styles.userSearchEmptySubtext}>
                Invita i tuoi amici a unirsi a TrendyChat!
              </Text>
            </View>
          ) : null}
        </View>
      )}

      {loading && !refreshing ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#1E88E5" />
        </View>
      ) : error ? (
        <View style={styles.errorContainer}>
          <Ionicons name="alert-circle" size={24} color="#D81B60" />
          <Text style={styles.errorText}>{error}</Text>
          <TouchableOpacity
            style={styles.retryButton}
            onPress={() => loadChats(user.uid)}
          >
            <LinearGradient
              colors={['#1E88E5', '#D81B60']}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 0 }}
              style={styles.retryButtonGradient}
            >
              <Text style={styles.retryText}>Riprova</Text>
            </LinearGradient>
          </TouchableOpacity>
        </View>
      ) : filteredChats.length === 0 ? (
        <View style={styles.emptyContainer}>
          <Ionicons name="chatbubble-ellipses-outline" size={64} color="#1E88E5" />
          <Text style={styles.emptyText}>Nessuna chat</Text>
          <Text style={styles.emptySubtext}>
            Tocca il pulsante + per iniziare una nuova conversazione
          </Text>
        </View>
      ) : (
        <FlatList
          data={filteredChats}
          renderItem={({ item }) => (
            <ChatItem
              item={item}
              onPress={handleChatPress}
              onLongPress={handleChatLongPress}
            />
          )}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.listContent}
          refreshing={refreshing}
          onRefresh={handleRefresh}
          ItemSeparatorComponent={() => <View style={styles.separator} />}
          ListHeaderComponent={<View style={{ height: 1 }} />}
          ListEmptyComponent={searchQuery ? (
            <View style={styles.emptySearchContainer}>
              <Ionicons name="search-outline" size={64} color="#AAAAAA" />
              <Text style={styles.emptySearchText}>Nessun risultato per "{searchQuery}"</Text>
            </View>
          ) : null}
        />
      )}

      {/* Pulsante FAB rimosso */}

      {/* Barra di navigazione rimossa perché già presente nel TabNavigator */}

      <Modal
        visible={isChatBotVisible}
        animationType="slide"
        onRequestClose={() => setIsChatBotVisible(false)}
        transparent={false}
      >
        <ChatBot onClose={() => setIsChatBotVisible(false)} />
      </Modal>

      <MenuModal
        visible={showMenu}
        onClose={() => setShowMenu(false)}
        navigation={navigation}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#121212', // Sfondo scuro come nella schermata di registrazione
  },
  header: {
    flexDirection: 'column',
    paddingHorizontal: width < 360 ? 12 : 16,
    paddingBottom: 12,
    paddingTop: Platform.OS === 'ios' ? 50 : 40,
    elevation: 0,
    shadowColor: 'transparent',
  },
  headerTop: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  title: {
    fontSize: width < 360 ? 20 : 24,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginRight: 12,
  },
  aiButton: {
    padding: 4,
    marginLeft: 4,
  },
  aiIconContainer: {
    borderRadius: 18,
    overflow: 'hidden',
    width: 36,
    height: 36,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 3,
    elevation: 3,
  },
  aiIconGradient: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 18,
  },
  headerButtons: {
    flexDirection: 'row',
  },
  headerButton: {
    marginLeft: 16,
    padding: 8,
  },
  menuIconContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 8,
    padding: 4,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    color: '#D81B60',
    marginTop: 8,
    marginBottom: 16,
    textAlign: 'center',
    fontSize: 16,
  },
  retryButton: {
    borderRadius: 8,
    overflow: 'hidden',
  },
  retryButtonGradient: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  retryText: {
    color: '#FFFFFF',
    fontWeight: '600',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#FFFFFF',
    marginTop: 16,
  },
  emptySubtext: {
    fontSize: 14,
    color: '#AAAAAA',
    textAlign: 'center',
    marginTop: 8,
  },
  listContent: {
    flexGrow: 1,
    paddingBottom: 60,
  },
  chatItem: {
    flexDirection: 'row',
    padding: Platform.OS === 'ios' ? 16 : 14,
    backgroundColor: '#1E1E1E', // Superficie leggermente più chiara dello sfondo
    alignItems: 'center',
  },
  unreadChat: {
    backgroundColor: '#2A2A2A', // Superficie ancora più chiara per chat non lette
  },
  avatarContainer: {
    position: 'relative',
    marginRight: 12,
  },
  avatar: {
    width: Platform.OS === 'ios' ? 50 : 45,
    height: Platform.OS === 'ios' ? 50 : 45,
    borderRadius: Platform.OS === 'ios' ? 25 : 22.5,
  },
  avatarText: {
    color: '#FFFFFF',
    fontSize: 20,
    fontWeight: 'bold',
  },
  onlineIndicator: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    width: 14,
    height: 14,
    borderRadius: 7,
    backgroundColor: '#1E88E5', // Blu per online
    borderWidth: 2,
    borderColor: '#1E1E1E',
  },
  chatInfo: {
    flex: 1,
  },
  chatHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  chatName: {
    fontSize: Platform.OS === 'ios' ? 16 : 15,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  unreadText: {
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  timeText: {
    fontSize: 12,
    color: '#AAAAAA',
  },
  messagePreview: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  lastMessage: {
    flex: 1,
    fontSize: Platform.OS === 'ios' ? 14 : 13,
    color: '#AAAAAA',
    marginRight: 8,
  },
  unreadBadge: {
    backgroundColor: '#D81B60', // Rosa per badge non letti
    borderRadius: 12,
    minWidth: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 6,
  },
  unreadCount: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '600',
  },
  separator: {
    height: 1,
    backgroundColor: '#2A2A2A', // Colore del separatore
  },
  defaultAvatar: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  fab: {
    position: 'absolute',
    bottom: Platform.OS === 'ios' ? 90 : 70,
    right: Platform.OS === 'ios' ? 24 : 20,
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    zIndex: 999,
  },

  searchContainer: {
    paddingVertical: 0,
    borderBottomWidth: 0,
  },
  searchBar: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F0F0F0',
    borderRadius: 25,
    paddingHorizontal: 12,
    height: 36,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    height: 36,
    color: '#333333',
    fontSize: 16,
  },
  clearButton: {
    padding: 4,
  },
  filtersAndArchivedContainer: {
    backgroundColor: '#1E1E1E',
  },
  filterContainer: {
    backgroundColor: '#1E1E1E',
    paddingVertical: 8,
    borderBottomWidth: 0,
  },
  filterContent: {
    paddingHorizontal: 16,
  },
  filterButton: {
    paddingHorizontal: 16,
    paddingVertical: 6,
    borderRadius: 20,
    marginRight: 8,
    backgroundColor: '#2A2A2A',
    marginBottom: 4,
  },
  activeFilterButton: {
    backgroundColor: 'rgba(30, 136, 229, 0.2)', // Blu con opacità
  },
  filterText: {
    color: '#AAAAAA',
    fontSize: 14,
  },
  activeFilterText: {
    color: '#1E88E5', // Blu
    fontWeight: '600',
  },
  filterTextContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  filterBadge: {
    backgroundColor: '#1E88E5',
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 6,
    paddingHorizontal: 4,
  },
  filterBadgeText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: 'bold',
  },
  archivedButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#1E1E1E',
    borderBottomWidth: 1,
    borderTopWidth: 1,
    borderTopColor: '#2A2A2A',
    borderBottomColor: '#2A2A2A',
    marginTop: 0,
  },
  archivedIconContainer: {
    width: 45,
    height: 45,
    borderRadius: 22.5,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  archivedIcon: {
  },
  archivedInfo: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  archivedText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  archivedCount: {
    color: '#1E88E5',
    fontSize: 14,
    marginRight: 8,
    fontWeight: 'bold',
  },
  archivedHeader: {
    paddingTop: Platform.OS === 'ios' ? 40 : 30,
    paddingBottom: 16,
    borderBottomWidth: 0,
  },
  archivedHeaderButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
  },
  archivedBackIcon: {
    marginRight: 16,
  },
  archivedHeaderText: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: '600',
  },
  emptySearchContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
    marginTop: 40,
  },
  emptySearchText: {
    fontSize: 16,
    color: '#AAAAAA',
    textAlign: 'center',
    marginTop: 16,
  },
  // Stili per i risultati della ricerca utenti
  userSearchResultsContainer: {
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    marginHorizontal: 16,
    marginTop: 8,
    marginBottom: 8,
    padding: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 1.41,
  },
  userSearchLoadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
  },
  userSearchLoadingText: {
    marginLeft: 8,
    fontSize: 14,
    color: '#666666',
  },
  userSearchResultsTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333333',
    marginBottom: 12,
  },
  userSearchResultItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  userSearchAvatar: {
    width: 48,
    height: 48,
    borderRadius: 24,
  },
  defaultAvatar: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatarText: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: 'bold',
  },
  userSearchInfo: {
    flex: 1,
    marginLeft: 12,
  },
  userSearchName: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333333',
  },
  userSearchPhone: {
    fontSize: 14,
    color: '#666666',
    marginTop: 2,
  },
  userSearchChatButton: {
    padding: 8,
  },
  userSearchEmptyContainer: {
    alignItems: 'center',
    padding: 16,
  },
  userSearchEmptyText: {
    fontSize: 14,
    color: '#666666',
    textAlign: 'center',
    marginTop: 8,
  },
  userSearchEmptySubtext: {
    fontSize: 12,
    color: '#999999',
    textAlign: 'center',
    marginTop: 4,
  },
  /* Stili della barra di navigazione rimossi perché già presenti nel TabNavigator */
});

export default HomeScreen;