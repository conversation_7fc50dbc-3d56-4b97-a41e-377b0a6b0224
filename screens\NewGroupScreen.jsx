import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  TextInput,
  Image,
  ActivityIndicator,
  Alert,
  SafeAreaView,
  StatusBar,
  Platform,
  Dimensions,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import * as ImagePicker from 'expo-image-picker';
import useContactsStore from '../store/contactsStore';
import useGroupStore from '../store/groupStore';
import useAuthStore from '../store/authStore';
import contactSearchService from '../services/contactSearchService';

const { width, height } = Dimensions.get('window');

const NewGroupScreen = () => {
  const navigation = useNavigation();
  const { user } = useAuthStore();
  const { contacts, loading: contactsLoading, loadContacts } = useContactsStore();
  const { createGroup, loading: groupLoading } = useGroupStore();
  const [selectedContacts, setSelectedContacts] = useState([]);
  const [groupName, setGroupName] = useState('');
  const [groupImage, setGroupImage] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [isCreating, setIsCreating] = useState(false);

  // 🔍 STATI RICERCA UTENTI (IDENTICI ALLA HOMESCREEN)
  const [userSearchResults, setUserSearchResults] = useState([]);
  const [isSearchingUsers, setIsSearchingUsers] = useState(false);
  const [showUserResults, setShowUserResults] = useState(false);

  // Carica i contatti all'avvio
  useEffect(() => {
    loadContacts();
  }, []);

  // 🔍 RICERCA UTENTI PER NUMERO DI TELEFONO (IDENTICA ALLA HOMESCREEN)
  useEffect(() => {
    const searchUsers = async () => {
      // Verifica se la query è un numero di telefono
      if (searchQuery.trim() && contactSearchService.isPhoneNumber(searchQuery)) {
        setIsSearchingUsers(true);
        setShowUserResults(true);

        try {
          const results = await contactSearchService.searchUsers(searchQuery);
          setUserSearchResults(results);
        } catch (error) {
          console.error('Errore nella ricerca degli utenti:', error);
          setUserSearchResults([]);
        } finally {
          setIsSearchingUsers(false);
        }
      } else {
        setShowUserResults(false);
        setUserSearchResults([]);
      }
    };

    // Esegui la ricerca con un piccolo ritardo per evitare troppe chiamate API
    const timeoutId = setTimeout(searchUsers, 500);

    return () => clearTimeout(timeoutId);
  }, [searchQuery]);

  // 🔍 DETERMINA QUALE LISTA MOSTRARE
  const getContactsToShow = () => {
    // Se stiamo mostrando risultati di ricerca utenti, usa quelli
    if (showUserResults && searchQuery.trim() && contactSearchService.isPhoneNumber(searchQuery)) {
      return userSearchResults.filter(user => {
        const userId = user.id || user._id;
        // Non mostrare utenti già selezionati
        return !selectedContacts.some(contact => contact.id === userId);
      });
    }

    // Altrimenti usa i contatti normali filtrati
    return contacts.filter(contact => {
      const contactName = contact.displayName || '';
      return (
        contactName &&
        (!searchQuery.trim() ||
         contactSearchService.isPhoneNumber(searchQuery) ||
         contactName.toLowerCase().includes(searchQuery.toLowerCase()))
      );
    });
  };

  // Ottieni i contatti da mostrare
  const contactsToShow = getContactsToShow();

  const handleSelectContact = (contact) => {
    if (selectedContacts.some(c => c.id === contact.id)) {
      setSelectedContacts(selectedContacts.filter(c => c.id !== contact.id));
    } else {
      setSelectedContacts([...selectedContacts, contact]);
    }
  };

  const handlePickImage = async () => {
    try {
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permesso negato', 'Permesso di accesso alla galleria negato');
        return;
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: 'images',
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.8,
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        setGroupImage(result.assets[0].uri);
      }
    } catch (error) {
      console.error("Errore nella selezione dell'immagine:", error);
      Alert.alert('Errore', 'Impossibile selezionare l\'immagine');
    }
  };

  const handleCreateGroup = async () => {
    if (!groupName.trim()) {
      Alert.alert('Errore', 'Inserisci un nome per il gruppo');
      return;
    }

    if (selectedContacts.length === 0) {
      Alert.alert('Errore', 'Seleziona almeno un contatto');
      return;
    }

    // ✅ VALIDAZIONE WHATSAPP: MINIMO 3 MEMBRI (tu + 2 contatti)
    if (selectedContacts.length < 2) {
      Alert.alert(
        'Gruppo troppo piccolo',
        'I gruppi devono avere almeno 3 partecipanti (tu + 2 contatti). Aggiungi altri contatti per creare il gruppo.',
        [{ text: 'OK' }]
      );
      return;
    }

    try {
      setIsCreating(true);
      const memberIds = selectedContacts.map(contact => contact.id);
      const groupId = await createGroup(groupName, memberIds, groupImage);

      Alert.alert(
        'Gruppo creato',
        `Il gruppo "${groupName}" è stato creato con successo!`,
        [
          {
            text: 'OK',
            onPress: () => navigation.navigate('GroupChat', { groupId })
          }
        ]
      );
    } catch (error) {
      console.error("Errore nella creazione del gruppo:", error);
      Alert.alert('Errore', 'Impossibile creare il gruppo');
    } finally {
      setIsCreating(false);
    }
  };

  const renderSelectedContact = ({ item }) => (
    <View style={styles.selectedContactItem}>
      <Image
        source={{ uri: item.photoURL || 'https://via.placeholder.com/50' }}
        style={styles.selectedContactImage}
      />
      <Text style={styles.selectedContactName} numberOfLines={1}>
        {item.displayName}
      </Text>
      <TouchableOpacity
        style={styles.removeButton}
        onPress={() => handleSelectContact(item)}
      >
        <Ionicons name="close-circle" size={20} color="#FFFFFF" />
      </TouchableOpacity>
    </View>
  );

  const renderContact = ({ item }) => {
    const contactId = item.id || item._id;
    const isSelected = selectedContacts.some(c => (c.id || c._id) === contactId);

    // Gestisci sia contatti locali che utenti trovati
    const contactName = item.displayName || item.name || 'Utente';
    const contactAvatar = item.photoURL || item.avatar || 'https://via.placeholder.com/50';
    const contactStatus = item.online ? 'Online' : 'Ultimo accesso recente';

    return (
      <TouchableOpacity
        style={[styles.contactItem, isSelected && styles.selectedItem]}
        onPress={() => handleSelectContact({
          id: contactId,
          _id: contactId,
          displayName: contactName,
          name: contactName,
          photoURL: contactAvatar,
          avatar: contactAvatar,
          online: item.online
        })}
      >
        <View style={styles.checkboxContainer}>
          {isSelected ? (
            <View style={styles.checkbox}>
              <Ionicons name="checkmark" size={16} color="#FFFFFF" />
            </View>
          ) : (
            <View style={styles.emptyCheckbox} />
          )}
        </View>

        <Image
          source={{ uri: contactAvatar }}
          style={styles.contactImage}
        />

        <View style={styles.contactInfo}>
          <Text style={styles.contactName}>{contactName}</Text>
          <Text style={styles.contactStatus}>
            {contactStatus}
            {showUserResults && ' • TrendyChat'}
          </Text>
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent />

      <LinearGradient
        colors={['#1E88E5', '#D81B60']}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 0 }}
        style={styles.header}
      >
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color="#FFFFFF" />
        </TouchableOpacity>

        <Text style={styles.headerTitle}>Nuovo gruppo</Text>

        {selectedContacts.length >= 2 && groupName.trim() ? (
          <TouchableOpacity
            style={styles.createButton}
            onPress={handleCreateGroup}
            disabled={isCreating}
          >
            {isCreating ? (
              <ActivityIndicator size="small" color="#FFFFFF" />
            ) : (
              <Ionicons name="checkmark" size={24} color="#FFFFFF" />
            )}
          </TouchableOpacity>
        ) : (
          <View style={styles.placeholder} />
        )}
      </LinearGradient>

      <View style={styles.groupInfoContainer}>
        <TouchableOpacity
          style={styles.groupImageContainer}
          onPress={handlePickImage}
        >
          <LinearGradient
            colors={['#1E88E5', '#D81B60']}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 0 }}
            style={styles.imageGradientBorder}
          >
            {groupImage ? (
              <Image source={{ uri: groupImage }} style={styles.groupImage} />
            ) : (
              <View style={styles.groupImagePlaceholder}>
                <Ionicons name="camera" size={24} color="#FFFFFF" />
              </View>
            )}
          </LinearGradient>
          <View style={styles.cameraIconContainer}>
            <LinearGradient
              colors={['#1E88E5', '#D81B60']}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 0 }}
              style={styles.cameraIconGradient}
            >
              <Ionicons name="camera" size={14} color="#FFFFFF" />
            </LinearGradient>
          </View>
        </TouchableOpacity>

        <View style={styles.inputContainer}>
          <Text style={styles.inputLabel}>Nome del gruppo</Text>
          <TextInput
            style={styles.groupNameInput}
            placeholder="Inserisci un nome"
            placeholderTextColor="#AAAAAA"
            value={groupName}
            onChangeText={setGroupName}
            maxLength={25}
          />
        </View>
      </View>

      {selectedContacts.length > 0 && (
        <View style={styles.selectedContactsContainer}>
          <View style={styles.sectionTitleContainer}>
            <Ionicons name="people" size={18} color="#1E88E5" />
            <Text style={styles.sectionTitle}>
              Partecipanti: {selectedContacts.length}
            </Text>
          </View>
          <FlatList
            data={selectedContacts}
            renderItem={renderSelectedContact}
            keyExtractor={item => item.id}
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.selectedContactsList}
          />
        </View>
      )}

      <View style={styles.searchContainer}>
        <View style={styles.searchInputContainer}>
          <Ionicons name="search" size={18} color="#AAAAAA" style={styles.searchIcon} />
          <TextInput
            style={styles.searchInput}
            placeholder="Cerca contatti o inserisci numero"
            placeholderTextColor="#AAAAAA"
            value={searchQuery}
            onChangeText={setSearchQuery}
          />
          {searchQuery.length > 0 && (
            <TouchableOpacity onPress={() => setSearchQuery('')} style={styles.clearButton}>
              <LinearGradient
                colors={['#1E88E5', '#D81B60']}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 0 }}
                style={styles.clearButtonGradient}
              >
                <Ionicons name="close" size={14} color="#FFFFFF" />
              </LinearGradient>
            </TouchableOpacity>
          )}
        </View>
      </View>

      {/* 🔍 LOADING RICERCA UTENTI */}
      {isSearchingUsers && (
        <View style={styles.userSearchLoadingContainer}>
          <ActivityIndicator size="small" color="#1E88E5" />
          <Text style={styles.userSearchLoadingText}>Ricerca utenti...</Text>
        </View>
      )}

      {contactsLoading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#1E88E5" />
        </View>
      ) : (
        <FlatList
          data={contactsToShow}
          renderItem={renderContact}
          keyExtractor={item => item.id || item._id}
          contentContainerStyle={styles.contactsList}
          ListEmptyComponent={
            <View style={styles.emptyContainer}>
              <Ionicons name="people" size={64} color="#AAAAAA" />
              <Text style={styles.emptyText}>
                {showUserResults && contactSearchService.isPhoneNumber(searchQuery)
                  ? `Nessun utente trovato con il numero ${searchQuery}`
                  : 'Nessun contatto trovato'
                }
              </Text>
              {showUserResults && contactSearchService.isPhoneNumber(searchQuery) && (
                <Text style={styles.emptySubtext}>
                  Invita i tuoi amici a unirsi a TrendyChat!
                </Text>
              )}
            </View>
          }
        />
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#121212',
    paddingTop: StatusBar.currentHeight,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingTop: Platform.OS === 'ios' ? 10 : 12,
    paddingBottom: 12,
    height: Platform.OS === 'ios' ? 70 : 60,
  },
  backButton: {
    padding: 8,
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  createButton: {
    padding: 8,
  },
  placeholder: {
    width: 40,
  },
  groupInfoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#1E1E1E',
    borderBottomWidth: 1,
    borderBottomColor: '#2A2A2A',
  },
  groupImageContainer: {
    marginRight: 16,
    position: 'relative',
  },
  imageGradientBorder: {
    width: 70,
    height: 70,
    borderRadius: 35,
    padding: 2,
    justifyContent: 'center',
    alignItems: 'center',
  },
  groupImage: {
    width: 66,
    height: 66,
    borderRadius: 33,
    backgroundColor: '#1E1E1E',
  },
  groupImagePlaceholder: {
    width: 66,
    height: 66,
    borderRadius: 33,
    backgroundColor: '#2A2A2A',
    justifyContent: 'center',
    alignItems: 'center',
  },
  cameraIconContainer: {
    position: 'absolute',
    bottom: 0,
    right: 0,
  },
  cameraIconGradient: {
    width: 26,
    height: 26,
    borderRadius: 13,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#1E1E1E',
  },
  inputContainer: {
    flex: 1,
  },
  inputLabel: {
    fontSize: 12,
    color: '#1E88E5',
    marginBottom: 4,
  },
  groupNameInput: {
    height: 40,
    color: '#FFFFFF',
    fontSize: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#2A2A2A',
    paddingVertical: 8,
  },
  selectedContactsContainer: {
    padding: 16,
    backgroundColor: '#1E1E1E',
    borderBottomWidth: 1,
    borderBottomColor: '#2A2A2A',
  },
  sectionTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  sectionTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#1E88E5',
    marginLeft: 8,
  },
  selectedContactsList: {
    paddingVertical: 8,
  },
  selectedContactItem: {
    alignItems: 'center',
    marginRight: 16,
    width: 60,
  },
  selectedContactImage: {
    width: 50,
    height: 50,
    borderRadius: 25,
  },
  selectedContactName: {
    fontSize: 12,
    color: '#FFFFFF',
    marginTop: 4,
    textAlign: 'center',
    width: 60,
  },
  removeButton: {
    position: 'absolute',
    top: -5,
    right: -5,
    backgroundColor: '#D81B60',
    borderRadius: 10,
  },
  searchContainer: {
    padding: 12,
    backgroundColor: '#1E1E1E',
    borderBottomWidth: 1,
    borderBottomColor: '#2A2A2A',
  },
  searchInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#2A2A2A',
    borderRadius: 20,
    paddingHorizontal: 12,
    paddingVertical: 8,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    color: '#FFFFFF',
    fontSize: 16,
    padding: 0,
  },
  clearButton: {
    marginLeft: 8,
  },
  clearButtonGradient: {
    width: 22,
    height: 22,
    borderRadius: 11,
    justifyContent: 'center',
    alignItems: 'center',
  },
  contactsList: {
    paddingVertical: 8,
  },
  contactItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    backgroundColor: '#1E1E1E',
  },
  selectedItem: {
    backgroundColor: '#2A2A2A',
  },
  checkboxContainer: {
    width: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  checkbox: {
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: '#1E88E5',
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyCheckbox: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 1,
    borderColor: '#AAAAAA',
  },
  contactImage: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: 12,
  },
  contactInfo: {
    flex: 1,
  },
  contactName: {
    fontSize: 16,
    color: '#FFFFFF',
  },
  contactStatus: {
    fontSize: 14,
    color: '#AAAAAA',
    marginTop: 2,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    marginTop: 50,
  },
  emptyText: {
    fontSize: 16,
    color: '#FFFFFF',
    marginTop: 16,
    textAlign: 'center',
  },
  emptySubtext: {
    fontSize: 14,
    color: '#AAAAAA',
    textAlign: 'center',
    marginTop: 8,
  },
  // 🔍 STILI RICERCA UTENTI
  userSearchLoadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    backgroundColor: '#1E1E1E',
  },
  userSearchLoadingText: {
    fontSize: 14,
    color: '#1E88E5',
    marginLeft: 8,
  },
});

export default NewGroupScreen;
