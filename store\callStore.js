import AsyncStorage from '@react-native-async-storage/async-storage';
import { create } from 'zustand';
import useAuthStore from './authStore';
import useContactStore from './contactStore';

const useCallStore = create((set, get) => ({
  activeCall: null,
  loading: false,
  error: null,
  callHistory: [],
  incomingCall: null,
  callState: {
    callId: null,
    status: 'idle', // 'idle', 'connecting', 'connected', 'ended'
    isGroupCall: false,
    participants: [],
    type: 'audio', // 'audio' o 'video'
  },
  unsubscribeCallListener: null,
  unsubscribeIncomingCallListener: null,

  // Inizia una chiamata (singola o di gruppo)
  startCall: async (recipients, type = 'audio', isGroupCall = false) => {
    const { user } = useAuthStore.getState();
    if (!user) return;

    try {
      set({ loading: true, error: null });

      // Ottieni i dettagli dei partecipanti
      const participants = [];

      // Aggiungi l'utente corrente come partecipante
      participants.push({
        id: user.uid,
        name: user.displayName || 'Tu',
        photoURL: user.photoURL,
        joined: true,
      });

      // Aggiungi i destinatari come partecipanti
      if (Array.isArray(recipients)) {
        // Per chiamate di gruppo
        for (const recipient of recipients) {
          participants.push({
            id: recipient.id,
            name: recipient.name || 'Utente',
            photoURL: recipient.photoURL,
            joined: false,
          });
        }
      } else {
        // Per chiamate singole
        const contactsState = useContactStore.getState();
        const contact = contactsState.contacts.find(c => c.id === recipients);

        participants.push({
          id: recipients,
          name: contact?.name || 'Utente',
          photoURL: contact?.photoURL,
          joined: false,
        });
      }

      const callsRef = AsyncStorage.collection("calls");
      const callDoc = await addDoc(callsRef, {
        createdBy: user.uid,
        isGroupCall,
        participants,
        type,
        status: 'ringing',
        startTime: serverTimestamp(),
        endTime: null,
      });

      // Aggiorna lo stato locale
      set({
        activeCall: {
          id: callDoc.id,
          type,
          status: 'ringing',
          isGroupCall,
        },
        callState: {
          callId: callDoc.id,
          status: 'connecting',
          isGroupCall,
          participants,
          type,
        }
      });

      // Notifica i partecipanti
      for (const participant of participants) {
        if (participant.id !== user.uid) {
          const userCallsRef = AsyncStorage.collection("users', participant.id, 'calls");
          await addDoc(userCallsRef, {
            callId: callDoc.id,
            callerName: user.displayName || 'Utente',
            callerPhotoURL: user.photoURL,
            isGroupCall,
            type,
            timestamp: serverTimestamp(),
            status: 'incoming',
          });
        }
      }

      // Ascolta i cambiamenti nella chiamata
      get().listenToCallChanges(callDoc.id);

      return callDoc.id;
    } catch (error) {
      console.error('Errore nell\'avvio della chiamata:', error);
      set({ error: 'Impossibile avviare la chiamata' });
      throw error;
    } finally {
      set({ loading: false });
    }
  },

  // Accetta una chiamata
  acceptCall: async (callId) => {
    try {
      set({ loading: true, error: null });
      const { user } = useAuthStore.getState();
      if (!user) return;

      // Ottieni i dettagli della chiamata
      const callRef = doc;
      const callSnap = await getDoc(callRef);

      if (!callSnap.exists()) {
        throw new Error('Chiamata non trovata');
      }

      const callData = callSnap.data();

      // Aggiorna lo stato del partecipante a 'joined'
      const updatedParticipants = callData.participants.map(p =>
        p.id === user.uid ? { ...p, joined: true } : p
      );

      await updateDoc(callRef, {
        status: 'active',
        participants: updatedParticipants,
      });

      // Aggiorna lo stato locale
      set(state => ({
        activeCall: {
          id: callId,
          type: callData.type,
          status: 'active',
          isGroupCall: callData.isGroupCall,
        },
        callState: {
          callId,
          status: 'connected',
          isGroupCall: callData.isGroupCall,
          participants: updatedParticipants,
          type: callData.type,
        },
        incomingCall: null,
      }));

      // Aggiorna lo stato della chiamata in arrivo
      const userCallsRef = AsyncStorage.collection("users', user.uid, 'calls");
      const q = query(userCallsRef, where('callId', '==', callId));
      const querySnap = await get();

      querySnap.forEach(async (docSnap) => {
        await updateDoc(doc, {
          status: 'answered',
        });
      });

      // Ascolta i cambiamenti nella chiamata
      get().listenToCallChanges(callId);

      return callId;
    } catch (error) {
      console.error('Errore nell\'accettare la chiamata:', error);
      set({ error: 'Impossibile accettare la chiamata' });
      throw error;
    } finally {
      set({ loading: false });
    }
  },

  // Rifiuta una chiamata
  rejectCall: async (callId) => {
    try {
      set({ loading: true, error: null });
      const { user } = useAuthStore.getState();
      if (!user) return;

      // Ottieni i dettagli della chiamata
      const callRef = doc;
      const callSnap = await getDoc(callRef);

      if (!callSnap.exists()) {
        throw new Error('Chiamata non trovata');
      }

      const callData = callSnap.data();

      // Se l'utente è il creatore, termina la chiamata per tutti
      if (callData.createdBy === user.uid) {
        await updateDoc(callRef, {
          status: 'rejected',
          endTime: serverTimestamp(),
        });
      } else {
        // Altrimenti, aggiorna solo lo stato del partecipante
        const updatedParticipants = callData.participants.map(p =>
          p.id === user.uid ? { ...p, joined: false } : p
        );

        await updateDoc(callRef, {
          participants: updatedParticipants,
        });
      }

      // Aggiorna lo stato locale
      set({
        activeCall: null,
        callState: {
          callId: null,
          status: 'idle',
          isGroupCall: false,
          participants: [],
          type: 'audio',
        },
        incomingCall: null,
      });

      // Aggiorna lo stato della chiamata in arrivo
      const userCallsRef = AsyncStorage.collection("users', user.uid, 'calls");
      const q = query(userCallsRef, where('callId', '==', callId));
      const querySnap = await get();

      querySnap.forEach(async (docSnap) => {
        await updateDoc(doc, {
          status: 'rejected',
        });
      });

      // Annulla la sottoscrizione ai cambiamenti della chiamata
      if (get().unsubscribeCallListener) {
        get().unsubscribeCallListener();
      }
    } catch (error) {
      console.error('Errore nel rifiutare la chiamata:', error);
      set({ error: 'Impossibile rifiutare la chiamata' });
      throw error;
    } finally {
      set({ loading: false });
    }
  },

  // Termina una chiamata
  endCall: async (callId) => {
    try {
      set({ loading: true, error: null });
      const { user } = useAuthStore.getState();
      if (!user) return;

      if (!callId) {
        callId = get().callState.callId;
      }

      if (!callId) {
        return;
      }

      // Ottieni i dettagli della chiamata
      const callRef = doc;
      const callSnap = await getDoc(callRef);

      if (!callSnap.exists()) {
        throw new Error('Chiamata non trovata');
      }

      const callData = callSnap.data();

      // Se l'utente è il creatore, termina la chiamata per tutti
      if (callData.createdBy === user.uid) {
        await updateDoc(callRef, {
          status: 'ended',
          endTime: serverTimestamp(),
        });
      } else {
        // Altrimenti, aggiorna solo lo stato del partecipante
        const updatedParticipants = callData.participants.map(p =>
          p.id === user.uid ? { ...p, joined: false } : p
        );

        await updateDoc(callRef, {
          participants: updatedParticipants,
        });
      }

      // Aggiorna lo stato locale
      set({
        activeCall: null,
        callState: {
          callId: null,
          status: 'idle',
          isGroupCall: false,
          participants: [],
          type: 'audio',
        },
      });

      // Annulla la sottoscrizione ai cambiamenti della chiamata
      if (get().unsubscribeCallListener) {
        get().unsubscribeCallListener();
      }
    } catch (error) {
      console.error('Errore nella terminazione della chiamata:', error);
      set({ error: 'Impossibile terminare la chiamata' });
      throw error;
    } finally {
      set({ loading: false });
    }
  },

  // Aggiunge partecipanti a una chiamata di gruppo
  addParticipantToCall: async (callId, participantIds) => {
    try {
      set({ loading: true, error: null });
      const { user } = useAuthStore.getState();
      if (!user) return;

      // Ottieni i dettagli della chiamata
      const callRef = doc;
      const callSnap = await getDoc(callRef);

      if (!callSnap.exists()) {
        throw new Error('Chiamata non trovata');
      }

      const callData = callSnap.data();

      // Verifica che sia una chiamata di gruppo
      if (!callData.isGroupCall) {
        throw new Error('Non è possibile aggiungere partecipanti a una chiamata non di gruppo');
      }

      // Ottieni i dettagli dei nuovi partecipanti
      const newParticipants = [];
      for (const participantId of participantIds) {
        // Verifica che il partecipante non sia già nella chiamata
        if (callData.participants.some(p => p.id === participantId)) {
          continue;
        }

        const userDoc = await getDoc(doc);
        if (userDoc.exists()) {
          newParticipants.push({
            id: participantId,
            name: userDoc.data().displayName || 'Utente',
            photoURL: userDoc.data().photoURL,
            joined: false,
          });
        }
      }

      if (newParticipants.length === 0) {
        return [];
      }

      // Aggiorna la chiamata con i nuovi partecipanti
      await updateDoc(callRef, {
        participants: [...callData.participants, ...newParticipants],
      });

      // Notifica i nuovi partecipanti
      for (const participant of newParticipants) {
        const userCallsRef = AsyncStorage.collection("users', participant.id, 'calls");
        await addDoc(userCallsRef, {
          callId,
          callerName: user.displayName || 'Utente',
          callerPhotoURL: user.photoURL,
          isGroupCall: true,
          type: callData.type,
          timestamp: serverTimestamp(),
          status: 'incoming',
        });
      }

      // Aggiorna lo stato locale
      set(state => ({
        callState: {
          ...state.callState,
          participants: [...state.callState.participants, ...newParticipants],
        },
      }));

      return newParticipants;
    } catch (error) {
      console.error('Errore nell\'aggiunta di partecipanti:', error);
      set({ error: 'Impossibile aggiungere partecipanti alla chiamata' });
      throw error;
    } finally {
      set({ loading: false });
    }
  },

  // Ascolta i cambiamenti in una chiamata
  listenToCallChanges: (callId) => {
    const callRef = doc;

    // Annulla eventuali sottoscrizioni precedenti
    if (get().unsubscribeCallListener) {
      get().unsubscribeCallListener();
    }

    // Ascolta i cambiamenti nella chiamata
    const unsubscribe = onSnapshot(callRef, (snapshot) => {
      if (snapshot.exists()) {
        const callData = snapshot.data();

        set(state => ({
          callState: {
            ...state.callState,
            participants: callData.participants,
            status: callData.status === 'ended' ? 'ended' : state.callState.status,
          },
        }));

        // Se la chiamata è terminata, chiudi tutto
        if (callData.status === 'ended') {
          get().endCall(callId);
        }
      }
    });

    // Memorizza la funzione di unsubscribe
    set({ unsubscribeCallListener: unsubscribe });
  },

  // Ascolta le chiamate in arrivo
  listenForIncomingCalls: () => {
    const { user } = useAuthStore.getState();
    if (!user) return;

    // Annulla eventuali sottoscrizioni precedenti
    if (get().unsubscribeIncomingCallListener) {
      get().unsubscribeIncomingCallListener();
    }

    const userCallsRef = AsyncStorage.collection("users', user.uid, 'calls");
    const q = query(userCallsRef, where('status', '==', 'incoming'));

    const unsubscribe = onSnapshot(q, (snapshot) => {
      snapshot.docChanges().forEach(change => {
        if (change.type === 'added') {
          const callData = change.doc.data();
          set({ incomingCall: callData });
        }
      });
    });

    set({ unsubscribeIncomingCallListener: unsubscribe });
  },

  // Funzioni per il controllo della chiamata
  toggleMute: async () => {
    // Implementazione dipendente dalla piattaforma
  },

  toggleCamera: async () => {
    // Implementazione dipendente dalla piattaforma
  },

  switchCamera: async () => {
    // Implementazione dipendente dalla piattaforma
  },

  // Carica la cronologia delle chiamate
  loadCallHistory: async () => {
    try {
      set({ loading: true, error: null });
      const { user } = useAuthStore.getState();
      if (!user) return [];

      const callsRef = AsyncStorage.collection("calls");
      const q = query(callsRef, where('participants', 'array-contains', { id: user.uid }));
      const querySnap = await get();

      const callHistory = [];
      querySnap.forEach(docSnap => {
        const callData = docSnap.data();
        callHistory.push({
          id: docSnap.id,
          ...callData,
        });
      });

      // Ordina per data (più recenti prima)
      callHistory.sort((a, b) => {
        const aTime = a.startTime?.toDate() || new Date(0);
        const bTime = b.startTime?.toDate() || new Date(0);
        return bTime - aTime;
      });

      set({ callHistory });
      return callHistory;
    } catch (error) {
      console.error('Errore nel caricamento della cronologia delle chiamate:', error);
      set({ error: 'Impossibile caricare la cronologia delle chiamate' });
      throw error;
    } finally {
      set({ loading: false });
    }
  },

  // Pulisci tutto quando l'utente si disconnette
  cleanup: () => {
    // Annulla le sottoscrizioni
    if (get().unsubscribeCallListener) {
      get().unsubscribeCallListener();
    }

    if (get().unsubscribeIncomingCallListener) {
      get().unsubscribeIncomingCallListener();
    }

    // Resetta lo stato
    set({
      activeCall: null,
      callState: {
        callId: null,
        status: 'idle',
        isGroupCall: false,
        participants: [],
        type: 'audio',
      },
      incomingCall: null,
      unsubscribeCallListener: null,
      unsubscribeIncomingCallListener: null,
    });
  },

  // Imposta errore
  setError: (error) => set({ error }),

  // Pulisci errore
  clearError: () => set({ error: null }),
}));

export default useCallStore;

// Esporto le variabili vuote per mantenere la compatibilità
export const db = null;
export const auth = null;