import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  FlatList,
  Alert,
  ActivityIndicator,
  ScrollView,
  Image
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import useBroadcastStore from '../store/useBroadcastStore';
import useContactsStore from '../store/contactsStore';

const CreateBroadcastScreen = ({ navigation }) => {
  const { createBroadcast, loading } = useBroadcastStore();
  const { contacts, loadContacts } = useContactsStore();

  const [broadcastName, setBroadcastName] = useState('');
  const [broadcastDescription, setBroadcastDescription] = useState('');
  const [selectedContacts, setSelectedContacts] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');

  useEffect(() => {
    loadContacts();
  }, []);

  // Contatti mock per test (incluso il tuo)
  const mockContacts = [
    {
      id: 'user_1747680860161', // Il tuo ID
      name: '<PERSON>reddu',
      displayName: '<PERSON>reddu',
      phone: '3209332143',
      phoneNumber: '3209332143',
      avatar: 'http://************:3001/api/files/1748099259567-366643352.jpeg',
      photoURL: 'http://************:3001/api/files/1748099259567-366643352.jpeg'
    },
    {
      id: 'user_test_001',
      name: 'Mario Rossi',
      displayName: 'Mario Rossi',
      phone: '3331234567',
      phoneNumber: '3331234567',
      avatar: '',
      photoURL: ''
    },
    {
      id: 'user_test_002',
      name: 'Giulia Bianchi',
      displayName: 'Giulia Bianchi',
      phone: '3337654321',
      phoneNumber: '3337654321',
      avatar: '',
      photoURL: ''
    },
    {
      id: 'user_test_003',
      name: 'Luca Verdi',
      displayName: 'Luca Verdi',
      phone: '3339876543',
      phoneNumber: '3339876543',
      avatar: '',
      photoURL: ''
    }
  ];

  const filteredContacts = contacts.filter(contact =>
    contact.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
    contact.phone?.includes(searchQuery)
  );

  const toggleContactSelection = (contact) => {
    setSelectedContacts(prev => {
      const isSelected = prev.find(c => c.userId === contact.id);
      if (isSelected) {
        return prev.filter(c => c.userId !== contact.id);
      } else {
        return [...prev, {
          userId: contact.id,
          name: contact.name || contact.displayName,
          phone: contact.phone || contact.phoneNumber,
          avatar: contact.avatar || contact.photoURL || ''
        }];
      }
    });
  };

  const handleCreateBroadcast = async () => {
    if (!broadcastName.trim()) {
      Alert.alert('Errore', 'Inserisci un nome per la lista broadcast');
      return;
    }

    if (selectedContacts.length === 0) {
      Alert.alert('Errore', 'Seleziona almeno un contatto');
      return;
    }

    try {
      await createBroadcast(broadcastName, broadcastDescription, selectedContacts);
      Alert.alert('Successo', 'Lista broadcast creata con successo', [
        { text: 'OK', onPress: () => navigation.goBack() }
      ]);
    } catch (error) {
      Alert.alert('Errore', 'Impossibile creare la lista broadcast');
    }
  };

  const renderContactItem = ({ item }) => {
    const isSelected = selectedContacts.find(c => c.userId === item.id);

    return (
      <TouchableOpacity
        style={[styles.contactItem, isSelected && styles.selectedContactItem]}
        onPress={() => toggleContactSelection(item)}
      >
        <View style={styles.contactAvatar}>
          {item.avatar || item.photoURL ? (
            <Image source={{ uri: item.avatar || item.photoURL }} style={styles.avatarImage} />
          ) : (
            <LinearGradient
              colors={['#1E88E5', '#D81B60']}
              style={styles.avatarGradient}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
            >
              <Text style={styles.avatarText}>
                {(item.name || item.displayName || 'U').charAt(0).toUpperCase()}
              </Text>
            </LinearGradient>
          )}
        </View>

        <View style={styles.contactInfo}>
          <Text style={styles.contactName}>
            {item.name || item.displayName || 'Utente'}
          </Text>
          <Text style={styles.contactPhone}>
            {item.phone || item.phoneNumber || 'Numero non disponibile'}
          </Text>
        </View>

        <View style={[styles.checkbox, isSelected && styles.checkedBox]}>
          {isSelected && <Ionicons name="checkmark" size={16} color="white" />}
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <View style={styles.container}>
      {/* Header TrendyChat */}
      <LinearGradient
        colors={['#1E88E5', '#D81B60']}
        style={styles.header}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color="white" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Nuova Lista Broadcast</Text>
        <TouchableOpacity
          style={styles.createButton}
          onPress={handleCreateBroadcast}
          disabled={loading}
        >
          {loading ? (
            <ActivityIndicator size="small" color="white" />
          ) : (
            <Ionicons name="checkmark" size={24} color="white" />
          )}
        </TouchableOpacity>
      </LinearGradient>

      <FlatList
        data={filteredContacts}
        renderItem={renderContactItem}
        keyExtractor={(item) => item.id}
        style={styles.content}
        showsVerticalScrollIndicator={false}
        ListHeaderComponent={
          <View>
            {/* Form Broadcast */}
            <View style={styles.formSection}>
              <Text style={styles.sectionTitle}>Informazioni Lista</Text>

              <View style={styles.inputContainer}>
                <Text style={styles.inputLabel}>Nome Lista *</Text>
                <TextInput
                  style={styles.textInput}
                  value={broadcastName}
                  onChangeText={setBroadcastName}
                  placeholder="Es. Famiglia, Lavoro, Amici..."
                  placeholderTextColor="#666"
                  maxLength={100}
                />
              </View>

              <View style={styles.inputContainer}>
                <Text style={styles.inputLabel}>Descrizione (opzionale)</Text>
                <TextInput
                  style={[styles.textInput, styles.textArea]}
                  value={broadcastDescription}
                  onChangeText={setBroadcastDescription}
                  placeholder="Descrizione della lista broadcast..."
                  placeholderTextColor="#666"
                  multiline
                  numberOfLines={3}
                  maxLength={500}
                />
              </View>
            </View>

            {/* Contatti Selezionati */}
            {selectedContacts.length > 0 && (
              <View style={styles.selectedSection}>
                <Text style={styles.sectionTitle}>
                  Contatti Selezionati ({selectedContacts.length})
                </Text>
                <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                  {selectedContacts.map((contact, index) => (
                    <View key={index} style={styles.selectedContact}>
                      <LinearGradient
                        colors={['#1E88E5', '#D81B60']}
                        style={styles.selectedAvatar}
                        start={{ x: 0, y: 0 }}
                        end={{ x: 1, y: 1 }}
                      >
                        <Text style={styles.selectedAvatarText}>
                          {contact.name.charAt(0).toUpperCase()}
                        </Text>
                      </LinearGradient>
                      <Text style={styles.selectedContactName} numberOfLines={1}>
                        {contact.name}
                      </Text>
                      <TouchableOpacity
                        style={styles.removeButton}
                        onPress={() => {
                          setSelectedContacts(prev =>
                            prev.filter(c => c.userId !== contact.userId)
                          );
                        }}
                      >
                        <Ionicons name="close-circle" size={20} color="#FF6B6B" />
                      </TouchableOpacity>
                    </View>
                  ))}
                </ScrollView>
              </View>
            )}

            {/* Ricerca Contatti */}
            <View style={styles.contactsSection}>
              <Text style={styles.sectionTitle}>Seleziona Contatti</Text>

              <View style={styles.searchContainer}>
                <Ionicons name="search" size={20} color="#666" />
                <TextInput
                  style={styles.searchInput}
                  value={searchQuery}
                  onChangeText={setSearchQuery}
                  placeholder="Cerca contatti..."
                  placeholderTextColor="#666"
                />
              </View>
            </View>
          </View>
        }
        ListEmptyComponent={
          <View style={styles.emptyContacts}>
            <Ionicons name="people-outline" size={48} color="#666" />
            <Text style={styles.emptyContactsText}>
              {searchQuery ? 'Nessun contatto trovato' : 'Nessun contatto disponibile'}
            </Text>
          </View>
        }
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#1E1E1E'
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
    paddingTop: 50,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 5
  },
  backButton: {
    padding: 8
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: 'white',
    flex: 1,
    textAlign: 'center'
  },
  createButton: {
    padding: 8
  },
  content: {
    flex: 1,
    backgroundColor: '#1E1E1E'
  },
  formSection: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#333'
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: 16
  },
  inputContainer: {
    marginBottom: 16
  },
  inputLabel: {
    fontSize: 14,
    color: '#CCCCCC',
    marginBottom: 8
  },
  textInput: {
    backgroundColor: '#2A2A2A',
    borderRadius: 8,
    padding: 12,
    color: 'white',
    fontSize: 16,
    borderWidth: 1,
    borderColor: '#444'
  },
  textArea: {
    height: 80,
    textAlignVertical: 'top'
  },
  selectedSection: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#333'
  },
  selectedContact: {
    alignItems: 'center',
    marginRight: 12,
    width: 60
  },
  selectedAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 4
  },
  selectedAvatarText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold'
  },
  selectedContactName: {
    color: 'white',
    fontSize: 12,
    textAlign: 'center'
  },
  removeButton: {
    position: 'absolute',
    top: -5,
    right: -5
  },
  contactsSection: {
    padding: 16,
    paddingBottom: 0
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#2A2A2A',
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: '#444'
  },
  searchInput: {
    flex: 1,
    color: 'white',
    fontSize: 16,
    marginLeft: 8
  },

  contactItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    backgroundColor: '#2A2A2A',
    borderRadius: 8,
    marginBottom: 8
  },
  selectedContactItem: {
    backgroundColor: '#1E88E5'
  },
  contactAvatar: {
    marginRight: 12
  },
  avatarImage: {
    width: 40,
    height: 40,
    borderRadius: 20
  },
  avatarGradient: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center'
  },
  avatarText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold'
  },
  contactInfo: {
    flex: 1
  },
  contactName: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 2
  },
  contactPhone: {
    color: '#CCCCCC',
    fontSize: 14
  },
  checkbox: {
    width: 24,
    height: 24,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: '#666',
    alignItems: 'center',
    justifyContent: 'center'
  },
  checkedBox: {
    backgroundColor: '#1E88E5',
    borderColor: '#1E88E5'
  },
  emptyContacts: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 40
  },
  emptyContactsText: {
    color: '#666',
    fontSize: 16,
    marginTop: 12,
    textAlign: 'center'
  }
});

export default CreateBroadcastScreen;
