import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  Platform,
  Dimensions,
  Alert,
  BackHandler,
  Image,
} from 'react-native';
import { useRoute, useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { Camera } from 'expo-camera';
import { Audio } from 'expo-av';
// Importa RTCView in modo sicuro
let RTCView;
let isRTCViewAvailable = false;

try {
  // Prova a importare RTCView da react-native-webrtc
  const WebRTC = require('react-native-webrtc');
  RTCView = WebRTC.RTCView;
  isRTCViewAvailable = true;
  console.log('RTCView disponibile');
} catch (error) {
  console.log('RTCView non disponibile, utilizzo mock:', error.message);
  // Fallback al mock
  RTCView = ({ style }) => {
    return (
      <View style={style}>
        <Text style={styles.streamPlaceholder}>Stream Video</Text>
        <Text style={styles.streamPlaceholderSubtext}>WebRTC disponibile solo nelle build EAS</Text>
      </View>
    );
  };
}
import { LinearGradient } from 'expo-linear-gradient';
import LottieView from 'lottie-react-native';

import useWebRTCStore from '../store/webrtcStore';
import useAuthStore from '../store/authStore';
import { theme } from '../theme';
import { saveCallToHistory } from '../utils/callHistoryUtils';

const { width, height } = Dimensions.get('window');

const CallScreenWebRTC = () => {
  const route = useRoute();
  const navigation = useNavigation();
  const { user } = useAuthStore();

  const {
    roomId,
    remoteUserId,
    remoteName,
    isOutgoing,
    isVideoCall,
    offer,
    // 🚀 NUOVI PARAMETRI ARCHITETTURA IBRIDA
    chatId,
    name,
    avatar,
    targetUserId,
    isGroupCall
  } = route.params || {};

  // 🔧 COMPATIBILITÀ: Usa i nuovi parametri se disponibili
  const finalRemoteName = name || remoteName || 'Utente';
  const finalRemoteUserId = targetUserId || remoteUserId;
  const finalIsVideoCall = isVideoCall !== undefined ? isVideoCall : true;

  const {
    isCallActive,
    localStream,
    remoteStream,
    callStatus,
    isMuted,
    isCameraOff,
    isSpeakerOn,
    answerCall,
    endCall,
    toggleMute,
    toggleCamera,
    switchCamera,
    toggleSpeaker,
    error
  } = useWebRTCStore();

  // 🚀 Stati locali per architettura ibrida
  const [callDuration, setCallDuration] = useState(0);
  const [isLocalStreamFullScreen, setIsLocalStreamFullScreen] = useState(false);
  const [hybridLocalStream, setHybridLocalStream] = useState(null); // Stream architettura ibrida
  const timerRef = useRef(null);
  const callStartTimeRef = useRef(null);





  // Gestisce il tap sulla finestra PiP per scambiare le visualizzazioni
  const handlePipTap = () => {
    setIsLocalStreamFullScreen(!isLocalStreamFullScreen);
  };

  // Inizializza lo stream locale quando la schermata viene caricata
  // 🚀 INIZIALIZZA STREAM LOCALE PER ARCHITETTURA IBRIDA
  useEffect(() => {
    const initLocalStream = async () => {
      try {
        console.log('🎥 IBRIDA: Inizializzando stream locale per CallScreenWebRTC');
        console.log('🎥 IBRIDA: isVideoCall:', finalIsVideoCall);
        console.log('🎥 IBRIDA: chatId:', chatId);

        // Richiedi i permessi per la fotocamera
        const cameraPermission = await Camera.requestCameraPermissionsAsync();
        const audioPermission = await Audio.requestPermissionsAsync();

        if (cameraPermission.granted && audioPermission.granted) {
          console.log('✅ IBRIDA: Permessi concessi, inizializzazione stream locale');

          // 🚀 IMPORTA E USA WEBRTC HANDLER PER STREAM LOCALE
          const webrtcHandler = require('../webrtc/webrtcHandler').default;
          const stream = await webrtcHandler.startLocalStream(finalIsVideoCall);

          console.log('✅ IBRIDA: Stream locale acquisito:', stream);
          console.log('✅ IBRIDA: Tracce video:', stream?.getVideoTracks?.()?.length || 0);
          console.log('✅ IBRIDA: Tracce audio:', stream?.getAudioTracks?.()?.length || 0);

          // 💾 SALVA STREAM NELLO STATO LOCALE
          setHybridLocalStream(stream);
          console.log('💾 IBRIDA: Stream salvato nello stato locale');

          // 🚀 PRODUCTION: AVVIA TIMER QUANDO STREAM È PRONTO
          if (!timerRef.current && !callStartTimeRef.current) {
            console.log('⏰ PRODUCTION: Avviando timer chiamata');
            callStartTimeRef.current = Date.now();

            timerRef.current = setInterval(() => {
              const elapsed = Math.floor((Date.now() - callStartTimeRef.current) / 1000);
              setCallDuration(elapsed);
            }, 1000);

            console.log('✅ PRODUCTION: Timer chiamata attivo');
          }

          // Forza lo stato a 'connected' per mostrare gli stream
          if (callStatus === 'connecting') {
            console.log('🔄 IBRIDA: Forzando lo stato a connected');
            setTimeout(() => {
              if (callStatus === 'connecting') {
                console.log('✅ IBRIDA: Stato forzato a connected');
              }
            }, 1000);
          }
        } else {
          console.error('❌ IBRIDA: Permessi camera/audio negati');
        }
      } catch (error) {
        console.error('❌ IBRIDA: Errore inizializzazione stream locale:', error);
      }
    };

    initLocalStream();
  }, [callStatus, finalIsVideoCall, chatId]);

  // 🧹 PRODUCTION: CLEANUP QUANDO COMPONENTE SI UNMONTA
  useEffect(() => {
    return () => {
      console.log('🧹 PRODUCTION: Cleanup componente CallScreenWebRTC');
      if (timerRef.current) {
        clearInterval(timerRef.current);
        timerRef.current = null;
        console.log('⏰ PRODUCTION: Timer pulito durante unmount');
      }
      callStartTimeRef.current = null;
    };
  }, []);

  // Richiedi i permessi per la fotocamera e il microfono
  useEffect(() => {
    const requestPermissions = async () => {
      try {
        // Usa la nuova API di permessi di Expo
        let cameraPermission;
        let audioPermission;

        // Gestisci sia la vecchia che la nuova API
        if (Camera.requestCameraPermissionsAsync) {
          cameraPermission = await Camera.requestCameraPermissionsAsync();
        } else if (Camera.requestPermissionsAsync) {
          cameraPermission = await Camera.requestPermissionsAsync();
        } else {
          console.log('API di permessi della fotocamera non trovata, assumo permessi concessi');
          cameraPermission = { granted: true };
        }

        if (Audio.requestPermissionsAsync) {
          audioPermission = await Audio.requestPermissionsAsync();
        } else {
          console.log('API di permessi audio non trovata, assumo permessi concessi');
          audioPermission = { granted: true };
        }

        if (!cameraPermission.granted || !audioPermission.granted) {
          Alert.alert(
            'Permessi necessari',
            'È necessario concedere i permessi per la fotocamera e il microfono per effettuare chiamate.',
            [{ text: 'OK', onPress: () => navigation.goBack() }]
          );
        }
      } catch (error) {
        console.error('Errore nella richiesta dei permessi:', error);
        Alert.alert('Errore', 'Impossibile richiedere i permessi necessari');
        navigation.goBack();
      }
    };

    requestPermissions();
  }, []);

  // Gestisci il pulsante indietro
  useEffect(() => {
    const backHandler = BackHandler.addEventListener('hardwareBackPress', () => {
      // Impedisci il comportamento predefinito del pulsante indietro
      return true;
    });

    return () => backHandler.remove();
  }, []);

  // Inizializza la chiamata
  useEffect(() => {
    const setupCall = async () => {
      try {
        if (!isOutgoing && offer) {
          // Chiamata in arrivo
          await answerCall(roomId, offer);
        }
      } catch (error) {
        console.error('Errore nella configurazione della chiamata:', error);
        Alert.alert('Errore', 'Impossibile configurare la chiamata');
        handleEndCall();
      }
    };

    setupCall();

    // Pulisci quando il componente viene smontato
    return () => {
      stopCallTimer();
      endCall();
    };
  }, []);

  // Gestisci lo stato della chiamata
  useEffect(() => {
    if (callStatus === 'connected' && !timerRef.current) {
      startCallTimer();

      // Resetta la posizione della finestra PiP quando la chiamata si connette
      pipPosition.setValue({ x: 0, y: 0 });
    } else if (callStatus === 'ended') {
      stopCallTimer();
      navigation.goBack();
    }
  }, [callStatus]);

  // Gestisci gli errori
  useEffect(() => {
    if (error) {
      Alert.alert('Errore', error);
    }
  }, [error]);

  // Avvia il timer della chiamata
  const startCallTimer = () => {
    callStartTimeRef.current = Date.now();
    timerRef.current = setInterval(() => {
      const elapsedSeconds = Math.floor((Date.now() - callStartTimeRef.current) / 1000);
      setCallDuration(elapsedSeconds);
    }, 1000);
  };

  // Ferma il timer della chiamata
  const stopCallTimer = () => {
    if (timerRef.current) {
      clearInterval(timerRef.current);
      timerRef.current = null;
    }
  };

  // Formatta la durata della chiamata
  const formatCallDuration = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  // Termina la chiamata
  const handleEndCall = () => {
    console.log('🔚 PRODUCTION: Terminando chiamata e pulendo timer');

    // 💾 CRONOLOGIA: Salva chiamata prima di terminare (SOLO se durata > 0)
    if (callDuration > 0 && callStartTimeRef.current) {
      console.log('💾 CRONOLOGIA: Salvando chiamata con durata:', callDuration);

      const endTime = new Date();
      const startTime = new Date(callStartTimeRef.current);

      try {
        // 🔍 Gestione chiamata con se stessi
        const isSelfCall = finalRemoteUserId === user?.id || !finalRemoteUserId;
        const contactName = isSelfCall ? (user?.displayName || user?.name || 'Tu') : finalRemoteName;
        const contactAvatar = isSelfCall ? (user?.photoURL || user?.avatar) : avatar;

        console.log('🔍 CRONOLOGIA: Dati contatto:', {
          isSelfCall,
          contactName,
          contactAvatar,
          finalRemoteName,
          userDisplayName: user?.displayName,
          userPhotoURL: user?.photoURL
        });

        saveCallToHistory({
          id: roomId || `call_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          type: finalIsVideoCall ? 'video' : 'audio',
          direction: isOutgoing ? 'outgoing' : 'incoming',
          status: 'completed',
          duration: callDuration,
          startTime: startTime.toISOString(),
          endTime: endTime.toISOString(),
          participants: [user?.id, finalRemoteUserId].filter(Boolean),
          contactName: contactName,
          contactAvatar: contactAvatar,
          server: 'render' // Sistema ibrido usa Render
        }).then(() => {
          console.log('✅ CRONOLOGIA: Chiamata salvata con successo!');
        }).catch((error) => {
          console.error('❌ CRONOLOGIA: Errore salvataggio:', error);
        });

      } catch (error) {
        console.error('❌ CRONOLOGIA: Errore salvataggio:', error);
      }
    } else {
      console.log('⚠️ CRONOLOGIA: Chiamata non salvata (durata:', callDuration, ', startTime:', !!callStartTimeRef.current, ')');
    }

    // 🧹 CLEANUP TIMER PRODUCTION
    if (timerRef.current) {
      clearInterval(timerRef.current);
      timerRef.current = null;
      console.log('⏰ PRODUCTION: Timer fermato');
    }

    // 🧹 RESET STATO
    callStartTimeRef.current = null;
    setCallDuration(0);

    endCall();
    navigation.goBack();
  };

  // 👥 GESTIONE AGGIUNTA PARTECIPANTI
  const handleAddParticipants = () => {
    console.log('👥 IBRIDA: Aggiungendo partecipanti alla chiamata');
    console.log('👥 IBRIDA: ChatId:', chatId);
    console.log('👥 IBRIDA: IsGroupCall:', isGroupCall);
    console.log('👥 IBRIDA: RoomId:', roomId);

    // Naviga alla schermata di aggiunta partecipanti
    navigation.navigate('AddCallParticipants', {
      callId: roomId || chatId,
      chatId: chatId,
      isVideoCall: finalIsVideoCall,
      currentParticipants: [user?.id, finalRemoteUserId].filter(Boolean)
    });
  };

  // Renderizza lo stato della chiamata
  const renderCallState = () => {
    switch (callStatus) {
      case 'connecting':
        return (
          <View style={styles.callStateContainer}>
            {isOutgoing ? (
              <LottieView
                source={require('../assets/lottie/calling.json')}
                autoPlay
                loop
                style={styles.callingAnimation}
              />
            ) : (
              <LottieView
                source={require('../assets/lottie/connecting.json')}
                autoPlay
                loop
                style={styles.callingAnimation}
              />
            )}
            <Text style={styles.callStateText}>
              {isOutgoing ? 'Chiamata in corso...' : 'Connessione in corso...'}
            </Text>
            <Text style={styles.remoteNameText}>{remoteName}</Text>
          </View>
        );
      case 'incoming':
        return (
          <View style={styles.callStateContainer}>
            <LottieView
              source={require('../assets/lottie/incoming-call.json')}
              autoPlay
              loop
              style={styles.callingAnimation}
            />
            <Text style={styles.callStateText}>Chiamata in arrivo</Text>
            <Text style={styles.remoteNameText}>{remoteName}</Text>

            <View style={styles.incomingCallActions}>
              <TouchableOpacity
                style={[styles.actionButton, styles.declineButton]}
                onPress={handleEndCall}
              >
                <Ionicons name="call" size={24} color="#FFFFFF" style={{ transform: [{ rotate: '135deg' }] }} />
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.actionButton, styles.acceptButton]}
                onPress={() => {
                  if (offer) {
                    answerCall(roomId, offer);
                  }
                }}
              >
                <Ionicons name="call" size={24} color="#FFFFFF" />
              </TouchableOpacity>
            </View>
          </View>
        );
      default:
        return null;
    }
  };

  // Log per debug
  console.log('CallScreenWebRTC - Rendering', {
    callStatus,
    isVideoCall,
    localStream: !!localStream,
    remoteStream: !!remoteStream,
    isLocalStreamFullScreen
  });

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#000000" />

      {/* Sfondo della chiamata */}
      <LinearGradient
        colors={['#1A237E', '#4A148C']}
        style={StyleSheet.absoluteFill}
      />

      {/* 📞 PRODUCTION: INTERFACCIA CHIAMATA AUDIO (con timer e profilo) */}
      {hybridLocalStream && !finalIsVideoCall && (
        <View style={styles.audioCallContainer}>
          {/* Immagine profilo utente */}
          <View style={styles.audioCallProfileContainer}>
            {avatar ? (
              <Image source={{ uri: avatar }} style={styles.audioCallAvatar} />
            ) : (
              <View style={styles.audioCallAvatarPlaceholder}>
                <Ionicons name="person" size={80} color="#FFFFFF" />
              </View>
            )}
          </View>

          {/* Nome utente */}
          <Text style={styles.audioCallName}>{finalRemoteName}</Text>

          {/* Timer chiamata */}
          <Text style={styles.audioCallTimer}>{formatCallDuration(callDuration)}</Text>

          {/* Stato chiamata */}
          <Text style={styles.audioCallStatus}>Chiamata in corso</Text>
        </View>
      )}

      {/* Stato della chiamata (quando non connesso) */}
      {!hybridLocalStream && !finalIsVideoCall && renderCallState()}

      {/* Streams per la videochiamata */}
      {(callStatus === 'connected' || isVideoCall) && (
        <>
          {/* Stream principale (a schermo intero) */}
          <View style={styles.fullScreenStream}>
            {isLocalStreamFullScreen ? (
              // 🚀 IBRIDA: Mostra stream locale ibrido a schermo intero
              (hybridLocalStream || localStream) ? (
                <RTCView
                  streamURL={(hybridLocalStream || localStream).toURL()}
                  style={StyleSheet.absoluteFill}
                  objectFit="cover"
                  zOrder={0}
                  mirror={true}
                />
              ) : (
                <View style={[StyleSheet.absoluteFill, { backgroundColor: '#333333', justifyContent: 'center', alignItems: 'center' }]}>
                  <Ionicons name="person" size={80} color="#FFFFFF" />
                </View>
              )
            ) : (
              // Altrimenti, mostra lo stream remoto a schermo intero
              remoteStream ? (
                <RTCView
                  streamURL={remoteStream.toURL()}
                  style={StyleSheet.absoluteFill}
                  objectFit="cover"
                  zOrder={0}
                />
              ) : (
                <View style={[StyleSheet.absoluteFill, { backgroundColor: '#333333', justifyContent: 'center', alignItems: 'center' }]}>
                  <Ionicons name="person" size={80} color="#FFFFFF" />
                </View>
              )
            )}

            {/* Overlay per mostrare il nome e la durata della chiamata */}
            <LinearGradient
              colors={['rgba(0,0,0,0.7)', 'transparent']}
              style={styles.callInfoGradient}
            >
              <Text style={styles.remoteNameText}>
                {isLocalStreamFullScreen ? 'Tu' : remoteName}
              </Text>
              <Text style={styles.callDurationText}>{formatCallDuration(callDuration)}</Text>
            </LinearGradient>
          </View>

          {/* Stream secondario (picture-in-picture) */}
          <View style={styles.localStreamContainer}>
            <TouchableOpacity
              style={styles.localStreamTouchable}
              onPress={handlePipTap}
              activeOpacity={0.8}
            >
              {isLocalStreamFullScreen ? (
                // Se isLocalStreamFullScreen è true, mostra lo stream remoto nella finestra PiP
                remoteStream ? (
                  <RTCView
                    streamURL={remoteStream.toURL()}
                    style={styles.localStream}
                    objectFit="cover"
                    zOrder={1}
                  />
                ) : (
                  <View style={[styles.localStream, { backgroundColor: '#333333', justifyContent: 'center', alignItems: 'center' }]}>
                    <Ionicons name="person" size={40} color="#FFFFFF" />
                  </View>
                )
              ) : (
                // 🚀 IBRIDA: Mostra stream locale ibrido nella finestra PiP
                (hybridLocalStream || localStream) ? (
                  <RTCView
                    streamURL={(hybridLocalStream || localStream).toURL()}
                    style={styles.localStream}
                    objectFit="cover"
                    zOrder={1}
                    mirror={true}
                  />
                ) : (
                  <View style={[styles.localStream, { backgroundColor: '#333333', justifyContent: 'center', alignItems: 'center' }]}>
                    <Ionicons name="person" size={40} color="#FFFFFF" />
                  </View>
                )
              )}

              {/* Indicatore di camera spenta */}
              {(isCameraOff && !isLocalStreamFullScreen) || (!(hybridLocalStream || localStream) && !isLocalStreamFullScreen) ? (
                <View style={styles.cameraOffIndicator}>
                  <Ionicons name="videocam-off" size={20} color="#FFFFFF" />
                </View>
              ) : null}

              {/* Etichetta per indicare chi è nella finestra PiP */}
              <View style={styles.localStreamLabel}>
                <Text style={styles.localStreamLabelText}>
                  {isLocalStreamFullScreen ? remoteName : 'Tu'}
                </Text>
              </View>
            </TouchableOpacity>
          </View>
        </>
      )}

      {/* Controlli della chiamata */}
      <View style={styles.controlsContainer}>
        <TouchableOpacity
          style={[styles.controlButton, isMuted && styles.activeControlButton]}
          onPress={toggleMute}
        >
          <Ionicons name={isMuted ? "mic-off" : "mic"} size={24} color="#FFFFFF" />
        </TouchableOpacity>

        {isVideoCall && (
          <TouchableOpacity
            style={[styles.controlButton, isCameraOff && styles.activeControlButton]}
            onPress={toggleCamera}
          >
            <Ionicons name={isCameraOff ? "videocam-off" : "videocam"} size={24} color="#FFFFFF" />
          </TouchableOpacity>
        )}

        {isVideoCall && (
          <TouchableOpacity
            style={styles.controlButton}
            onPress={switchCamera}
          >
            <Ionicons name="camera-reverse" size={24} color="#FFFFFF" />
          </TouchableOpacity>
        )}

        <TouchableOpacity
          style={[styles.controlButton, isSpeakerOn && styles.activeControlButton]}
          onPress={toggleSpeaker}
        >
          <Ionicons name={isSpeakerOn ? "volume-high" : "volume-mute"} size={24} color="#FFFFFF" />
        </TouchableOpacity>

        {/* 👥 BOTTONE AGGIUNGI PARTECIPANTI */}
        {(isGroupCall || chatId) && (
          <TouchableOpacity
            style={styles.controlButton}
            onPress={handleAddParticipants}
          >
            <Ionicons name="person-add" size={24} color="#FFFFFF" />
          </TouchableOpacity>
        )}

        <TouchableOpacity
          style={[styles.controlButton, styles.endCallButton]}
          onPress={handleEndCall}
        >
          <Ionicons name="call" size={24} color="#FFFFFF" style={{ transform: [{ rotate: '135deg' }] }} />
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000000',
  },
  fullScreenStream: {
    ...StyleSheet.absoluteFill,
  },
  callInfoGradient: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: 100,
    paddingTop: Platform.OS === 'ios' ? 50 : 30,
    paddingHorizontal: 20,
    alignItems: 'center',
  },
  localStreamContainer: {
    position: 'absolute',
    top: 70,
    right: 20,
    width: 120,
    height: 180,
    borderRadius: 10,
    overflow: 'hidden',
    borderWidth: 2,
    borderColor: '#FFFFFF',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.8,
    shadowRadius: 3,
    elevation: 5,
    zIndex: 999, // Assicura che la finestra PiP sia sopra tutti gli altri elementi
  },
  localStreamTouchable: {
    width: '100%',
    height: '100%',
  },
  localStream: {
    width: '100%',
    height: '100%',
  },
  localStreamLabel: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    padding: 5,
    alignItems: 'center',
  },
  localStreamLabelText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: 'bold',
  },
  cameraOffIndicator: {
    ...StyleSheet.absoluteFill,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  callStateContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  iconContainer: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
  },
  callStateText: {
    fontSize: 18,
    color: '#FFFFFF',
    marginTop: 20,
  },
  remoteNameText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginTop: 10,
  },
  callInfoContainer: {
    position: 'absolute',
    top: 40,
    left: 0,
    right: 0,
    alignItems: 'center',
    padding: 20,
  },
  callDurationText: {
    fontSize: 16,
    color: '#FFFFFF',
    marginTop: 5,
  },
  controlsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    position: 'absolute',
    bottom: 40,
    left: 0,
    right: 0,
    paddingHorizontal: 20,
  },
  controlButton: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  activeControlButton: {
    backgroundColor: theme.colors.primary,
  },
  endCallButton: {
    backgroundColor: '#F44336',
  },
  incomingCallActions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    width: '80%',
    marginTop: 40,
  },
  actionButton: {
    width: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
  },
  acceptButton: {
    backgroundColor: '#4CAF50',
  },
  declineButton: {
    backgroundColor: '#F44336',
  },
  streamPlaceholder: {
    color: '#FFFFFF',
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 8,
  },
  streamPlaceholderSubtext: {
    color: 'rgba(255, 255, 255, 0.7)',
    fontSize: 12,
    textAlign: 'center',
  },
  // 📞 PRODUCTION: STILI CHIAMATA AUDIO
  audioCallContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 100,
    paddingBottom: 150,
  },
  audioCallProfileContainer: {
    marginBottom: 30,
  },
  audioCallAvatar: {
    width: 150,
    height: 150,
    borderRadius: 75,
    borderWidth: 4,
    borderColor: '#FFFFFF',
  },
  audioCallAvatarPlaceholder: {
    width: 150,
    height: 150,
    borderRadius: 75,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 4,
    borderColor: '#FFFFFF',
  },
  audioCallName: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#FFFFFF',
    textAlign: 'center',
    marginBottom: 10,
  },
  audioCallTimer: {
    fontSize: 18,
    color: '#FFFFFF',
    textAlign: 'center',
    marginBottom: 5,
    fontFamily: Platform.OS === 'ios' ? 'Courier' : 'monospace',
  },
  audioCallStatus: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.8)',
    textAlign: 'center',
  },
});

export default CallScreenWebRTC;
