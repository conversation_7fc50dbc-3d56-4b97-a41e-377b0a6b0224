import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
  Alert,
  Platform,
  StatusBar,
  Dimensions
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';

const { width } = Dimensions.get('window');

const TrendyStoreScreen = () => {
  const navigation = useNavigation();
  const [selectedPackage, setSelectedPackage] = useState(null);

  // Pacchetti TrendyCoin disponibili
  const coinPackages = [
    {
      id: 1,
      coins: 100,
      price: 0.99,
      popular: false,
      bonus: 0,
      description: 'Pacchetto Base'
    },
    {
      id: 2,
      coins: 500,
      price: 4.99,
      popular: true,
      bonus: 50,
      description: 'Pacchetto Popolare'
    },
    {
      id: 3,
      coins: 1000,
      price: 9.99,
      popular: false,
      bonus: 150,
      description: 'Pacchetto Premium'
    },
    {
      id: 4,
      coins: 2500,
      price: 19.99,
      popular: false,
      bonus: 500,
      description: 'Pacchetto VIP'
    },
    {
      id: 5,
      coins: 5000,
      price: 39.99,
      popular: false,
      bonus: 1500,
      description: 'Pacchetto Elite'
    }
  ];

  const handlePurchase = (packageData) => {
    Alert.alert(
      '💳 Conferma Acquisto',
      `Vuoi acquistare ${packageData.coins + packageData.bonus} TrendyCoin per €${packageData.price}?`,
      [
        { text: 'Annulla', style: 'cancel' },
        {
          text: 'Acquista',
          onPress: () => processPurchase(packageData),
          style: 'default'
        }
      ]
    );
  };

  const processPurchase = (packageData) => {
    // TODO: Implementare logica di acquisto reale
    Alert.alert(
      '🎉 Acquisto Completato!',
      `Hai ricevuto ${packageData.coins + packageData.bonus} TrendyCoin!\n\nGrazie per il tuo acquisto!`,
      [
        {
          text: 'OK',
          onPress: () => navigation.goBack()
        }
      ]
    );
  };

  const renderPackage = (pkg) => (
    <TouchableOpacity
      key={pkg.id}
      style={[
        styles.packageCard,
        pkg.popular && styles.popularPackage,
        selectedPackage === pkg.id && styles.selectedPackage
      ]}
      onPress={() => {
        setSelectedPackage(pkg.id);
        handlePurchase(pkg);
      }}
    >
      {pkg.popular && (
        <View style={styles.popularBadge}>
          <Text style={styles.popularText}>🔥 POPOLARE</Text>
        </View>
      )}

      <View style={styles.packageHeader}>
        <Text style={styles.packageTitle}>{pkg.description}</Text>
        <Text style={styles.packagePrice}>€{pkg.price}</Text>
      </View>

      <View style={styles.packageContent}>
        <View style={styles.coinsContainer}>
          <Ionicons name="diamond" size={24} color="#FFD700" />
          <Text style={styles.coinsAmount}>{pkg.coins}</Text>
          <Text style={styles.coinsLabel}>TrendyCoin</Text>
        </View>

        {pkg.bonus > 0 && (
          <View style={styles.bonusContainer}>
            <Text style={styles.bonusText}>+ {pkg.bonus} BONUS</Text>
            <Text style={styles.totalText}>Totale: {pkg.coins + pkg.bonus}</Text>
          </View>
        )}
      </View>

      <LinearGradient
        colors={pkg.popular ? ['#FFD700', '#FFA500'] : ['#1E88E5', '#D81B60']}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 0 }}
        style={styles.purchaseButton}
      >
        <Text style={styles.purchaseButtonText}>Acquista Ora</Text>
      </LinearGradient>
    </TouchableOpacity>
  );

  return (
    <>
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent={true} />
      <SafeAreaView style={styles.container}>
        {/* Header */}
        <LinearGradient
          colors={['#1E88E5', '#D81B60']}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 0 }}
          style={styles.header}
        >
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <Ionicons name="arrow-back" size={24} color="#FFFFFF" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>TrendyStore</Text>
          <TouchableOpacity
            style={styles.walletButton}
            onPress={() => navigation.navigate('TrendyWallet')}
          >
            <Ionicons name="wallet" size={24} color="#FFFFFF" />
          </TouchableOpacity>
        </LinearGradient>

        <ScrollView style={styles.scrollContainer}>
          {/* Intro */}
          <View style={styles.introSection}>
            <Text style={styles.introTitle}>💎 Acquista TrendyCoin</Text>
            <Text style={styles.introDescription}>
              Usa i TrendyCoin per inviare regali durante le live stream e supportare i tuoi creator preferiti!
            </Text>
          </View>

          {/* Vantaggi */}
          <View style={styles.benefitsSection}>
            <Text style={styles.sectionTitle}>✨ Cosa puoi fare con i TrendyCoin:</Text>

            <View style={styles.benefitItem}>
              <Ionicons name="gift" size={20} color="#1E88E5" />
              <Text style={styles.benefitText}>Invia regali virtuali durante le live</Text>
            </View>

            <View style={styles.benefitItem}>
              <Ionicons name="heart" size={20} color="#E91E63" />
              <Text style={styles.benefitText}>Supporta i tuoi creator preferiti</Text>
            </View>

            <View style={styles.benefitItem}>
              <Ionicons name="star" size={20} color="#FFD700" />
              <Text style={styles.benefitText}>Ottieni riconoscimenti speciali</Text>
            </View>
          </View>

          {/* Pacchetti */}
          <View style={styles.packagesSection}>
            <Text style={styles.sectionTitle}>🛒 Scegli il tuo pacchetto:</Text>
            {coinPackages.map(renderPackage)}
          </View>

          {/* Info Sicurezza */}
          <View style={styles.securitySection}>
            <View style={styles.securityItem}>
              <Ionicons name="shield-checkmark" size={20} color="#4CAF50" />
              <Text style={styles.securityText}>Pagamenti sicuri e protetti</Text>
            </View>

            <View style={styles.securityItem}>
              <Ionicons name="flash" size={20} color="#FF9800" />
              <Text style={styles.securityText}>TrendyCoin disponibili immediatamente</Text>
            </View>

            <View style={styles.securityItem}>
              <Ionicons name="support" size={20} color="#2196F3" />
              <Text style={styles.securityText}>Supporto clienti 24/7</Text>
            </View>
          </View>
        </ScrollView>
      </SafeAreaView>
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#121212',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    paddingTop: Platform.OS === 'ios' ? 50 : 30,
  },
  backButton: {
    padding: 8,
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  walletButton: {
    padding: 8,
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  scrollContainer: {
    flex: 1,
    backgroundColor: '#121212',
  },
  introSection: {
    padding: 20,
    alignItems: 'center',
  },
  introTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 12,
    textAlign: 'center',
  },
  introDescription: {
    fontSize: 16,
    color: '#AAAAAA',
    textAlign: 'center',
    lineHeight: 24,
  },
  benefitsSection: {
    marginHorizontal: 20,
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 16,
  },
  benefitItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  benefitText: {
    fontSize: 16,
    color: '#FFFFFF',
    marginLeft: 12,
  },
  packagesSection: {
    marginHorizontal: 20,
    marginBottom: 24,
  },
  packageCard: {
    backgroundColor: '#1E1E1E',
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    borderWidth: 2,
    borderColor: 'transparent',
    position: 'relative',
  },
  popularPackage: {
    borderColor: '#FFD700',
    backgroundColor: '#2A2A2A',
  },
  selectedPackage: {
    borderColor: '#1E88E5',
  },
  popularBadge: {
    position: 'absolute',
    top: -8,
    right: 16,
    backgroundColor: '#FFD700',
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 12,
  },
  popularText: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#000000',
  },
  packageHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  packageTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  packagePrice: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1E88E5',
  },
  packageContent: {
    marginBottom: 20,
  },
  coinsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  coinsAmount: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginLeft: 8,
  },
  coinsLabel: {
    fontSize: 16,
    color: '#AAAAAA',
    marginLeft: 8,
  },
  bonusContainer: {
    alignItems: 'flex-start',
  },
  bonusText: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#4CAF50',
    marginBottom: 4,
  },
  totalText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  purchaseButton: {
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
  },
  purchaseButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  securitySection: {
    marginHorizontal: 20,
    marginBottom: 40,
    backgroundColor: '#1E1E1E',
    borderRadius: 12,
    padding: 20,
  },
  securityItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  securityText: {
    fontSize: 14,
    color: '#AAAAAA',
    marginLeft: 12,
  },
});

export default TrendyStoreScreen;
