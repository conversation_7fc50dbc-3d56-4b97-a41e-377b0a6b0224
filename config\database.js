// Configurazione database

const database = {
  // Configurazione Firebase (DISABILITATO)
  firebase: {
    enabled: false, // Firebase rimosso - usa server HP
    apiKey: null,
    authDomain: null,
    projectId: null,
    storageBucket: null,
    messagingSenderId: null,
    appId: null,
    measurementId: null
  },

  // Configurazione Firestore
  firestore: {
    // Configurazione collezioni
    collections: {
      users: {
        name: 'users',
        indexes: ['email', 'username', 'phone'],
        subcollections: {
          contacts: 'contacts',
          groups: 'groups',
          settings: 'settings',
        },
      },
      chats: {
        name: 'chats',
        indexes: ['participants', 'lastMessage', 'createdAt'],
        subcollections: {
          messages: 'messages',
          media: 'media',
          files: 'files',
          links: 'links',
        },
      },
      groups: {
        name: 'groups',
        indexes: ['members', 'admins', 'createdAt'],
        subcollections: {
          messages: 'messages',
          media: 'media',
          files: 'files',
          links: 'links',
          members: 'members',
          admins: 'admins',
        },
      },
      calls: {
        name: 'calls',
        indexes: ['participants', 'startTime', 'endTime'],
        subcollections: {
          participants: 'participants',
          media: 'media',
        },
      },
      status: {
        name: 'status',
        indexes: ['userId', 'createdAt', 'expiresAt'],
        subcollections: {
          views: 'views',
          replies: 'replies',
          forwards: 'forwards',
          screenshots: 'screenshots',
        },
      },
      notifications: {
        name: 'notifications',
        indexes: ['userId', 'type', 'createdAt'],
        subcollections: {
          settings: 'settings',
        },
      },
      settings: {
        name: 'settings',
        indexes: ['userId', 'type'],
        subcollections: {
          privacy: 'privacy',
          security: 'security',
          notifications: 'notifications',
          appearance: 'appearance',
          language: 'language',
          data: 'data',
        },
      },
    },

    // Configurazione indici
    indexes: {
      users: [
        { fields: ['email'], order: 'asc' },
        { fields: ['username'], order: 'asc' },
        { fields: ['phone'], order: 'asc' },
      ],
      chats: [
        { fields: ['participants'], order: 'asc' },
        { fields: ['lastMessage'], order: 'desc' },
        { fields: ['createdAt'], order: 'desc' },
      ],
      groups: [
        { fields: ['members'], order: 'asc' },
        { fields: ['admins'], order: 'asc' },
        { fields: ['createdAt'], order: 'desc' },
      ],
      calls: [
        { fields: ['participants'], order: 'asc' },
        { fields: ['startTime'], order: 'desc' },
        { fields: ['endTime'], order: 'desc' },
      ],
      status: [
        { fields: ['userId'], order: 'asc' },
        { fields: ['createdAt'], order: 'desc' },
        { fields: ['expiresAt'], order: 'asc' },
      ],
      notifications: [
        { fields: ['userId'], order: 'asc' },
        { fields: ['type'], order: 'asc' },
        { fields: ['createdAt'], order: 'desc' },
      ],
      settings: [
        { fields: ['userId'], order: 'asc' },
        { fields: ['type'], order: 'asc' },
      ],
    },

    // Configurazione regole
    rules: {
      users: {
        read: 'auth != null',
        write: 'auth != null && auth.uid == resource.data.userId',
      },
      chats: {
        read: 'auth != null && auth.uid in resource.data.participants',
        write: 'auth != null && auth.uid in resource.data.participants',
      },
      groups: {
        read: 'auth != null && auth.uid in resource.data.members',
        write: 'auth != null && auth.uid in resource.data.admins',
      },
      calls: {
        read: 'auth != null && auth.uid in resource.data.participants',
        write: 'auth != null && auth.uid in resource.data.participants',
      },
      status: {
        read: 'auth != null',
        write: 'auth != null && auth.uid == resource.data.userId',
      },
      notifications: {
        read: 'auth != null && auth.uid == resource.data.userId',
        write: 'auth != null && auth.uid == resource.data.userId',
      },
      settings: {
        read: 'auth != null && auth.uid == resource.data.userId',
        write: 'auth != null && auth.uid == resource.data.userId',
      },
    },

    // Configurazione cache
    cache: {
      enabled: true,
      size: 100 * 1024 * 1024, // 100 MB
      maxAge: 3600, // 1 ora in secondi
      cleanupInterval: 300 // 5 minuti in secondi
    },

    // Configurazione offline
    offline: {
      enabled: true,
      persistence: true,
      maxSize: 50 * 1024 * 1024, // 50 MB
      maxAge: 86400 // 24 ore in secondi
    },

    // Configurazione batch
    batch: {
      maxSize: 500,
      timeout: 30000, // 30 secondi
      retryAttempts: 3,
      retryDelay: 1000 // 1 secondo
    },

    // Configurazione query
    query: {
      maxResults: 100,
      timeout: 30000, // 30 secondi
      cacheResults: true,
      cacheAge: 300 // 5 minuti in secondi
    }
  },

  // Configurazione Storage
  storage: {
    // Configurazione cartelle
    folders: {
      profile: {
        name: 'profile',
        maxSize: 5 * 1024 * 1024, // 5 MB
        allowedTypes: ['image/jpeg', 'image/png', 'image/gif'],
        maxWidth: 1920,
        maxHeight: 1080,
        quality: 0.8,
      },
      chat: {
        name: 'chat',
        maxSize: 100 * 1024 * 1024, // 100 MB
        allowedTypes: [
          'image/jpeg',
          'image/png',
          'image/gif',
          'video/mp4',
          'video/quicktime',
          'audio/mpeg',
          'audio/wav',
          'audio/m4a',
          'audio/aac',
          'application/pdf',
          'application/msword',
          'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
          'application/vnd.ms-excel',
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          'text/plain',
        ],
        maxWidth: 1920,
        maxHeight: 1080,
        quality: 0.8,
      },
      group: {
        name: 'group',
        maxSize: 5 * 1024 * 1024, // 5 MB
        allowedTypes: ['image/jpeg', 'image/png', 'image/gif'],
        maxWidth: 1920,
        maxHeight: 1080,
        quality: 0.8,
      },
      status: {
        name: 'status',
        maxSize: 16 * 1024 * 1024, // 16 MB
        allowedTypes: [
          'image/jpeg',
          'image/png',
          'image/gif',
          'video/mp4',
          'video/quicktime',
        ],
        maxWidth: 1920,
        maxHeight: 1080,
        quality: 0.8,
      },
    },

    // Configurazione regole
    rules: {
      profile: {
        read: 'auth != null',
        write: 'auth != null && auth.uid == resource.metadata.userId',
      },
      chat: {
        read: 'auth != null && auth.uid in resource.metadata.participants',
        write: 'auth != null && auth.uid in resource.metadata.participants',
      },
      group: {
        read: 'auth != null && auth.uid in resource.metadata.members',
        write: 'auth != null && auth.uid in resource.metadata.admins',
      },
      status: {
        read: 'auth != null',
        write: 'auth != null && auth.uid == resource.metadata.userId',
      },
    },

    // Configurazione cache
    cache: {
      enabled: true,
      size: 200 * 1024 * 1024, // 200 MB
      maxAge: 86400, // 24 ore in secondi
      cleanupInterval: 3600 // 1 ora in secondi
    },

    // Configurazione upload
    upload: {
      maxSize: 100 * 1024 * 1024, // 100 MB
      allowedTypes: [
        'image/jpeg',
        'image/png',
        'image/gif',
        'video/mp4',
        'audio/mpeg',
        'application/pdf'
      ],
      maxConcurrent: 3,
      retryAttempts: 3,
      retryDelay: 1000 // 1 secondo
    },

    // Configurazione download
    download: {
      maxConcurrent: 5,
      retryAttempts: 3,
      retryDelay: 1000, // 1 secondo
      timeout: 30000 // 30 secondi
    }
  },

  // Configurazione Realtime Database
  realtime: {
    // Configurazione nodi
    nodes: {
      presence: {
        path: 'presence',
        rules: {
          read: 'auth != null',
          write: 'auth != null && auth.uid == resource.data.userId',
        },
      },
      typing: {
        path: 'typing',
        rules: {
          read: 'auth != null',
          write: 'auth != null && auth.uid == resource.data.userId',
        },
      },
      calls: {
        path: 'calls',
        rules: {
          read: 'auth != null',
          write: 'auth != null && auth.uid == resource.data.userId',
        },
      },
    },

    // Configurazione regole
    rules: {
      presence: {
        read: 'auth != null',
        write: 'auth != null && auth.uid == resource.data.userId',
      },
      typing: {
        read: 'auth != null',
        write: 'auth != null && auth.uid == resource.data.userId',
      },
      calls: {
        read: 'auth != null',
        write: 'auth != null && auth.uid == resource.data.userId',
      },
    },

    // Configurazione connessione
    connection: {
      enabled: true,
      timeout: 30000, // 30 secondi
      retryAttempts: 3,
      retryDelay: 1000 // 1 secondo
    },

    // Configurazione cache
    cache: {
      enabled: true,
      size: 50 * 1024 * 1024, // 50 MB
      maxAge: 3600, // 1 ora in secondi
      cleanupInterval: 300 // 5 minuti in secondi
    },

    // Configurazione sincronizzazione
    sync: {
      enabled: true,
      interval: 5000, // 5 secondi
      batchSize: 100,
      maxRetries: 3
    }
  },

  // Configurazione backup
  backup: {
    enabled: true,
    schedule: 'daily',
    retention: 30, // giorni
    compression: true,
    encryption: true
  }
};

// Helper per verificare la connessione
export const checkConnection = async () => {
  // Firebase rimosso - usa server HP per verificare connessione
  try {
    const response = await fetch(`${process.env.API_URL || 'http://localhost:3000'}/api/health`);
    return response.ok;
  } catch (error) {
    console.warn('Connessione server non disponibile:', error);
    return false;
  }
};

// Helper per verificare lo stato della cache
export const checkCacheStatus = () => {
  const status = {
    firestore: {
      size: 0,
      items: 0,
      age: 0
    },
    storage: {
      size: 0,
      items: 0,
      age: 0
    },
    realtime: {
      size: 0,
      items: 0,
      age: 0
    }
  };
  
  // Implementa la logica per ottenere lo stato della cache
  
  return status;
};

// Helper per pulire la cache
export const clearCache = async (type = 'all') => {
  // Firebase rimosso - usa AsyncStorage per pulire la cache
  const AsyncStorage = require('@react-native-async-storage/async-storage').default;

  try {
    if (type === 'all') {
      await AsyncStorage.clear();
      console.log('✅ Cache completamente pulita');
    } else {
      // Pulisci cache specifica per tipo
      const keys = await AsyncStorage.getAllKeys();
      const keysToRemove = keys.filter(key => key.startsWith(type));
      await AsyncStorage.multiRemove(keysToRemove);
      console.log(`✅ Cache ${type} pulita`);
    }
  } catch (error) {
    console.error('❌ Errore pulizia cache:', error);
  }
};

// Helper per verificare lo stato del backup
export const checkBackupStatus = async () => {
  if (!database.backup.enabled) return null;
  
  try {
    // Implementa la logica per verificare lo stato del backup
    return {
      lastBackup: new Date(),
      size: 0,
      status: 'success'
    };
  } catch (error) {
    return {
      lastBackup: null,
      size: 0,
      status: 'error',
      error: error.message
    };
  }
};

// Helper per ottenere una configurazione del database
export const getDatabaseConfig = (path) => {
  const keys = path.split('.');
  let config = database;
  
  for (const key of keys) {
    if (!config[key]) return null;
    config = config[key];
  }
  
  return config;
};

// Helper per verificare se una configurazione del database esiste
export const hasDatabaseConfig = (path) => {
  const keys = path.split('.');
  let config = database;
  
  for (const key of keys) {
    if (!config[key]) return false;
    config = config[key];
  }
  
  return true;
};

// Helper per ottenere tutte le configurazioni del database di un percorso
export const getDatabaseConfigs = (path) => {
  const keys = path.split('.');
  let config = database;
  
  for (const key of keys) {
    if (!config[key]) return null;
    config = config[key];
  }
  
  return config;
};

// Helper per verificare se un tipo di file è consentito
export const isFileTypeAllowed = (type, folder) => {
  const config = database.storage.folders[folder];
  if (!config) return false;
  return config.allowedTypes.includes(type);
};

// Helper per verificare se un file supera la dimensione massima
export const isFileSizeAllowed = (size, folder) => {
  const config = database.storage.folders[folder];
  if (!config) return false;
  return size <= config.maxSize;
};

// Esporto le variabili vuote per mantenere la compatibilità
export const auth = null;
export const db = null;
export const storage = null;
export const functions = null;
export const messaging = null;

export default database; 