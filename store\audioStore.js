import AsyncStorage from '@react-native-async-storage/async-storage';
import { create } from 'zustand';
import { Audio } from 'expo-av';
import useAuthStore from './authStore';

const useAudioStore = create((set, get) => ({
  recording: null,
  sound: null,
  isRecording: false,
  isPlaying: false,
  duration: 0,
  loading: false,
  error: null,

  // Inizia la registrazione
  startRecording: async () => {
    try {
      set({ loading: true, error: null });

      await Audio.requestPermissionsAsync();
      await Audio.setAudioModeAsync({
        allowsRecordingIOS: true,
        playsInSilentModeIOS: true,
      });

      const { recording } = await Audio.Recording.createAsync(
        Audio.RecordingOptionsPresets.HIGH_QUALITY
      );

      set({ recording, isRecording: true });
    } catch (error) {
      console.error('Errore nell\'avvio della registrazione:', error);
      set({ error: 'Impossibile avviare la registrazione' });
      throw error;
    } finally {
      set({ loading: false });
    }
  },

  // Ferma la registrazione
  stopRecording: async () => {
    try {
      set({ loading: true, error: null });

      const { recording } = get();
      if (!recording) return;

      await recording.stopAndUnloadAsync();
      const uri = recording.getURI();

      set({ recording: null, isRecording: false });

      return uri;
    } catch (error) {
      console.error('Errore nell\'arresto della registrazione:', error);
      set({ error: 'Impossibile fermare la registrazione' });
      throw error;
    } finally {
      set({ loading: false });
    }
  },

  // Invia un messaggio vocale
  sendAudioMessage: async (chatId, audioUri) => {
    const { user } = useAuthStore.getState();
    if (!user) return;

    try {
      set({ loading: true, error: null });

      // Carica l'audio su Firebase Storage
      const response = await fetch(audioUri);
      const blob = await response.blob();
      
      const filename = `chats/${chatId}/audio/${Date.now()}.m4a`;
      const storageRef = ref(storage, filename);
      
      await setItem;
      const audioUrl = await getItem;

      // Crea il messaggio
      const messagesRef = AsyncStorage.collection("chats', chatId, 'messages");
      await addDoc(messagesRef, {
        type: 'audio',
        audioUrl,
        senderId: user.uid,
        timestamp: serverTimestamp(),
      });

      // Aggiorna l'ultimo messaggio nella chat
      const chatRef = doc;
      await updateDoc(chatRef, {
        lastMessage: {
          text: '🎤 Messaggio vocale',
          timestamp: serverTimestamp(),
        },
      });
    } catch (error) {
      console.error('Errore nell\'invio del messaggio vocale:', error);
      set({ error: 'Impossibile inviare il messaggio vocale' });
      throw error;
    } finally {
      set({ loading: false });
    }
  },

  // Riproduci un messaggio vocale
  playAudio: async (audioUrl) => {
    try {
      set({ loading: true, error: null });

      const { sound } = get();
      if (sound) {
        await sound.unloadAsync();
      }

      const { sound: newSound } = await Audio.Sound.createAsync(
        { uri: audioUrl },
        { shouldPlay: true }
      );

      set({ sound: newSound, isPlaying: true });

      newSound.setOnPlaybackStatusUpdate(status => {
        if (status.isLoaded) {
          set({ duration: status.durationMillis / 1000 });
        }
        if (status.didJustFinish) {
          set({ isPlaying: false });
        }
      });
    } catch (error) {
      console.error('Errore nella riproduzione dell\'audio:', error);
      set({ error: 'Impossibile riprodurre l\'audio' });
      throw error;
    } finally {
      set({ loading: false });
    }
  },

  // Pausa la riproduzione
  pauseAudio: async () => {
    try {
      const { sound } = get();
      if (sound) {
        await sound.pauseAsync();
        set({ isPlaying: false });
      }
    } catch (error) {
      console.error('Errore nella pausa dell\'audio:', error);
      set({ error: 'Impossibile mettere in pausa l\'audio' });
    }
  },

  // Riprendi la riproduzione
  resumeAudio: async () => {
    try {
      const { sound } = get();
      if (sound) {
        await sound.playAsync();
        set({ isPlaying: true });
      }
    } catch (error) {
      console.error('Errore nella ripresa dell\'audio:', error);
      set({ error: 'Impossibile riprendere l\'audio' });
    }
  },

  // Ferma la riproduzione
  stopAudio: async () => {
    try {
      const { sound } = get();
      if (sound) {
        await sound.stopAsync();
        await sound.unloadAsync();
        set({ sound: null, isPlaying: false, duration: 0 });
      }
    } catch (error) {
      console.error('Errore nell\'arresto dell\'audio:', error);
      set({ error: 'Impossibile fermare l\'audio' });
    }
  },

  // Imposta errore
  setError: (error) => set({ error }),
  
  // Pulisci errore
  clearError: () => set({ error: null }),
}));

export default useAudioStore;

// Esporto le variabili vuote per mantenere la compatibilità
export const db = null;
export const storage = null; 