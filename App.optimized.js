import React, { useEffect, useState, useRef, useCallback } from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { StatusBar } from 'expo-status-bar';
import { LogBox, View, Text, ActivityIndicator, Alert, AppState } from 'react-native';
import * as Notifications from 'expo-notifications';
import * as SplashScreen from 'expo-splash-screen';
import { registerForPushNotificationsAsync, handleNotificationResponse } from './services/NotificationService';
import useWebRTCStore from './store/webrtcStore';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import MainNavigator from './navigation/MainNavigator';
import AuthNavigator from './navigation/AuthNavigator';
import useAuthStore from './store/authStore';
import { ThemeProvider } from './context/ThemeContext';
import { AuthProvider } from './context/AuthContext';
import { CallProvider } from './context/CallContext';
import { loadFonts } from './utils/loadFonts';
import { theme } from './theme';

// Importa i servizi ottimizzati
import authService from './services/authService';
import * as cloudBackupService from './services/cloudBackupService';
import { preloadImages } from './utils/optimizedImageUtils';
import memoryOptimizer from './utils/memoryOptimizer';
import networkOptimizer from './utils/networkOptimizer';

// Previeni la chiusura automatica della schermata di splash
SplashScreen.preventAutoHideAsync().catch(() => {});

// Ignora avvisi specifici
LogBox.ignoreLogs([
  'Animated: `useNativeDriver`',
  'Setting a timer',
  'AsyncStorage has been extracted',
  'VirtualizedLists should never be nested',
  'Non-serializable values were found in the navigation state',
]);

export default function App() {
  // Inizializzazione silenziosa senza log ripetitivi
  const { user, isLoading, initialize } = useAuthStore();
  const [fontsLoaded, setFontsLoaded] = useState(false);
  const [error, setError] = useState(null);
  const [expoPushToken, setExpoPushToken] = useState('');
  const [notification, setNotification] = useState(false);
  const notificationListener = useRef();
  const responseListener = useRef();
  const navigationRef = useRef();
  const appState = useRef(AppState.currentState);
  
  // Flag per tracciare se l'app è già stata inizializzata
  const isInitialized = useRef(false);
  
  // Riferimenti per i cleanup
  const cleanupFunctions = useRef([]);

  // Funzione per gestire i cambiamenti di stato dell'app
  const handleAppStateChange = useCallback((nextAppState) => {
    if (appState.current.match(/inactive|background/) && nextAppState === 'active') {
      // L'app è tornata in primo piano
      console.log('App tornata in primo piano');
    } else if (nextAppState.match(/inactive|background/) && appState.current === 'active') {
      // L'app è andata in background
      console.log('App andata in background');
    }
    
    appState.current = nextAppState;
  }, []);

  // Effetto per l'inizializzazione dell'app
  useEffect(() => {
    // Evita inizializzazioni multiple
    if (isInitialized.current) return;
    isInitialized.current = true;

    console.log('App.js: Inizializzazione dell\'app');

    const initializeApp = async () => {
      try {
        // Inizializza il monitoraggio della rete
        const unsubscribeNetwork = networkOptimizer.initNetworkMonitoring();
        cleanupFunctions.current.push(unsubscribeNetwork);
        
        // Inizializza il monitoraggio della memoria
        const unsubscribeMemory = memoryOptimizer.initMemoryMonitoring();
        cleanupFunctions.current.push(unsubscribeMemory);
        
        // Carica i font
        await loadFonts();
        setFontsLoaded(true);

        // Inizializza l'autenticazione
        await initialize();

        // Precarica le immagini comuni
        await preloadImages([
          require('./assets/logo.png'),
          require('./assets/default-avatar.png'),
          require('./assets/placeholder.png'),
        ]);

        // Configura le notifiche push
        const token = await registerForPushNotificationsAsync();
        setExpoPushToken(token);

        // Configura i listener per le notifiche
        notificationListener.current = Notifications.addNotificationReceivedListener(notification => {
          setNotification(notification);
        });

        responseListener.current = Notifications.addNotificationResponseReceivedListener(response => {
          handleNotificationResponse(response, navigationRef.current);
        });
        
        // Configura il listener per i cambiamenti di stato dell'app
        const subscription = AppState.addEventListener('change', handleAppStateChange);
        cleanupFunctions.current.push(() => subscription.remove());

        // Nascondi la schermata di splash
        await SplashScreen.hideAsync();
      } catch (err) {
        console.error('Errore durante l\'inizializzazione dell\'app:', err);
        setError(err.message);
        
        // Nascondi la schermata di splash anche in caso di errore
        await SplashScreen.hideAsync();
      }
    };

    initializeApp();

    // Pulizia quando il componente viene smontato
    return () => {
      // Rimuovi i listener delle notifiche
      if (notificationListener.current) {
        Notifications.removeNotificationSubscription(notificationListener.current);
      }
      if (responseListener.current) {
        Notifications.removeNotificationSubscription(responseListener.current);
      }
      
      // Esegui tutte le funzioni di pulizia
      cleanupFunctions.current.forEach(cleanup => {
        if (typeof cleanup === 'function') {
          cleanup();
        }
      });
    };
  }, [initialize, handleAppStateChange]);

  // Mostra un indicatore di caricamento se l'app è ancora in fase di inizializzazione
  if (isLoading || !fontsLoaded) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: theme.colors.background }}>
        <ActivityIndicator size="large" color={theme.colors.primary} />
      </View>
    );
  }

  // Mostra un messaggio di errore se c'è stato un problema durante l'inizializzazione
  if (error) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: theme.colors.background, padding: 20 }}>
        <Text style={{ color: theme.colors.error, fontSize: 16, textAlign: 'center', marginBottom: 20 }}>
          Si è verificato un errore durante l'avvio dell'app: {error}
        </Text>
        <Text style={{ color: theme.colors.text, fontSize: 14, textAlign: 'center' }}>
          Prova a riavviare l'applicazione. Se il problema persiste, contatta l'assistenza.
        </Text>
      </View>
    );
  }

  // Controlla se l'utente è autenticato e reindirizza alla schermata appropriata
  return (
    <SafeAreaProvider>
      <NavigationContainer ref={navigationRef}>
        <ThemeProvider>
          <AuthProvider>
            <CallProvider>
              <StatusBar style="light-content" translucent={true} backgroundColor="transparent" hidden={false} />
              {user ? <MainNavigator /> : <AuthNavigator />}
            </CallProvider>
          </AuthProvider>
        </ThemeProvider>
      </NavigationContainer>
    </SafeAreaProvider>
  );
}
