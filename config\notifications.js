import AsyncStorage from '@react-native-async-storage/async-storage';
import * as Notifications from 'expo-notifications';
import { Platform } from 'react-native';
import useAuthStore from '../store/authStore';

Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: true,
  }),
});

export async function registerForPushNotificationsAsync() {
  let token;

  if (Platform.OS === 'android') {
    await Notifications.setNotificationChannelAsync('default', {
      name: 'default',
      importance: Notifications.AndroidImportance.MAX,
      vibrationPattern: [0, 250, 250, 250],
      lightColor: '#FF231F7C',
    });
  }

  const { status: existingStatus } = await Notifications.getPermissionsAsync();
  let finalStatus = existingStatus;
  
  if (existingStatus !== 'granted') {
    const { status } = await Notifications.requestPermissionsAsync();
    finalStatus = status;
  }
  
  if (finalStatus !== 'granted') {
    alert('Non è stato possibile ottenere il token per le notifiche push!');
    return;
  }
  
  token = (await Notifications.getExpoPushTokenAsync()).data;

  if (AsyncStorage.getItem("currentUser")) {
    await AsyncStorage.getItem("currentUser").updateProfile({
      pushToken: token
    });
  }

  return token;
}

export async function schedulePushNotification(title, body, data = {}) {
  await Notifications.scheduleNotificationAsync({
    content: {
      title,
      body,
      data,
    },
    trigger: null,
  });
}

export async function cancelAllNotifications() {
  await Notifications.cancelAllScheduledNotificationsAsync();
}

export async function getBadgeCountAsync() {
  return await Notifications.getBadgeCountAsync();
}

export async function setBadgeCountAsync(count) {
  await Notifications.setBadgeCountAsync(count);
}

// Configurazione delle notifiche

const notificationTypes = {
  // Notifiche di chat
  chat: {
    newMessage: {
      title: 'Nuovo messaggio',
      body: '{sender} ti ha inviato un messaggio',
      sound: 'default',
      badge: 1,
      priority: 'high',
      vibrate: [0, 250, 250, 250]
    },
    groupMessage: {
      title: 'Nuovo messaggio di gruppo',
      body: '{sender} ha inviato un messaggio in {group}',
      sound: 'default',
      badge: 1,
      priority: 'high',
      vibrate: [0, 250, 250, 250]
    },
    mentioned: {
      title: 'Sei stato menzionato',
      body: '{sender} ti ha menzionato in {group}',
      sound: 'default',
      badge: 1,
      priority: 'high',
      vibrate: [0, 250, 250, 250]
    }
  },

  // Notifiche di chiamata
  call: {
    incoming: {
      title: 'Chiamata in arrivo',
      body: '{caller} sta chiamando',
      sound: 'ringtone',
      priority: 'max',
      vibrate: [0, 500, 200, 500]
    },
    missed: {
      title: 'Chiamata persa',
      body: 'Hai perso una chiamata da {caller}',
      sound: 'default',
      badge: 1,
      priority: 'high',
      vibrate: [0, 250, 250, 250]
    }
  },

  // Notifiche di stato
  status: {
    new: {
      title: 'Nuovo stato',
      body: '{user} ha pubblicato un nuovo stato',
      sound: 'default',
      badge: 1,
      priority: 'default',
      vibrate: [0, 250, 250, 250]
    },
    reply: {
      title: 'Risposta allo stato',
      body: '{user} ha risposto al tuo stato',
      sound: 'default',
      badge: 1,
      priority: 'default',
      vibrate: [0, 250, 250, 250]
    }
  },

  // Notifiche di gruppo
  group: {
    added: {
      title: 'Aggiunto al gruppo',
      body: 'Sei stato aggiunto al gruppo {group}',
      sound: 'default',
      badge: 1,
      priority: 'default',
      vibrate: [0, 250, 250, 250]
    },
    removed: {
      title: 'Rimosso dal gruppo',
      body: 'Sei stato rimosso dal gruppo {group}',
      sound: 'default',
      badge: 1,
      priority: 'default',
      vibrate: [0, 250, 250, 250]
    },
    promoted: {
      title: 'Promosso amministratore',
      body: 'Sei stato promosso amministratore del gruppo {group}',
      sound: 'default',
      badge: 1,
      priority: 'default',
      vibrate: [0, 250, 250, 250]
    }
  },

  // Notifiche di contatto
  contact: {
    request: {
      title: 'Nuova richiesta di contatto',
      body: '{user} vuole aggiungerti ai contatti',
      sound: 'default',
      badge: 1,
      priority: 'default',
      vibrate: [0, 250, 250, 250]
    },
    accepted: {
      title: 'Richiesta accettata',
      body: '{user} ha accettato la tua richiesta di contatto',
      sound: 'default',
      badge: 1,
      priority: 'default',
      vibrate: [0, 250, 250, 250]
    }
  }
};

// Configurazione delle impostazioni di notifica
const notificationSettings = {
  // Impostazioni generali
  general: {
    enabled: true,
    sound: true,
    vibration: true,
    badge: true,
    preview: true
  },

  // Impostazioni per tipo
  types: {
    chat: {
      enabled: true,
      sound: true,
      vibration: true,
      badge: true,
      preview: true
    },
    call: {
      enabled: true,
      sound: true,
      vibration: true,
      badge: true,
      preview: true
    },
    status: {
      enabled: true,
      sound: true,
      vibration: true,
      badge: true,
      preview: true
    },
    group: {
      enabled: true,
      sound: true,
      vibration: true,
      badge: true,
      preview: true
    },
    contact: {
      enabled: true,
      sound: true,
      vibration: true,
      badge: true,
      preview: true
    }
  },

  // Impostazioni per orario
  quietHours: {
    enabled: false,
    start: '22:00',
    end: '07:00',
    days: [0, 1, 2, 3, 4, 5, 6] // Tutti i giorni
  }
};

// Funzione helper per ottenere la configurazione di una notifica
export const getNotificationConfig = (type, subtype) => {
  return notificationTypes[type]?.[subtype] || null;
};

// Funzione helper per ottenere le impostazioni di notifica
export const getNotificationSettings = (type = 'general') => {
  return type === 'general' 
    ? notificationSettings.general 
    : notificationSettings.types[type];
};

// Funzione helper per verificare se le ore di silenzio sono attive
export const isQuietHours = () => {
  const { enabled, start, end, days } = notificationSettings.quietHours;
  
  if (!enabled) return false;
  
  const now = new Date();
  const currentDay = now.getDay();
  const currentTime = now.getHours() * 60 + now.getMinutes();
  
  if (!days.includes(currentDay)) return false;
  
  const [startHour, startMinute] = start.split(':').map(Number);
  const [endHour, endMinute] = end.split(':').map(Number);
  
  const startTime = startHour * 60 + startMinute;
  const endTime = endHour * 60 + endMinute;
  
  if (startTime <= endTime) {
    return currentTime >= startTime && currentTime <= endTime;
  } else {
    return currentTime >= startTime || currentTime <= endTime;
  }
};

export default {
  types: notificationTypes,
  settings: notificationSettings
}; 