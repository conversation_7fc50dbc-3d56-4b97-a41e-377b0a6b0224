import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  TextInput,
  Image,
  ActivityIndicator,
  Alert,
  SafeAreaView,
  StatusBar,
  Platform,
} from 'react-native';
import { useRoute, useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import useContactStore from '../store/contactStore';
import useCallStore from '../store/callStore';
import useGroupStore from '../store/groupStore';
import contactSearchService from '../services/contactSearchService';

const AddCallParticipantsScreen = () => {
  const route = useRoute();
  const navigation = useNavigation();
  const { callId, chatId, currentParticipants = [] } = route.params;
  const { contacts, loading: contactsLoading } = useContactStore();
  const { addParticipantToCall, callState, loading: callLoading } = useCallStore();
  const { currentGroup, loadGroup, loading: groupLoading } = useGroupStore();
  const [selectedContacts, setSelectedContacts] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [isAdding, setIsAdding] = useState(false);
  const [groupMembers, setGroupMembers] = useState([]);

  // 🔍 STATI PER RICERCA UTENTI (IDENTICI ALLA HOMESCREEN)
  const [userSearchResults, setUserSearchResults] = useState([]);
  const [isSearchingUsers, setIsSearchingUsers] = useState(false);
  const [showUserResults, setShowUserResults] = useState(false);

  // 👥 CARICA MEMBRI DEL GRUPPO
  useEffect(() => {
    const loadGroupMembers = async () => {
      if (chatId) {
        console.log('👥 IBRIDA: Caricando membri del gruppo:', chatId);
        try {
          await loadGroup(chatId);
        } catch (error) {
          console.error('❌ Errore nel caricamento gruppo:', error);
        }
      }
    };

    loadGroupMembers();
  }, [chatId]);

  // 👥 AGGIORNA MEMBRI QUANDO IL GRUPPO È CARICATO
  useEffect(() => {
    if (currentGroup && currentGroup.members) {
      console.log('👥 IBRIDA: Membri gruppo caricati:', currentGroup.members.length);
      setGroupMembers(currentGroup.members);
    }
  }, [currentGroup]);

  // 🔍 RICERCA UTENTI PER NUMERO DI TELEFONO (IDENTICA ALLA HOMESCREEN)
  useEffect(() => {
    const searchUsers = async () => {
      // Verifica se la query è un numero di telefono
      if (searchQuery.trim() && contactSearchService.isPhoneNumber(searchQuery)) {
        setIsSearchingUsers(true);
        setShowUserResults(true);

        try {
          const results = await contactSearchService.searchUsers(searchQuery);
          console.log('🔍 IBRIDA: Utenti trovati per numero:', results.length);
          setUserSearchResults(results);
        } catch (error) {
          console.error('❌ Errore nella ricerca degli utenti:', error);
          setUserSearchResults([]);
        } finally {
          setIsSearchingUsers(false);
        }
      } else {
        setShowUserResults(false);
        setUserSearchResults([]);
      }
    };

    // Esegui la ricerca con un piccolo ritardo per evitare troppe chiamate API
    const timeoutId = setTimeout(searchUsers, 500);

    return () => clearTimeout(timeoutId);
  }, [searchQuery]);

  // 🔍 DETERMINA QUALE LISTA MOSTRARE
  const getContactsToShow = () => {
    // Se stiamo mostrando risultati di ricerca utenti, usa quelli
    if (showUserResults && searchQuery.trim() && contactSearchService.isPhoneNumber(searchQuery)) {
      return userSearchResults.filter(user => {
        const userId = user.id || user._id;
        // ✅ LOGICA WHATSAPP: Non mostrare chi è già in chiamata
        return !currentParticipants.includes(userId);
      });
    }

    // Altrimenti usa i membri del gruppo o i contatti normali
    const availableContacts = chatId ? groupMembers : contacts;

    return availableContacts.filter(contact => {
      // Per i membri del gruppo, usa l'ID direttamente
      const contactId = contact.id || contact._id || contact;
      const contactName = contact.displayName || contact.name || 'Utente';

      return (
        // Non mostrare i partecipanti già in chiamata
        !currentParticipants.includes(contactId) &&
        // Filtra per nome se c'è una query di ricerca (solo se non è un numero di telefono)
        (!searchQuery.trim() ||
         contactSearchService.isPhoneNumber(searchQuery) ||
         contactName.toLowerCase().includes(searchQuery.toLowerCase()))
      );
    });
  };

  const filteredContacts = getContactsToShow();

  const handleSelectContact = (contact) => {
    if (selectedContacts.some(c => c.id === contact.id)) {
      setSelectedContacts(selectedContacts.filter(c => c.id !== contact.id));
    } else {
      setSelectedContacts([...selectedContacts, contact]);
    }
  };

  const handleAddParticipants = async () => {
    if (selectedContacts.length === 0) {
      Alert.alert('Errore', 'Seleziona almeno un contatto');
      return;
    }

    try {
      setIsAdding(true);
      const participantIds = selectedContacts.map(contact => contact.id);
      await addParticipantToCall(callId, participantIds);

      Alert.alert(
        'Partecipanti aggiunti',
        `${selectedContacts.length} nuovi partecipanti aggiunti alla chiamata`,
        [
          {
            text: 'OK',
            onPress: () => navigation.goBack()
          }
        ]
      );
    } catch (error) {
      console.error("Errore nell'aggiunta dei partecipanti:", error);
      Alert.alert('Errore', 'Impossibile aggiungere i partecipanti alla chiamata');
    } finally {
      setIsAdding(false);
    }
  };

  const renderContact = ({ item }) => {
    // Gestisci sia membri del gruppo che contatti normali
    const contactId = item.id || item._id || item;
    const contactName = item.displayName || item.name || 'Utente';
    const contactPhoto = item.photoURL || item.avatar || 'https://via.placeholder.com/50';
    const isOnline = item.online || false;

    const isSelected = selectedContacts.some(c => (c.id || c._id || c) === contactId);

    return (
      <TouchableOpacity
        style={[styles.contactItem, isSelected && styles.selectedItem]}
        onPress={() => handleSelectContact({
          id: contactId,
          name: contactName,
          photoURL: contactPhoto,
          online: isOnline
        })}
      >
        <View style={styles.checkboxContainer}>
          {isSelected ? (
            <View style={styles.checkbox}>
              <Ionicons name="checkmark" size={16} color="#FFFFFF" />
            </View>
          ) : (
            <View style={styles.emptyCheckbox} />
          )}
        </View>

        <Image
          source={{ uri: contactPhoto }}
          style={styles.contactImage}
        />

        <View style={styles.contactInfo}>
          <Text style={styles.contactName}>{contactName}</Text>
          <Text style={styles.contactStatus}>
            {isOnline ? 'Online' : 'Ultimo accesso recente'}
          </Text>
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#1E88E5" />

      <LinearGradient
        colors={['#1E88E5', '#D81B60']}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 0 }}
        style={styles.header}
      >
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color="#FFFFFF" />
        </TouchableOpacity>

        <Text style={styles.headerTitle}>Aggiungi partecipanti</Text>

        {selectedContacts.length > 0 && (
          <TouchableOpacity
            style={styles.addButton}
            onPress={handleAddParticipants}
            disabled={isAdding}
          >
            {isAdding ? (
              <ActivityIndicator size="small" color="#FFFFFF" />
            ) : (
              <Ionicons name="checkmark" size={24} color="#FFFFFF" />
            )}
          </TouchableOpacity>
        )}
      </LinearGradient>

      {selectedContacts.length > 0 && (
        <View style={styles.selectedContactsContainer}>
          <Text style={styles.selectedCountText}>
            {selectedContacts.length} contatti selezionati
          </Text>
        </View>
      )}

      <View style={styles.searchContainer}>
        <Ionicons name="search" size={20} color="#AAAAAA" />
        <TextInput
          style={styles.searchInput}
          placeholder="Cerca contatti"
          placeholderTextColor="#AAAAAA"
          value={searchQuery}
          onChangeText={setSearchQuery}
        />
        {searchQuery.length > 0 && (
          <TouchableOpacity onPress={() => setSearchQuery('')}>
            <Ionicons name="close-circle" size={20} color="#AAAAAA" />
          </TouchableOpacity>
        )}
      </View>

      {contactsLoading || callLoading || groupLoading || isSearchingUsers ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#1E88E5" />
          <Text style={styles.loadingText}>
            {isSearchingUsers ? 'Ricerca utenti...' : 'Caricamento...'}
          </Text>
        </View>
      ) : (
        <FlatList
          data={filteredContacts}
          renderItem={renderContact}
          keyExtractor={item => item.id || item._id || item}
          contentContainerStyle={styles.contactsList}
          ListEmptyComponent={
            <View style={styles.emptyContainer}>
              <Ionicons name="people" size={64} color="#AAAAAA" />
              <Text style={styles.emptyText}>
                {showUserResults && contactSearchService.isPhoneNumber(searchQuery)
                  ? `Nessun utente trovato con il numero ${searchQuery}`
                  : chatId
                    ? 'Nessun membro disponibile'
                    : 'Nessun contatto disponibile'
                }
              </Text>
              <Text style={styles.emptySubtext}>
                {showUserResults && contactSearchService.isPhoneNumber(searchQuery)
                  ? 'Invita i tuoi amici a unirsi a TrendyChat!'
                  : chatId
                    ? 'Tutti i membri del gruppo sono già nella chiamata'
                    : 'Tutti i tuoi contatti sono già nella chiamata o non sono online'
                }
              </Text>
            </View>
          }
        />
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#121212',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingTop: Platform.OS === 'ios' ? 50 : 30,
    paddingBottom: 10,
    height: Platform.OS === 'ios' ? 110 : 100,
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFFFFF',
    flex: 1,
    marginLeft: 8,
  },
  addButton: {
    padding: 8,
  },
  selectedContactsContainer: {
    padding: 12,
    backgroundColor: '#1E1E1E',
    borderBottomWidth: 1,
    borderBottomColor: '#2A2A2A',
  },
  selectedCountText: {
    fontSize: 14,
    color: '#1E88E5',
    fontWeight: 'bold',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    backgroundColor: '#1E1E1E',
    borderBottomWidth: 1,
    borderBottomColor: '#2A2A2A',
  },
  searchInput: {
    flex: 1,
    marginLeft: 8,
    color: '#FFFFFF',
    fontSize: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    color: '#FFFFFF',
    marginTop: 10,
    fontSize: 16,
  },
  contactsList: {
    paddingVertical: 8,
  },
  contactItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    backgroundColor: '#1E1E1E',
  },
  selectedItem: {
    backgroundColor: '#2A2A2A',
  },
  checkboxContainer: {
    width: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  checkbox: {
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: '#1E88E5',
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyCheckbox: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 1,
    borderColor: '#AAAAAA',
  },
  contactImage: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: 12,
  },
  contactInfo: {
    flex: 1,
  },
  contactName: {
    fontSize: 16,
    color: '#FFFFFF',
  },
  contactStatus: {
    fontSize: 14,
    color: '#AAAAAA',
    marginTop: 2,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    marginTop: 50,
  },
  emptyText: {
    fontSize: 16,
    color: '#FFFFFF',
    marginTop: 16,
    textAlign: 'center',
  },
  emptySubtext: {
    fontSize: 14,
    color: '#AAAAAA',
    marginTop: 8,
    textAlign: 'center',
  },
});

export default AddCallParticipantsScreen;
