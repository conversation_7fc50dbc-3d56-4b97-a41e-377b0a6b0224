import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  Animated,
  Dimensions
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { useNavigation } from '@react-navigation/native';
import { answerCall, rejectCall } from '../services/callService';

const { width } = Dimensions.get('window');

const IncomingCallNotification = ({ call, onDismiss }) => {
  const navigation = useNavigation();
  const slideAnim = React.useRef(new Animated.Value(-width)).current;

  React.useEffect(() => {
    Animated.spring(slideAnim, {
      toValue: 0,
      useNativeDriver: true,
      tension: 50,
      friction: 7
    }).start();
  }, []);

  const handleAnswer = async () => {
    try {
      await answerCall(call.id);
      navigation.navigate('Call', {
        callId: call.id,
        isIncoming: true,
        recipientId: call.callerId
      });
      onDismiss();
    } catch (error) {
      console.error('Errore risposta chiamata:', error);
    }
  };

  const handleReject = async () => {
    try {
      await rejectCall(call.id);
      onDismiss();
    } catch (error) {
      console.error('Errore rifiuto chiamata:', error);
    }
  };

  return (
    <Animated.View
      style={[
        styles.container,
        {
          transform: [{ translateX: slideAnim }]
        }
      ]}
    >
      <View style={styles.content}>
        <View style={styles.callerInfo}>
          <Image
            source={{ uri: call.callerPhotoURL }}
            style={styles.avatar}
          />
          <View style={styles.textContainer}>
            <Text style={styles.name}>{call.callerName}</Text>
            <Text style={styles.type}>
              {call.type === 'video' ? 'Videochiamata' : 'Chiamata vocale'}
            </Text>
          </View>
        </View>

        <View style={styles.actions}>
          <TouchableOpacity
            style={[styles.button, styles.rejectButton]}
            onPress={handleReject}
          >
            <Icon name="phone-hangup" size={24} color="#fff" />
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.button, styles.answerButton]}
            onPress={handleAnswer}
          >
            <Icon
              name={call.type === 'video' ? 'video' : 'phone'}
              size={24}
              color="#fff"
            />
          </TouchableOpacity>
        </View>
      </View>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    backgroundColor: '#fff',
    borderRadius: 15,
    margin: 10,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  content: {
    padding: 15,
  },
  callerInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 15,
  },
  avatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    marginRight: 15,
  },
  textContainer: {
    flex: 1,
  },
  name: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  type: {
    fontSize: 14,
    color: '#666',
  },
  actions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  button: {
    width: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
  },
  rejectButton: {
    backgroundColor: '#ff4444',
  },
  answerButton: {
    backgroundColor: '#4CAF50',
  },
});

export default IncomingCallNotification; 