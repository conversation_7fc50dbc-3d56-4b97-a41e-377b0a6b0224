import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  StatusBar,
  TouchableOpacity,
  TextInput,
  Alert,
  ActivityIndicator,
  Image,
  Platform,
  ScrollView,
} from 'react-native';
import { useRoute, useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import * as ImagePicker from 'expo-image-picker';
import useGroupStore from '../store/groupStore';

const EditGroupScreen = () => {
  const route = useRoute();
  const navigation = useNavigation();
  const { groupId } = route.params;
  const { currentGroup, updateGroup, loading } = useGroupStore();
  
  const [groupName, setGroupName] = useState('');
  const [groupDescription, setGroupDescription] = useState('');
  const [groupImage, setGroupImage] = useState('');
  const [isUpdating, setIsUpdating] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);

  // Inizializza i campi con i dati del gruppo
  useEffect(() => {
    if (currentGroup) {
      setGroupName(currentGroup.name || '');
      setGroupDescription(currentGroup.description || '');
      setGroupImage(currentGroup.photoURL || '');
    }
  }, [currentGroup]);

  // Controlla se ci sono modifiche
  useEffect(() => {
    const nameChanged = groupName !== (currentGroup?.name || '');
    const descChanged = groupDescription !== (currentGroup?.description || '');
    const imageChanged = groupImage !== (currentGroup?.photoURL || '');
    
    setHasChanges(nameChanged || descChanged || imageChanged);
  }, [groupName, groupDescription, groupImage, currentGroup]);

  const handleChangePhoto = async () => {
    try {
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      
      if (status !== 'granted') {
        Alert.alert('Permesso negato', 'È necessario il permesso per accedere alla galleria');
        return;
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        setGroupImage(result.assets[0].uri);
      }
    } catch (error) {
      console.error('Errore nella selezione dell\'immagine:', error);
      Alert.alert('Errore', 'Impossibile selezionare l\'immagine');
    }
  };

  const handleSave = async () => {
    if (!groupName.trim()) {
      Alert.alert('Errore', 'Il nome del gruppo non può essere vuoto');
      return;
    }

    try {
      setIsUpdating(true);
      
      const updateData = {
        name: groupName.trim(),
        description: groupDescription.trim(),
      };

      // Se l'immagine è cambiata, includila nell'aggiornamento
      if (groupImage !== (currentGroup?.photoURL || '')) {
        updateData.photoURL = groupImage;
      }

      await updateGroup(groupId, updateData);
      
      Alert.alert(
        'Successo', 
        'Informazioni del gruppo aggiornate con successo',
        [
          {
            text: 'OK',
            onPress: () => navigation.goBack()
          }
        ]
      );
    } catch (error) {
      console.error('Errore nell\'aggiornamento del gruppo:', error);
      Alert.alert('Errore', 'Impossibile aggiornare le informazioni del gruppo');
    } finally {
      setIsUpdating(false);
    }
  };

  const handleCancel = () => {
    if (hasChanges) {
      Alert.alert(
        'Modifiche non salvate',
        'Sei sicuro di voler uscire senza salvare le modifiche?',
        [
          { text: 'Continua modifica', style: 'cancel' },
          { 
            text: 'Esci senza salvare', 
            style: 'destructive',
            onPress: () => navigation.goBack()
          }
        ]
      );
    } else {
      navigation.goBack();
    }
  };

  if (loading) {
    return (
      <View style={styles.container}>
        <StatusBar barStyle="light-content" backgroundColor="#1E88E5" />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#1E88E5" />
        </View>
      </View>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#1E88E5" />
      
      <LinearGradient
        colors={['#1E88E5', '#D81B60']}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 0 }}
        style={styles.header}
      >
        <TouchableOpacity 
          style={styles.backButton}
          onPress={handleCancel}
        >
          <Ionicons name="arrow-back" size={24} color="#FFFFFF" />
        </TouchableOpacity>
        
        <Text style={styles.headerTitle}>Modifica gruppo</Text>
        
        {hasChanges && (
          <TouchableOpacity 
            style={styles.saveButton}
            onPress={handleSave}
            disabled={isUpdating}
          >
            {isUpdating ? (
              <ActivityIndicator size="small" color="#FFFFFF" />
            ) : (
              <Ionicons name="checkmark" size={24} color="#FFFFFF" />
            )}
          </TouchableOpacity>
        )}
      </LinearGradient>

      <ScrollView style={styles.content}>
        {/* Immagine del gruppo */}
        <View style={styles.imageSection}>
          <TouchableOpacity 
            style={styles.imageContainer}
            onPress={handleChangePhoto}
          >
            <Image
              source={{ uri: groupImage || 'https://via.placeholder.com/120' }}
              style={styles.groupImage}
            />
            <View style={styles.editImageButton}>
              <Ionicons name="camera" size={20} color="#FFFFFF" />
            </View>
          </TouchableOpacity>
          <Text style={styles.imageHint}>Tocca per cambiare l'immagine del gruppo</Text>
        </View>

        {/* Nome del gruppo */}
        <View style={styles.inputSection}>
          <Text style={styles.inputLabel}>Nome del gruppo</Text>
          <TextInput
            style={styles.textInput}
            value={groupName}
            onChangeText={setGroupName}
            placeholder="Inserisci il nome del gruppo"
            placeholderTextColor="#AAAAAA"
            maxLength={50}
          />
          <Text style={styles.characterCount}>{groupName.length}/50</Text>
        </View>

        {/* Descrizione del gruppo */}
        <View style={styles.inputSection}>
          <Text style={styles.inputLabel}>Descrizione del gruppo</Text>
          <TextInput
            style={[styles.textInput, styles.textArea]}
            value={groupDescription}
            onChangeText={setGroupDescription}
            placeholder="Inserisci una descrizione per il gruppo (opzionale)"
            placeholderTextColor="#AAAAAA"
            multiline
            numberOfLines={4}
            maxLength={200}
          />
          <Text style={styles.characterCount}>{groupDescription.length}/200</Text>
        </View>

        {/* Pulsante salva (per dispositivi senza header button) */}
        {hasChanges && (
          <TouchableOpacity 
            style={styles.saveButtonLarge}
            onPress={handleSave}
            disabled={isUpdating}
          >
            <LinearGradient
              colors={['#1E88E5', '#D81B60']}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 0 }}
              style={styles.saveButtonGradient}
            >
              {isUpdating ? (
                <ActivityIndicator size="small" color="#FFFFFF" />
              ) : (
                <>
                  <Ionicons name="checkmark" size={20} color="#FFFFFF" />
                  <Text style={styles.saveButtonText}>Salva modifiche</Text>
                </>
              )}
            </LinearGradient>
          </TouchableOpacity>
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#121212',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingTop: Platform.OS === 'ios' ? 50 : 30,
    paddingBottom: 10,
    height: Platform.OS === 'ios' ? 110 : 100,
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFFFFF',
    flex: 1,
    marginLeft: 8,
  },
  saveButton: {
    padding: 8,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  imageSection: {
    alignItems: 'center',
    marginBottom: 32,
  },
  imageContainer: {
    position: 'relative',
    marginBottom: 12,
  },
  groupImage: {
    width: 120,
    height: 120,
    borderRadius: 60,
  },
  editImageButton: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    backgroundColor: '#1E88E5',
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 3,
    borderColor: '#121212',
  },
  imageHint: {
    fontSize: 14,
    color: '#AAAAAA',
    textAlign: 'center',
  },
  inputSection: {
    marginBottom: 24,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
    marginBottom: 8,
  },
  textInput: {
    backgroundColor: '#1E1E1E',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    color: '#FFFFFF',
    borderWidth: 1,
    borderColor: '#2A2A2A',
  },
  textArea: {
    height: 100,
    textAlignVertical: 'top',
  },
  characterCount: {
    fontSize: 12,
    color: '#AAAAAA',
    textAlign: 'right',
    marginTop: 4,
  },
  saveButtonLarge: {
    marginTop: 24,
    borderRadius: 8,
    overflow: 'hidden',
  },
  saveButtonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 24,
  },
  saveButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
});

export default EditGroupScreen;
