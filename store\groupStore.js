import AsyncStorage from '@react-native-async-storage/async-storage';
import axios from 'axios';
import { create } from 'zustand';
import useAuthStore from './authStore';
import { API_URL } from '../config/api';
import mediaCompressionService from '../services/mediaCompressionService';
import { Audio } from 'expo-av';

const useGroupStore = create((set, get) => ({
  groups: [],
  currentGroup: null,
  loading: false,
  error: null,

  // Carica i messaggi di un gruppo specifico dal server HP
  loadMessages: async (groupId) => {
    console.log('🔄 GroupStore: loadMessages chiamato per gruppo:', groupId);
    const { user } = useAuthStore.getState();
    if (!user) {
      console.log('❌ GroupStore: Utente non autenticato');
      return;
    }

    try {
      set({ loading: true, error: null });

      // Ottieni il token di autenticazione
      const token = await AsyncStorage.getItem('@trendychat:token');
      if (!token) {
        throw new Error('Token di autenticazione non trovato');
      }

      console.log('🔗 Chiamando endpoint messaggi gruppo:', `${API_URL}/groups/${groupId}/messages`);

      // Chiama il server HP per ottenere i messaggi del gruppo
      const response = await axios.get(`${API_URL}/groups/${groupId}/messages`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      console.log('📡 Risposta server messaggi ricevuta:', response.status);
      console.log('📡 DATI MESSAGGI DAL SERVER:', JSON.stringify(response.data, null, 2));

      if (response.data && response.data.messages) {
        const messages = response.data.messages.map(message => {
          console.log(`🔍 Processando messaggio ${message.type}:`, message.id);

          // 🛠️ SISTEMA DI RICOSTRUZIONE URL PER PRODUZIONE
          // Il server HP non salva gli URL, li ricostruiamo dal pattern dei file
          let reconstructedMessage = {
            id: message.id || message._id,
            text: message.text || message.content,
            user: {
              _id: message.sender?.id || message.senderId,
              name: message.sender?.displayName || message.senderName,
              avatar: message.sender?.photoURL || message.senderAvatar
            },
            createdAt: new Date(message.createdAt || message.timestamp),
            type: message.type || 'text',
            senderId: message.senderId,
            // Campi base
            audioUrl: message.audioUrl,
            duration: message.duration || 0,
            imageUrl: message.imageUrl,
            videoUrl: message.videoUrl,
            thumbnail: message.thumbnail,
            documentUrl: message.documentUrl,
            fileName: message.fileName,
            fileSize: message.fileSize,
            mediaUrl: message.mediaUrl
          };

          // 🔧 RICOSTRUZIONE URL MANCANTI (FALLBACK PER SERVER HP)
          if (!message.audioUrl && message.type === 'audio') {
            // Ricostruisci URL audio dal timestamp del messaggio
            const timestamp = new Date(message.timestamp).getTime();
            reconstructedMessage.audioUrl = `${API_URL}/files/file-${timestamp}-audio.m4a`;
            reconstructedMessage.duration = 5; // Durata di default
            console.log(`🔧 Ricostruito audioUrl: ${reconstructedMessage.audioUrl}`);
          }

          if (!message.imageUrl && message.type === 'image') {
            // Ricostruisci URL immagine dal timestamp del messaggio
            const timestamp = new Date(message.timestamp).getTime();
            reconstructedMessage.imageUrl = `${API_URL}/files/file-${timestamp}-image.jpg`;
            console.log(`🔧 Ricostruito imageUrl: ${reconstructedMessage.imageUrl}`);
          }

          if (!message.videoUrl && message.type === 'video') {
            // Ricostruisci URL video dal timestamp del messaggio
            const timestamp = new Date(message.timestamp).getTime();
            reconstructedMessage.videoUrl = `${API_URL}/files/file-${timestamp}-video.mp4`;
            reconstructedMessage.thumbnail = `${API_URL}/files/file-${timestamp}-thumb.jpg`;
            console.log(`🔧 Ricostruito videoUrl: ${reconstructedMessage.videoUrl}`);
          }

          return reconstructedMessage;
        });

        // 🔍 FILTRA MESSAGGI NASCOSTI (LOGICA WHATSAPP - IDENTICA AL CHATSTORE)
        const hiddenMessagesKey = `@trendychat:hidden_group_messages_${groupId}`;
        const existingHidden = await AsyncStorage.getItem(hiddenMessagesKey);
        const hiddenMessages = existingHidden ? JSON.parse(existingHidden) : [];

        // Filtra i messaggi nascosti per questo utente
        const visibleMessages = messages.filter(msg => !hiddenMessages.includes(msg.id));

        console.log(`🔍 Messaggi gruppo nascosti per questo utente: ${hiddenMessages.length}`);
        console.log(`✅ Messaggi gruppo visibili: ${visibleMessages.length}/${messages.length}`);

        // Aggiorna il gruppo corrente con i messaggi visibili
        const { groups } = get();
        const updatedGroups = groups.map(group =>
          group.id === groupId
            ? { ...group, messages: visibleMessages.reverse() } // Inverti per ordine cronologico
            : group
        );

        // Trova il gruppo corrente
        const currentGroup = updatedGroups.find(group => group.id === groupId);

        set({
          groups: updatedGroups,
          currentGroup: currentGroup
        });

        console.log(`✅ Caricati ${visibleMessages.length} messaggi per il gruppo ${groupId}`);
      } else {
        console.log('📭 Nessun messaggio trovato per il gruppo');
        // Imposta messaggi vuoti per il gruppo
        const { groups } = get();
        const updatedGroups = groups.map(group =>
          group.id === groupId
            ? { ...group, messages: [] }
            : group
        );
        const currentGroup = updatedGroups.find(group => group.id === groupId);
        set({
          groups: updatedGroups,
          currentGroup: currentGroup
        });
      }
    } catch (error) {
      console.error('❌ Errore nel caricamento messaggi gruppo:', error);
      set({ error: 'Impossibile caricare i messaggi del gruppo' });
    } finally {
      set({ loading: false });
    }
  },

  // Carica i gruppi dal server HP
  loadGroups: async () => {
    console.log('🔄 GroupStore: loadGroups chiamato');
    const { user } = useAuthStore.getState();
    if (!user) {
      console.log('❌ GroupStore: Utente non autenticato');
      return;
    }
    try {
      set({ loading: true, error: null });

      // Ottieni il token di autenticazione
      const token = await AsyncStorage.getItem('@trendychat:token');
      const response = await axios.get(`${API_URL}/groups`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      // Verifica che la risposta sia valida
      if (response.data && response.data.groups) {
        // Filtra SOLO i gruppi veri (logica WhatsApp finale: 3+ membri = gruppo)
        const realGroups = response.data.groups
          .filter(group =>
            group.members && group.members.length >= 3  // ✅ LOGICA WHATSAPP FINALE: 3+ membri = gruppo
          )
          .map(group => ({
            id: group.id || group._id,
            name: group.name,
            description: group.description || '',
            photoURL: group.photoURL,
            members: group.members || [],
            admins: group.admins || [],
            messages: group.messages || [],
            createdAt: group.createdAt,
            lastMessage: group.lastMessage || null
          }));

        set({ groups: realGroups });
      } else {
        set({ groups: [] });
      }
    } catch (error) {
      console.error('❌ Errore nel caricamento dei gruppi:', error);
      set({ error: 'Impossibile caricare i gruppi dal server' });
    } finally {
      set({ loading: false });
    }
  },

  // Crea un nuovo gruppo sul server HP
  createGroup: async (name, members, photo = null) => {
    const { user } = useAuthStore.getState();
    if (!user) {
      return;
    }

    try {
      set({ loading: true, error: null });

      let photoURL = null;

      // Se c'è una foto, comprimila e caricala
      if (photo) {
        try {
          // Comprimi l'immagine per i gruppi
          const compressedImage = await mediaCompressionService.compressImage(
            photo,
            'chat' // Usa impostazioni chat per i gruppi
          );

          // Carica l'immagine compressa sul server
          const cloudService = require('../services/cloudService').default;
          const uploadResult = await cloudService.uploadFile(compressedImage.uri, user.id || user.uid);

          if (uploadResult && uploadResult.file && uploadResult.file.url) {
            photoURL = uploadResult.file.url;
          }
        } catch (uploadError) {
          console.error('❌ Errore nel caricamento immagine gruppo:', uploadError);
          // Continua senza immagine se il caricamento fallisce
        }
      }

      // Ottieni il token di autenticazione
      const token = await AsyncStorage.getItem('@trendychat:token');

      // ✅ VALIDAZIONE WHATSAPP: MINIMO 3 MEMBRI TOTALI
      const totalMembers = members.length + 1; // +1 per il creatore
      if (totalMembers < 3) {
        throw new Error('I gruppi devono avere almeno 3 partecipanti');
      }

      // Prepara i dati del gruppo per il server
      const groupData = {
        name,
        description: '',
        photoURL: photoURL,
        members: members.map(member => member.id || member.uid || member),
        isPublic: true
      };

      // Crea il gruppo sul server HP
      const response = await axios.post(`${API_URL}/groups`, groupData, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.data && response.data.group) {
        const newGroup = {
          id: response.data.group.id || response.data.group._id,
          name: response.data.group.name,
          description: response.data.group.description || '',
          photoURL: response.data.group.photoURL || photoURL,
          members: response.data.group.members || [],
          admins: response.data.group.admins || [],
          messages: [],
          createdAt: response.data.group.createdAt
        };

        // Aggiungi il gruppo alla lista locale
        const newGroups = [...get().groups, newGroup];
        set({ groups: newGroups });

        return newGroup.id;
      } else {
        throw new Error('Risposta server non valida');
      }
    } catch (error) {
      console.error('❌ Errore nella creazione del gruppo:', error);
      set({ error: 'Impossibile creare il gruppo sul server' });
      throw error;
    } finally {
      set({ loading: false });
    }
  },

  // Invia un messaggio di testo al gruppo
  sendMessage: async (groupId, text) => {
    const { user } = useAuthStore.getState();
    if (!user) {
      return;
    }

    try {
      set({ loading: true, error: null });

      // Ottieni il token di autenticazione
      const token = await AsyncStorage.getItem('@trendychat:token');
      if (!token) {
        throw new Error('Token di autenticazione non trovato');
      }

      console.log('🔗 Inviando messaggio alla chat:', `${API_URL}/groups/${groupId}/messages`);

      // Prepara i dati del messaggio (IDENTICO AL CHATSTORE)
      const messageData = {
        text: text,
        type: 'text',
        senderId: user.id || user.uid,
        senderName: user.displayName || user.name,
        senderAvatar: user.photoURL || user.avatar
      };

      // Invia il messaggio al server HP
      const response = await axios.post(`${API_URL}/groups/${groupId}/messages`, messageData, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      console.log('📡 Messaggio inviato con successo:', response.status);
      console.log('📡 Risposta server messaggio:', JSON.stringify(response.data, null, 2));

      if (response.data && response.data.message) {
        // Aggiorna i messaggi localmente (IDENTICO AL CHATSTORE)
        const newMessage = {
          id: response.data.message.id || response.data.message._id,
          text: response.data.message.text,
          user: {
            _id: user.id || user.uid,
            name: user.displayName || user.name,
            avatar: user.photoURL || user.avatar
          },
          createdAt: new Date(response.data.message.createdAt || Date.now()),
          type: 'text',
          senderId: user.id || user.uid
        };

        // Aggiorna il gruppo corrente con il nuovo messaggio (IDENTICO AL CHATSTORE)
        const { groups, currentGroup } = get();
        const updatedGroups = groups.map(group =>
          group.id === groupId
            ? { ...group, messages: [newMessage, ...(group.messages || [])] }
            : group
        );

        const updatedCurrentGroup = currentGroup?.id === groupId
          ? { ...currentGroup, messages: [newMessage, ...(currentGroup.messages || [])] }
          : currentGroup;

        set({
          groups: updatedGroups,
          currentGroup: updatedCurrentGroup
        });

        console.log('✅ Messaggio aggiunto localmente alla chat');
      }
    } catch (error) {
      console.error('❌ Errore nell\'invio del messaggio:', error);
      set({ error: 'Impossibile inviare il messaggio' });
      throw error;
    } finally {
      set({ loading: false });
    }
  },

  // Invia un'immagine al gruppo
  sendImage: async (groupId, imageUri) => {
    const { user } = useAuthStore.getState();
    if (!user) {
      return;
    }

    try {
      set({ loading: true, error: null });

      // Ottieni il token di autenticazione
      const token = await AsyncStorage.getItem('@trendychat:token');
      if (!token) {
        throw new Error('Token di autenticazione non trovato');
      }



      // Crea FormData per upload del file immagine
      const formData = new FormData();
      formData.append('file', {
        uri: imageUri,
        name: `image_${Date.now()}.jpg`,
        type: 'image/jpeg',
      });

      // Upload del file immagine al server HP
      const uploadResponse = await axios.post(`${API_URL}/cloud/upload`, formData, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'multipart/form-data',
        },
      });



      if (!uploadResponse.data || !uploadResponse.data.file) {
        throw new Error('Errore nel caricamento del file immagine');
      }

      const imageUrl = uploadResponse.data.file.url;

      // Prepara i dati del messaggio immagine
      const messageData = {
        text: '📷 Immagine',
        type: 'image',
        imageUrl: imageUrl,
        mediaUrl: imageUrl, // ← AGGIUNTO: per compatibilità con sistema di cancellazione
        senderId: user.id || user.uid,
        senderName: user.displayName || user.name,
        senderAvatar: user.photoURL || user.avatar
      };

      console.log('📤 Inviando messaggio immagine alla chat:', `${API_URL}/groups/${groupId}/messages`);

      // Invia il messaggio al server HP
      const response = await axios.post(`${API_URL}/groups/${groupId}/messages`, messageData, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      console.log('📡 Messaggio immagine inviato con successo:', response.status);

      if (response.data && response.data.message) {
        // Aggiorna i messaggi localmente (IDENTICO AL CHATSTORE)
        const newMessage = {
          id: response.data.message.id || response.data.message._id,
          text: '📷 Immagine',
          type: 'image',
          imageUrl: imageUrl,
          user: {
            _id: user.id || user.uid,
            name: user.displayName || user.name,
            avatar: user.photoURL || user.avatar
          },
          createdAt: new Date(response.data.message.createdAt || response.data.message.timestamp || Date.now()),
          senderId: user.id || user.uid
        };

        // Aggiorna i messaggi del gruppo corrente (IDENTICO AL CHATSTORE)
        const { groups, currentGroup } = get();
        const updatedGroups = groups.map(group => {
          if (group.id === groupId) {
            return { ...group, messages: [newMessage, ...(group.messages || [])] };
          }
          return group;
        });

        const updatedCurrentGroup = currentGroup?.id === groupId
          ? { ...currentGroup, messages: [newMessage, ...(currentGroup.messages || [])] }
          : currentGroup;

        set({
          groups: updatedGroups,
          currentGroup: updatedCurrentGroup
        });

        console.log('✅ Messaggio immagine aggiunto localmente alla chat');
      }
    } catch (error) {
      console.error('❌ Errore nell\'invio dell\'immagine:', error);
      set({ error: 'Impossibile inviare l\'immagine' });
      throw error;
    } finally {
      set({ loading: false });
    }
  },

  // Invia un video al gruppo (IDENTICO AL CHATSTORE)
  sendVideo: async (groupId, videoUri, thumbnailUri) => {
    console.log('🎥 ChatStore: sendVideoMessage chiamato per chat:', groupId);
    const { user } = useAuthStore.getState();
    if (!user) {
      console.log('❌ ChatStore: Utente non autenticato');
      return;
    }

    try {
      set({ loading: true, error: null });

      // Ottieni il token di autenticazione
      const token = await AsyncStorage.getItem('@trendychat:token');
      if (!token) {
        throw new Error('Token di autenticazione non trovato');
      }

      console.log('📤 Caricando file video sul server HP...');

      // Crea FormData per upload del file video
      const formData = new FormData();
      formData.append('file', {
        uri: videoUri,
        name: `video_${Date.now()}.mp4`,
        type: 'video/mp4',
      });

      // Upload del file video al server HP
      const uploadResponse = await axios.post(`${API_URL}/cloud/upload`, formData, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'multipart/form-data',
        },
      });

      console.log('📡 File video caricato:', uploadResponse.status);
      console.log('📡 Risposta server upload video:', JSON.stringify(uploadResponse.data, null, 2));

      if (!uploadResponse.data || !uploadResponse.data.file) {
        throw new Error('Errore nel caricamento del file video');
      }

      const videoUrl = uploadResponse.data.file.url;

      // Prepara i dati del messaggio video
      const messageData = {
        text: '🎥 Video',
        type: 'video',
        videoUrl: videoUrl,
        mediaUrl: videoUrl, // ← AGGIUNTO: per compatibilità con sistema di cancellazione
        thumbnail: thumbnailUri, // Usa la thumbnail generata (IDENTICO AL CHATSTORE)
        senderId: user.id || user.uid,
        senderName: user.displayName || user.name,
        senderAvatar: user.photoURL || user.avatar
      };

      console.log('📤 Inviando messaggio video alla chat:', `${API_URL}/groups/${groupId}/messages`);

      // Invia il messaggio al server HP
      const response = await axios.post(`${API_URL}/groups/${groupId}/messages`, messageData, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      console.log('📡 Messaggio video inviato con successo:', response.status);

      if (response.data && response.data.message) {
        // Aggiorna i messaggi localmente (IDENTICO AL CHATSTORE)
        const newMessage = {
          id: response.data.message.id || response.data.message._id,
          text: '🎥 Video',
          type: 'video',
          videoUrl: videoUrl,
          thumbnail: thumbnailUri, // Usa la thumbnail generata (IDENTICO AL CHATSTORE)
          user: {
            _id: user.id || user.uid,
            name: user.displayName || user.name,
            avatar: user.photoURL || user.avatar
          },
          createdAt: new Date(response.data.message.createdAt || response.data.message.timestamp || Date.now()),
          senderId: user.id || user.uid
        };

        // Aggiorna i messaggi del gruppo corrente (IDENTICO AL CHATSTORE)
        const { groups, currentGroup } = get();
        const updatedGroups = groups.map(group => {
          if (group.id === groupId) {
            return { ...group, messages: [newMessage, ...(group.messages || [])] };
          }
          return group;
        });

        const updatedCurrentGroup = currentGroup?.id === groupId
          ? { ...currentGroup, messages: [newMessage, ...(currentGroup.messages || [])] }
          : currentGroup;

        set({
          groups: updatedGroups,
          currentGroup: updatedCurrentGroup
        });

        console.log('✅ Messaggio video aggiunto localmente alla chat');
      }
    } catch (error) {
      console.error('❌ Errore nell\'invio del video:', error);
      set({ error: 'Impossibile inviare il video' });
      throw error;
    } finally {
      set({ loading: false });
    }
  },

  // Invia un documento al gruppo (IDENTICO AL CHATSTORE)
  sendDocument: async (groupId, documentUri, documentName, documentSize, documentType) => {
    console.log('📄 ChatStore: sendDocumentMessage chiamato per chat:', groupId);
    const { user } = useAuthStore.getState();
    if (!user) {
      console.log('❌ ChatStore: Utente non autenticato');
      return;
    }

    try {
      set({ loading: true, error: null });

      // Ottieni il token di autenticazione
      const token = await AsyncStorage.getItem('@trendychat:token');
      if (!token) {
        throw new Error('Token di autenticazione non trovato');
      }

      console.log('📤 Caricando file documento sul server HP...');

      // Crea FormData per upload del file documento
      const formData = new FormData();
      formData.append('file', {
        uri: documentUri,
        name: documentName || `document_${Date.now()}`,
        type: documentType || 'application/octet-stream',
      });

      // Upload del file documento al server HP
      const uploadResponse = await axios.post(`${API_URL}/cloud/upload`, formData, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'multipart/form-data',
        },
      });

      console.log('📡 File documento caricato:', uploadResponse.status);

      if (!uploadResponse.data || !uploadResponse.data.file) {
        throw new Error('Errore nel caricamento del file documento');
      }

      const documentUrl = uploadResponse.data.file.url;

      // Prepara i dati del messaggio documento
      const messageData = {
        text: `📄 ${documentName || 'Documento'}`,
        type: 'document',
        documentUrl: documentUrl,
        mediaUrl: documentUrl, // ← AGGIUNTO: per compatibilità con sistema di cancellazione
        fileName: documentName,
        senderId: user.id || user.uid,
        senderName: user.displayName || user.name,
        senderAvatar: user.photoURL || user.avatar
      };

      console.log('📤 Inviando messaggio documento alla chat:', `${API_URL}/groups/${groupId}/messages`);

      // Invia il messaggio al server HP
      const response = await axios.post(`${API_URL}/groups/${groupId}/messages`, messageData, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      console.log('📡 Messaggio documento inviato con successo:', response.status);

      if (response.data && response.data.message) {
        // Aggiorna i messaggi localmente (IDENTICO AL CHATSTORE)
        const newMessage = {
          id: response.data.message.id || response.data.message._id,
          text: `📄 ${documentName || 'Documento'}`,
          type: 'document',
          documentUrl: documentUrl,
          fileName: documentName,
          user: {
            _id: user.id || user.uid,
            name: user.displayName || user.name,
            avatar: user.photoURL || user.avatar
          },
          createdAt: new Date(response.data.message.createdAt || response.data.message.timestamp || Date.now()),
          senderId: user.id || user.uid
        };

        // Aggiorna i messaggi del gruppo corrente (IDENTICO AL CHATSTORE)
        const { groups, currentGroup } = get();
        const updatedGroups = groups.map(group => {
          if (group.id === groupId) {
            return { ...group, messages: [newMessage, ...(group.messages || [])] };
          }
          return group;
        });

        const updatedCurrentGroup = currentGroup?.id === groupId
          ? { ...currentGroup, messages: [newMessage, ...(currentGroup.messages || [])] }
          : currentGroup;

        set({
          groups: updatedGroups,
          currentGroup: updatedCurrentGroup
        });

        console.log('✅ Messaggio documento aggiunto localmente alla chat');
      }
    } catch (error) {
      console.error('❌ Errore nell\'invio del documento:', error);
      set({ error: 'Impossibile inviare il documento' });
      throw error;
    } finally {
      set({ loading: false });
    }
  },

  // Invia un messaggio audio al gruppo (IDENTICO AL CHATSTORE)
  sendAudioMessage: async (groupId, userId, audioUri) => {
    console.log('🎤 ChatStore: sendAudioMessage chiamato per chat:', groupId);
    const { user } = useAuthStore.getState();
    if (!user) {
      console.log('❌ ChatStore: Utente non autenticato');
      return;
    }

    try {
      set({ loading: true, error: null });

      // Ottieni il token di autenticazione
      const token = await AsyncStorage.getItem('@trendychat:token');
      if (!token) {
        throw new Error('Token di autenticazione non trovato');
      }

      console.log('📤 Caricando file audio sul server HP...');

      // Crea FormData per upload del file audio
      const formData = new FormData();
      formData.append('file', {
        uri: audioUri,
        name: `audio_${Date.now()}.aac`, // Cambiato estensione per coerenza
        type: 'audio/aac', // Cambiato da audio/m4a a audio/aac per compatibilità server
      });

      // Upload del file audio al server HP (con timeout esteso per audio lunghi)
      const uploadResponse = await axios.post(`${API_URL}/cloud/upload`, formData, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'multipart/form-data',
        },
        timeout: 300000, // 5 minuti per audio lunghi
      });

      console.log('📡 File audio caricato:', uploadResponse.status);
      console.log('📡 Risposta server upload:', uploadResponse.data);
      const audioUrl = uploadResponse.data.file.url;

      // CALCOLA DURATA REALE DELL'AUDIO
      let audioDuration = 0;
      try {
        console.log('⏱️ Calcolando durata audio...');
        const { sound } = await Audio.Sound.createAsync({ uri: audioUri });
        const status = await sound.getStatusAsync();
        if (status.isLoaded && status.durationMillis) {
          audioDuration = Math.round(status.durationMillis / 1000);
          console.log('✅ Durata audio calcolata:', audioDuration, 'secondi');
        }
        await sound.unloadAsync();
      } catch (error) {
        audioDuration = 0;
      }

      // Prepara i dati del messaggio audio
      const messageData = {
        text: '🎤 Messaggio vocale',
        type: 'audio',
        audioUrl: audioUrl,
        mediaUrl: audioUrl, // ← AGGIUNTO: per compatibilità con sistema di cancellazione
        duration: audioDuration,
        senderId: user.id || user.uid,
        senderName: user.displayName || user.name,
        senderAvatar: user.photoURL || user.avatar
      };

      console.log('📤 Inviando messaggio audio alla chat:', `${API_URL}/groups/${groupId}/messages`);

      // Invia il messaggio al server HP
      const response = await axios.post(`${API_URL}/groups/${groupId}/messages`, messageData, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      console.log('📡 Messaggio audio inviato con successo:', response.status);

      if (response.data && response.data.message) {
        // Aggiorna i messaggi localmente
        const newMessage = {
          id: response.data.message.id || response.data.message._id,
          text: '🎤 Messaggio vocale',
          type: 'audio',
          audioUrl: audioUrl,
          duration: audioDuration,
          user: {
            _id: user.id || user.uid,
            name: user.displayName || user.name,
            avatar: user.photoURL || user.avatar
          },
          createdAt: new Date(response.data.message.createdAt || response.data.message.timestamp || Date.now()),
          senderId: user.id || user.uid
        };

        // Aggiorna il gruppo corrente con il nuovo messaggio (IDENTICO ALLA CHAT UTENTI)
        const { groups, currentGroup } = get();
        const updatedGroups = groups.map(group =>
          group.id === groupId
            ? { ...group, messages: [newMessage, ...(group.messages || [])] }
            : group
        );

        const updatedCurrentGroup = currentGroup?.id === groupId
          ? { ...currentGroup, messages: [newMessage, ...(currentGroup.messages || [])] }
          : currentGroup;

        set({
          groups: updatedGroups,
          currentGroup: updatedCurrentGroup
        });

        console.log('✅ Messaggio audio aggiunto localmente alla chat');
      }
    } catch (error) {
      console.error('❌ Errore nell\'invio del messaggio audio gruppo:', error);
      set({ error: 'Impossibile inviare il messaggio audio' });
      throw error;
    } finally {
      set({ loading: false });
    }
  },

  // Aggiorna le informazioni del gruppo (SERVER HP)
  updateGroup: async (groupId, updates) => {
    console.log('🔄 GroupStore: updateGroup chiamato', { groupId, updates });
    const { user } = useAuthStore.getState();
    if (!user) {
      console.log('❌ GroupStore: Utente non autenticato');
      return;
    }

    try {
      set({ loading: true, error: null });

      // Ottieni il token di autenticazione
      const token = await AsyncStorage.getItem('@trendychat:token');
      if (!token) {
        throw new Error('Token di autenticazione non trovato');
      }

      console.log('📤 Aggiornando gruppo sul server HP...');

      // Aggiorna il gruppo sul server HP
      const response = await axios.put(`${API_URL}/groups/${groupId}`, updates, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      console.log('📡 Gruppo aggiornato sul server:', response.status);

      if (response.status === 200) {
        // Ricarica il gruppo dal server per avere i dati aggiornati
        await get().loadGroup(groupId);
        console.log('✅ Gruppo aggiornato e ricaricato dal server');
      }
    } catch (error) {
      console.error('❌ Errore nell\'aggiornamento del gruppo:', error);
      set({ error: 'Impossibile aggiornare il gruppo' });
      throw error;
    } finally {
      set({ loading: false });
    }
  },

  // Aggiungi membri al gruppo
  addMembers: async (groupId, newMembers) => {
    try {
      set({ loading: true, error: null });

      const groupRef = doc;
      const group = get().groups.find(g => g.id === groupId);

      await updateDoc(groupRef, {
        members: [...group.members, ...newMembers],
        updatedAt: serverTimestamp(),
      });
    } catch (error) {
      console.error('Errore nell\'aggiunta dei membri:', error);
      set({ error: 'Impossibile aggiungere i membri' });
      throw error;
    } finally {
      set({ loading: false });
    }
  },

  // Rimuovi membri dal gruppo
  removeMembers: async (groupId, membersToRemove) => {
    try {
      set({ loading: true, error: null });

      const groupRef = doc;
      const group = get().groups.find(g => g.id === groupId);

      await updateDoc(groupRef, {
        members: group.members.filter(m => !membersToRemove.includes(m)),
        updatedAt: serverTimestamp(),
      });
    } catch (error) {
      console.error('Errore nella rimozione dei membri:', error);
      set({ error: 'Impossibile rimuovere i membri' });
      throw error;
    } finally {
      set({ loading: false });
    }
  },

  // Promuovi un membro ad admin
  promoteToAdmin: async (groupId, memberId) => {
    try {
      set({ loading: true, error: null });

      const groupRef = doc;
      const group = get().groups.find(g => g.id === groupId);

      await updateDoc(groupRef, {
        admins: [...group.admins, memberId],
        updatedAt: serverTimestamp(),
      });
    } catch (error) {
      console.error('Errore nella promozione ad admin:', error);
      set({ error: 'Impossibile promuovere il membro ad admin' });
      throw error;
    } finally {
      set({ loading: false });
    }
  },

  // Rimuovi un admin
  removeAdmin: async (groupId, adminId) => {
    try {
      set({ loading: true, error: null });

      const groupRef = doc;
      const group = get().groups.find(g => g.id === groupId);

      await updateDoc(groupRef, {
        admins: group.admins.filter(a => a !== adminId),
        updatedAt: serverTimestamp(),
      });
    } catch (error) {
      console.error('Errore nella rimozione dell\'admin:', error);
      set({ error: 'Impossibile rimuovere l\'admin' });
      throw error;
    } finally {
      set({ loading: false });
    }
  },

  // Cancella messaggio per tutti (IDENTICO AL CHATSTORE)
  deleteMessageForEveryone: async (groupId, messageId) => {
    console.log('🗑️ TrendyChat: Cancellando messaggio per tutti:', messageId);
    const { user } = useAuthStore.getState();
    if (!user) {
      console.log('❌ TrendyChat: Utente non autenticato');
      return;
    }

    try {
      set({ loading: true, error: null });

      // Trova il messaggio da cancellare per vedere il tipo (IDENTICO AL CHATSTORE)
      const { currentGroup } = get();
      const currentMessages = currentGroup?.messages || [];
      const messageToDelete = currentMessages.find(msg => msg.id === messageId);

      if (messageToDelete) {
        console.log('📋 Tipo messaggio da cancellare:', messageToDelete.type);
        console.log('📋 Contenuto messaggio:', {
          text: messageToDelete.text,
          imageUrl: messageToDelete.imageUrl,
          videoUrl: messageToDelete.videoUrl,
          audioUrl: messageToDelete.audioUrl,
          documentUrl: messageToDelete.documentUrl
        });
      }

      // Ottieni il token di autenticazione (IDENTICO AL CHATSTORE)
      const token = await AsyncStorage.getItem('@trendychat:token');
      if (!token) {
        throw new Error('Token di autenticazione non trovato');
      }

      console.log('📤 Cancellando messaggio sul server HP...');

      // Cancella il messaggio sul server HP (IDENTICO AL CHATSTORE MA ENDPOINT GRUPPI)
      const response = await axios.delete(`${API_URL}/groups/${groupId}/messages/${messageId}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      console.log('📡 Messaggio cancellato dal server:', response.status);

      if (response.status === 200) {
        // Rimuovi il messaggio localmente (IDENTICO AL CHATSTORE)
        const updatedMessages = currentMessages.filter(msg => msg.id !== messageId);

        // Aggiorna i messaggi del gruppo corrente (IDENTICO AL CHATSTORE)
        set(state => ({
          currentGroup: state.currentGroup?.id === groupId
            ? { ...state.currentGroup, messages: updatedMessages }
            : state.currentGroup
        }));

        console.log('✅ Messaggio cancellato per tutti (tipo:', messageToDelete?.type || 'unknown', ')');
      }
    } catch (error) {
      console.error('❌ Errore nella cancellazione del messaggio:', error);
      set({ error: 'Impossibile cancellare il messaggio' });
      throw error;
    } finally {
      set({ loading: false });
    }
  },

  // Cancella messaggio solo per me (STILE TRENDYCHAT)
  deleteMessageForMe: async (groupId, messageId) => {
    console.log('👤 GroupChat: Nascondendo messaggio solo per me:', messageId);

    try {
      // Rimuovi il messaggio solo localmente (non dal server) - STESSO DEL CHATSTORE
      const { currentGroup } = get();
      const currentMessages = currentGroup?.messages || [];
      const updatedMessages = currentMessages.filter(msg => msg.id !== messageId);

      // Aggiorna solo il gruppo corrente
      set(state => ({
        currentGroup: state.currentGroup?.id === groupId
          ? { ...state.currentGroup, messages: updatedMessages }
          : state.currentGroup
      }));

      // Salva la lista dei messaggi nascosti in AsyncStorage
      const hiddenMessagesKey = `@trendychat:hidden_group_messages_${groupId}`;
      const existingHidden = await AsyncStorage.getItem(hiddenMessagesKey);
      const hiddenMessages = existingHidden ? JSON.parse(existingHidden) : [];

      if (!hiddenMessages.includes(messageId)) {
        hiddenMessages.push(messageId);
        await AsyncStorage.setItem(hiddenMessagesKey, JSON.stringify(hiddenMessages));
      }

      console.log('✅ Messaggio nascosto solo per me');
    } catch (error) {
      console.error('❌ Errore nel nascondere il messaggio:', error);
      set({ error: 'Impossibile nascondere il messaggio' });
      throw error;
    }
  },

  // Cancella gruppo completamente (gruppo + messaggi + media)
  deleteGroup: async (groupId) => {
    console.log('🗑️ GroupStore: Cancellando gruppo:', groupId);

    try {
      set({ loading: true, error: null });

      // Ottieni il token di autenticazione
      const token = await AsyncStorage.getItem('@trendychat:token');
      if (!token) {
        throw new Error('Token di autenticazione non trovato');
      }

      // ✅ PULIZIA MEDIA: Elimina tutti i media del gruppo prima di eliminare il gruppo
      try {
        await axios.delete(`${API_URL}/groups/${groupId}/cleanup-media`, {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });
      } catch (mediaError) {
        console.error('⚠️ Errore pulizia media gruppo:', mediaError);
        // Non bloccare l'eliminazione del gruppo se la pulizia fallisce
      }

      // Cancella il gruppo sul server HP (cancella anche messaggi e media)
      const response = await axios.delete(`${API_URL}/groups/${groupId}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      console.log('📡 Gruppo cancellato dal server:', response.status);

      if (response.status === 200) {
        // Rimuovi il gruppo dalla lista locale
        const { groups } = get();
        const updatedGroups = groups.filter(group => group.id !== groupId);

        // Aggiorna lo stato
        set({
          groups: updatedGroups,
          currentGroup: null // Reset gruppo corrente se era quello cancellato
        });

        console.log('✅ Gruppo cancellato completamente (gruppo + messaggi + media)');
        return true;
      }
    } catch (error) {
      console.error('❌ Errore nella cancellazione del gruppo:', error);
      set({ error: 'Impossibile cancellare il gruppo' });
      throw error;
    } finally {
      set({ loading: false });
    }
  },

  // Carica un gruppo specifico per ID
  loadGroup: async (groupId) => {
    const { user } = useAuthStore.getState();
    if (!user) {
      console.error('❌ Utente non autenticato');
      return;
    }

    try {
      set({ loading: true, error: null });

      // Ottieni il token di autenticazione
      const token = await AsyncStorage.getItem('@trendychat:token');

      // Richiesta al server HP ProLiant
      console.log('🔗 Caricando gruppo specifico:', `${API_URL}/groups/${groupId}`);
      const response = await axios.get(`${API_URL}/groups/${groupId}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      console.log('📡 Risposta server gruppo:', response.status);

      if (response.data && response.data.group) {
        const group = {
          id: response.data.group.id || response.data.group._id,
          name: response.data.group.name,
          description: response.data.group.description || '',
          photoURL: response.data.group.photoURL,
          members: response.data.group.members || [],
          admins: response.data.group.admins || [],
          // NON sovrascrivere i messaggi per evitare di perdere l'ultimo messaggio
          // messages: response.data.group.messages || [],
          createdAt: response.data.group.createdAt,
          lastMessage: response.data.group.lastMessage || null
        };

        // Aggiorna solo le info del gruppo, mantieni i messaggi esistenti
        set(state => ({
          currentGroup: {
            ...state.currentGroup,
            ...group,
            messages: state.currentGroup?.messages || [] // Mantieni i messaggi esistenti
          }
        }));
        console.log('✅ Gruppo caricato:', group.name);
      }
    } catch (error) {
      console.error('❌ Errore nel caricamento del gruppo:', error);
      set({ error: 'Impossibile caricare il gruppo' });
    } finally {
      set({ loading: false });
    }
  },

  // Imposta il gruppo corrente
  setCurrentGroup: (group) => {
    set({ currentGroup: group });
  },

  // Imposta errore
  setError: (error) => set({ error }),

  // Pulisci errore
  clearError: () => set({ error: null }),
}));

export default useGroupStore;

// Esporto le variabili vuote per mantenere la compatibilità
export const db = null;
export const storage = null;