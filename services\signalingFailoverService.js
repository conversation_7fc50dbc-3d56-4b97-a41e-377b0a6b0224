// 🔄 Signaling Failover Service per TrendyChat
// Gestisce la connessione automatica con failover tra Render e SuperMicron

import io from 'socket.io-client';
const webrtcConfig = require('../webrtc.config');

class SignalingFailoverService {
  constructor() {
    this.socket = null;
    this.currentServer = null;
    this.currentServerIndex = 0;
    this.isConnected = false;
    this.isConnecting = false;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 3;
    this.healthCheckInterval = null;
    this.connectionPromise = null;

    // Eventi personalizzati
    this.eventHandlers = {
      connect: [],
      disconnect: [],
      error: [],
      serverSwitch: []
    };

    console.log('🔄 SignalingFailoverService inizializzato');
  }

  // 🚀 Inizializza la connessione con failover
  async initialize() {
    if (this.isConnecting) {
      console.log('⏳ Connessione già in corso...');
      return this.connectionPromise;
    }

    this.isConnecting = true;
    console.log('🚀 Inizializzando connessione signaling con failover...');

    this.connectionPromise = this._attemptConnection();

    try {
      await this.connectionPromise;
      this._startHealthCheck();
      return true;
    } catch (error) {
      console.error('❌ Fallimento completo connessione signaling:', error);
      throw error;
    } finally {
      this.isConnecting = false;
    }
  }

  // 🔗 Tenta la connessione ai server in ordine di priorità
  async _attemptConnection() {
    const servers = webrtcConfig.signalingServers;

    for (let i = 0; i < servers.length; i++) {
      const server = servers[i];
      console.log(`🔗 Tentativo connessione a ${server.type}: ${server.url}`);

      try {
        await this._connectToServer(server, i);
        console.log(`✅ Connesso con successo a ${server.type}`);
        this._emitEvent('connect', { server, index: i });
        return true;
      } catch (error) {
        console.warn(`⚠️ Connessione fallita a ${server.type}:`, error.message);

        if (i < servers.length - 1) {
          console.log(`🔄 Tentativo server successivo in ${webrtcConfig.failover.fallbackDelay}ms...`);
          await this._delay(webrtcConfig.failover.fallbackDelay);
        }
      }
    }

    throw new Error('Tutti i server signaling non disponibili');
  }

  // 🌐 Connette a un server specifico
  async _connectToServer(server, index) {
    return new Promise((resolve, reject) => {
      // Disconnetti socket esistente
      if (this.socket) {
        this.socket.disconnect();
        this.socket = null;
      }

      // Crea nuovo socket
      this.socket = io(server.url, {
        transports: ['websocket'],
        timeout: server.timeout || webrtcConfig.failover.connectionTimeout,
        reconnection: false, // Gestiamo noi il reconnect
        forceNew: true
      });

      // Timer di timeout
      const timeoutTimer = setTimeout(() => {
        this.socket.disconnect();
        reject(new Error(`Timeout connessione a ${server.type}`));
      }, server.timeout || webrtcConfig.failover.connectionTimeout);

      // Eventi socket
      this.socket.on('connect', () => {
        clearTimeout(timeoutTimer);
        this.currentServer = server;
        this.currentServerIndex = index;
        this.isConnected = true;
        this.reconnectAttempts = 0;

        console.log(`🎯 Connesso a signaling ${server.type}: ${server.url}`);
        resolve(true);
      });

      this.socket.on('connect_error', (error) => {
        clearTimeout(timeoutTimer);
        console.error(`❌ Errore connessione ${server.type}:`, error);
        reject(error);
      });

      this.socket.on('disconnect', (reason) => {
        console.warn(`🔌 Disconnesso da ${server.type}:`, reason);
        this.isConnected = false;
        this._emitEvent('disconnect', { server, reason });

        // Riconnessione automatica se non intenzionale
        if (reason !== 'io client disconnect') {
          this._handleDisconnection();
        }
      });

      this.socket.on('error', (error) => {
        console.error(`🚨 Errore socket ${server.type}:`, error);
        this._emitEvent('error', { server, error });
      });
    });
  }

  // 🔄 Gestisce la disconnessione e riconnessione
  async _handleDisconnection() {
    if (this.isConnecting) return;

    console.log('🔄 Gestendo disconnessione...');
    this.reconnectAttempts++;

    if (this.reconnectAttempts <= this.maxReconnectAttempts) {
      console.log(`🔄 Tentativo riconnessione ${this.reconnectAttempts}/${this.maxReconnectAttempts}...`);

      await this._delay(webrtcConfig.failover.retryDelay);

      try {
        await this.initialize();
      } catch (error) {
        console.error('❌ Riconnessione fallita:', error);
      }
    } else {
      console.error('❌ Massimo numero di tentativi di riconnessione raggiunto');
      this._emitEvent('error', {
        error: new Error('Riconnessione fallita dopo tutti i tentativi')
      });
    }
  }

  // 💓 Avvia health check periodico
  _startHealthCheck() {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
    }

    this.healthCheckInterval = setInterval(() => {
      this._performHealthCheck();
    }, webrtcConfig.failover.healthCheckInterval);
  }

  // 🏥 Esegue health check
  _performHealthCheck() {
    if (!this.socket || !this.isConnected) {
      console.log('💓 Health check: Socket non connesso');
      return;
    }

    // Ping semplice per verificare la connessione
    const startTime = Date.now();

    this.socket.emit('ping', { timestamp: startTime });

    const timeout = setTimeout(() => {
      // Health check timeout silenzioso
    }, webrtcConfig.failover.healthCheckTimeout);

    this.socket.once('pong', (data) => {
      clearTimeout(timeout);
      const latency = Date.now() - startTime;
      console.log(`💓 Health check OK - Latenza: ${latency}ms`);
    });
  }

  // 📡 Ottieni il socket corrente
  getSocket() {
    return this.socket;
  }

  // 📊 Ottieni informazioni server corrente
  getCurrentServerInfo() {
    return {
      server: this.currentServer,
      index: this.currentServerIndex,
      isConnected: this.isConnected,
      reconnectAttempts: this.reconnectAttempts
    };
  }

  // 🎧 Aggiungi event listener
  on(event, handler) {
    if (this.eventHandlers[event]) {
      this.eventHandlers[event].push(handler);
    }
  }

  // 🔇 Rimuovi event listener
  off(event, handler) {
    if (this.eventHandlers[event]) {
      const index = this.eventHandlers[event].indexOf(handler);
      if (index > -1) {
        this.eventHandlers[event].splice(index, 1);
      }
    }
  }

  // 📢 Emetti evento
  _emitEvent(event, data) {
    if (this.eventHandlers[event]) {
      this.eventHandlers[event].forEach(handler => {
        try {
          handler(data);
        } catch (error) {
          console.error(`Errore in event handler ${event}:`, error);
        }
      });
    }
  }

  // ⏱️ Utility per delay
  _delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // 🛑 Disconnetti e pulisci
  disconnect() {
    console.log('🛑 Disconnessione signaling failover service...');

    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
      this.healthCheckInterval = null;
    }

    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }

    this.isConnected = false;
    this.currentServer = null;
    this.currentServerIndex = 0;
    this.reconnectAttempts = 0;
  }
}

// Esporta istanza singleton
export default new SignalingFailoverService();
