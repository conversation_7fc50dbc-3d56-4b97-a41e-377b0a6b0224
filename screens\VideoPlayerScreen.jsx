import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  SafeAreaView,
  StatusBar,
  Platform,
  Dimensions,
  ActivityIndicator,
  BackHandler,
} from 'react-native';
import { Video } from 'expo-av';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { useNavigation, useRoute } from '@react-navigation/native';
import useAuthStore from '../store/authStore';
import useTrendyCoinStore from '../store/trendyCoinStore';

const { width, height } = Dimensions.get('window');

const VideoPlayerScreen = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const { user } = useAuthStore();
  const { addEarnedCoins } = useTrendyCoinStore();

  const [status, setStatus] = useState({});
  const [loading, setLoading] = useState(true);
  const [watchedTime, setWatchedTime] = useState(0);
  const [totalTime, setTotalTime] = useState(0);
  const [canExit, setCanExit] = useState(false);
  const [earnedCoins, setEarnedCoins] = useState(0);
  const [showProgress, setShowProgress] = useState(true);

  // 🔧 REF per stato video aggiornato in tempo reale
  const statusRef = useRef({});

  const videoRef = useRef(null);
  const watchTimeRef = useRef(0);
  const intervalRef = useRef(null);

  // 🛡️ NUOVI STATI PER ANTI-CHEAT
  const [sessionId, setSessionId] = useState(`session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`);
  const [actualWatchTime, setActualWatchTime] = useState(0);
  const actualWatchTimeRef = useRef(0);

  // Video ads di esempio (in produzione verranno dal server)
  const videoAds = [
    {
      id: 1,
      url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4',
      title: 'Big Buck Bunny',
      duration: 596, // 9:56 minuti
      reward: 20, // TrendyCoin per questo video
    },
    {
      id: 2,
      url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ElephantsDream.mp4',
      title: 'Elephants Dream',
      duration: 653, // 10:53 minuti
      reward: 25,
    },
    {
      id: 3,
      url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerBlazes.mp4',
      title: 'For Bigger Blazes',
      duration: 15, // 15 secondi (per test)
      reward: 5,
    }
  ];

  const [currentVideo, setCurrentVideo] = useState(videoAds[0]);

  useEffect(() => {
    console.log('🎬 VideoPlayerScreen: Avvio player video ads');

    // Impedisci il back button durante la visione
    const backHandler = BackHandler.addEventListener('hardwareBackPress', handleBackPress);

    // Avvia il timer per il tempo guardato
    startWatchTimer();

    return () => {
      backHandler.remove();
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
      // Cleanup video quando il componente si smonta
      if (videoRef.current) {
        videoRef.current.unloadAsync().catch(() => {});
      }
    };
  }, []);

  const handleBackPress = () => {
    if (!canExit) {
      Alert.alert(
        '⚠️ Attenzione',
        'Devi guardare almeno 30 secondi per guadagnare TrendyCoin. Vuoi davvero uscire?',
        [
          { text: 'Continua a guardare', style: 'cancel' },
          { text: 'Esci senza ricompensa', onPress: () => navigation.goBack() },
        ]
      );
      return true;
    } else {
      // Può uscire, salva i TrendyCoin guadagnati
      handleEarlyExit();
      return true;
    }
  };

  const startWatchTimer = () => {
    console.log('⏱️ Avviando timer per nuovo video...');
    console.log(`🛡️ SessionId: ${sessionId}`);

    // Assicurati che non ci siano timer precedenti
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }

    intervalRef.current = setInterval(() => {
      // Incrementa solo se il video sta effettivamente riproducendo
      if (statusRef.current.isPlaying && !statusRef.current.isBuffering) {
        watchTimeRef.current += 1;
        actualWatchTimeRef.current += 1;
        setWatchedTime(watchTimeRef.current);
        setActualWatchTime(actualWatchTimeRef.current);

        // Calcola TrendyCoin guadagnati (0.1 coin ogni 30 secondi di visualizzazione effettiva)
        const coins = Math.round(Math.floor(actualWatchTimeRef.current / 30) * 0.1 * 10) / 10;
        setEarnedCoins(coins);

        // Dopo 30 secondi di visualizzazione effettiva può uscire
        if (actualWatchTimeRef.current >= 30) {
          setCanExit(true);
        }

        // Log ogni 30 secondi per debugging
        if (actualWatchTimeRef.current % 30 === 0) {
          console.log(`⏱️ Tempo effettivo: ${actualWatchTimeRef.current}s - Coins: ${Math.round(coins * 10) / 10}`);
        }
      } else {
        // Video in pausa o buffering - incrementa solo il tempo totale
        watchTimeRef.current += 1;
        setWatchedTime(watchTimeRef.current);
      }
    }, 1000);
  };

  const onPlaybackStatusUpdate = (playbackStatus) => {
    setStatus(playbackStatus);
    statusRef.current = playbackStatus; // 🔧 Sincronizza ref per timer
    const wasLoading = loading;
    setLoading(!playbackStatus.isLoaded);

    // Debug solo per errori importanti
    if (!playbackStatus.isLoaded && playbackStatus.error) {
      console.log(`❌ Video Error: ${playbackStatus.error}`);
    }

    if (playbackStatus.isLoaded) {
      setTotalTime(Math.floor(playbackStatus.durationMillis / 1000));

      // Se il video è appena stato caricato e non c'è un timer attivo, avvialo
      if (wasLoading && !intervalRef.current) {
        console.log('🎬 Video caricato, avvio timer...');
        startWatchTimer();
      }
    }
  };

  const handleVideoEnd = () => {
    console.log('🎬 Video terminato, assegnando ricompensa...');

    // Ferma il timer
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }

    // Usa il tempo effettivo di visualizzazione per calcolare i TrendyCoin (0.1 per 30s)
    const finalCoins = Math.floor(actualWatchTimeRef.current / 30) * 0.1;

    if (finalCoins > 0) {
      Alert.alert(
        '🎉 Complimenti!',
        `Hai guadagnato ${finalCoins} TrendyCoin!\n\nTempo effettivo guardato: ${Math.floor(actualWatchTimeRef.current / 60)}:${(actualWatchTimeRef.current % 60).toString().padStart(2, '0')}\n\n💰 I TrendyCoin saranno disponibili domenica dopo la liquidazione settimanale.`,
        [
          {
            text: 'Raccogli ricompensa',
            onPress: async () => {
              try {
                await saveTrendyCoins(finalCoins);
                navigation.goBack();
              } catch (error) {
                // Errore già gestito in saveTrendyCoins
                console.log('❌ Errore durante il salvataggio, utente rimane nella schermata');
              }
            }
          }
        ]
      );
    } else {
      Alert.alert(
        '⏱️ Tempo insufficiente',
        'Devi guardare almeno 30 secondi di video per guadagnare TrendyCoin.',
        [
          {
            text: 'OK',
            onPress: () => navigation.goBack()
          }
        ]
      );
    }
  };

  const handleCollectAndContinue = async () => {
    console.log('💰 Raccogliendo TrendyCoin e passando al prossimo video...');

    // SOLO 0.1 TrendyCoin per chiamata (non cumulativo)
    const finalCoins = 0.1;

    if (finalCoins > 0) {
      try {
        // Salva i TrendyCoin
        const result = await saveTrendyCoins(finalCoins);

        // Mostra notifica rapida con info aggiuntive
        let message = `+${finalCoins} TrendyCoin aggiunti!\n\n🎬 Prossimo video in arrivo...`;

        if (result && result.data && result.data.dailyEarningsLeft !== undefined) {
          message += `\n\n📊 Guadagni rimanenti oggi: ${result.data.dailyEarningsLeft}`;
        }

        Alert.alert(
          '🎉 TrendyCoin Raccolti!',
          message,
          [
            {
              text: 'Continua',
              onPress: () => {
                // Passa al prossimo video automaticamente
                handleNextVideo();
              }
            }
          ]
        );
      } catch (error) {
        // Errore già gestito in saveTrendyCoins
        console.log('❌ Errore durante il salvataggio, utente rimane nella schermata');
      }
    } else {
      // Nessun TrendyCoin, passa direttamente al prossimo
      handleNextVideo();
    }
  };

  const handleEarlyExit = () => {
    console.log('🚪 Uscita anticipata...');

    // Ferma il timer
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }

    const finalCoins = Math.floor(actualWatchTimeRef.current / 30) * 0.1;

    if (finalCoins > 0) {
      Alert.alert(
        '💰 Vuoi raccogliere i TrendyCoin?',
        `Hai guadagnato ${finalCoins} TrendyCoin!\n\nCosa vuoi fare?`,
        [
          {
            text: 'Esci senza raccogliere',
            style: 'destructive',
            onPress: () => navigation.goBack()
          },
          {
            text: 'Raccogli e esci',
            onPress: async () => {
              await saveTrendyCoins(finalCoins);
              navigation.goBack();
            }
          }
        ]
      );
    } else {
      navigation.goBack();
    }
  };

  const saveTrendyCoins = async (coins) => {
    try {
      console.log(`💰 Salvando ${coins} TrendyCoin nel saldo pendente...`);
      console.log(`🛡️ Tempo effettivo guardato: ${actualWatchTimeRef.current}s`);
      console.log(`🛡️ SessionId: ${sessionId}`);

      // Usa il tempo effettivo di visualizzazione e sessionId per anti-cheat
      const result = await addEarnedCoins(
        coins,
        'video_ads',
        actualWatchTimeRef.current,
        sessionId
      );

      console.log(`✅ ${coins} TrendyCoin aggiunti al saldo pendente`);

      // Mostra info aggiuntive se disponibili
      if (result && result.data && result.data.dailyEarningsLeft !== undefined) {
        console.log(`📊 Guadagni rimanenti oggi: ${result.data.dailyEarningsLeft}`);
      }

      return result;
    } catch (error) {
      console.error('❌ Errore salvataggio TrendyCoin:', error);

      // Mostra messaggio di errore specifico dal server
      const errorMessage = error.message || 'Impossibile salvare i TrendyCoin. Riprova più tardi.';
      Alert.alert('Errore', errorMessage);

      throw error;
    }
  };

  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const handleNextVideo = async () => {
    console.log('🎬 Passando al prossimo video...');

    // Ferma il timer corrente
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }

    // Ferma il video corrente
    if (videoRef.current) {
      await videoRef.current.pauseAsync();
      await videoRef.current.unloadAsync();
    }

    // Trova il prossimo video
    const currentIndex = videoAds.findIndex(v => v.id === currentVideo.id);
    const nextIndex = (currentIndex + 1) % videoAds.length;
    const nextVideo = videoAds[nextIndex];

    console.log(`🎬 Caricando video: ${nextVideo.title}`);

    // Reset tutto per il nuovo video (incluso tempo effettivo)
    watchTimeRef.current = 0;
    actualWatchTimeRef.current = 0;
    setWatchedTime(0);
    setActualWatchTime(0);
    setEarnedCoins(0);
    setCanExit(false);
    setLoading(true);

    // 🛡️ NUOVO SESSIONID PER OGNI VIDEO
    const newSessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    setSessionId(newSessionId);
    console.log(`🆔 Nuovo SessionId generato: ${newSessionId}`);

    // Imposta il nuovo video
    setCurrentVideo(nextVideo);

    // Riavvia il timer per il nuovo video
    setTimeout(() => {
      startWatchTimer();
    }, 1000); // Aspetta 1 secondo per il caricamento
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent={true} />

      {/* Header minimale */}
      <LinearGradient
        colors={['rgba(30, 136, 229, 0.9)', 'rgba(216, 27, 96, 0.9)']}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 0 }}
        style={styles.header}
      >
        <TouchableOpacity
          style={styles.backButton}
          onPress={handleBackPress}
        >
          <Ionicons name="close" size={24} color="#FFFFFF" />
        </TouchableOpacity>

        <View style={styles.headerInfo}>
          <Text style={styles.headerTitle}>Video Ads</Text>
          <Text style={styles.headerSubtitle}>{currentVideo.title}</Text>
        </View>

        <View style={styles.coinCounter}>
          <Ionicons name="wallet" size={20} color="#FFD700" />
          <Text style={styles.coinText}>{earnedCoins}</Text>
        </View>
      </LinearGradient>

      {/* Video Player */}
      <View style={styles.videoContainer}>
        {loading && (
          <View style={styles.loadingOverlay}>
            <ActivityIndicator size="large" color="#1E88E5" />
            <Text style={styles.loadingText}>Caricamento video...</Text>
          </View>
        )}

        <Video
          ref={videoRef}
          key={currentVideo.id} // Forza reload quando cambia video
          style={styles.video}
          source={{ uri: currentVideo.url }}
          useNativeControls={false}
          resizeMode="contain"
          isLooping={false}
          shouldPlay={!loading}
          onPlaybackStatusUpdate={(status) => {
            onPlaybackStatusUpdate(status);
            if (status.didJustFinish) {
              handleVideoEnd();
            }
          }}
        />
      </View>

      {/* Progress Bar e Info */}
      {showProgress && (
        <LinearGradient
          colors={['rgba(18, 18, 18, 0.9)', 'rgba(18, 18, 18, 0.7)']}
          style={styles.progressContainer}
        >
          <View style={styles.progressInfo}>
            <Text style={styles.progressText}>
              Tempo effettivo: {formatTime(actualWatchTime)}
            </Text>
            <Text style={styles.progressText}>
              TrendyCoin guadagnati: {earnedCoins}
            </Text>
          </View>

          <View style={styles.progressBar}>
            <View
              style={[
                styles.progressFill,
                { width: `${Math.min((actualWatchTime / 1800) * 100, 100)}%` }
              ]}
            />
          </View>

          <Text style={styles.progressLabel}>
            {actualWatchTime < 30 ?
              `${30 - actualWatchTime}s per guadagnare 0.1 TrendyCoin` :
              `Guadagni 0.1 TrendyCoin ogni 30s! 🎉`
            }
          </Text>

          {canExit && (
            <TouchableOpacity style={styles.mainButton} onPress={handleCollectAndContinue}>
              <Text style={styles.mainButtonText}>
                💰 Raccogli e Continua ({earnedCoins} TrendyCoin)
              </Text>
            </TouchableOpacity>
          )}
        </LinearGradient>
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000000',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    paddingTop: Platform.OS === 'ios' ? 50 : 30,
  },
  backButton: {
    padding: 8,
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerInfo: {
    flex: 1,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  headerSubtitle: {
    fontSize: 12,
    color: '#FFFFFF',
    opacity: 0.8,
  },
  coinCounter: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
  },
  coinText: {
    color: '#FFD700',
    fontWeight: 'bold',
    marginLeft: 4,
  },
  videoContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  video: {
    width: width,
    height: height * 0.6,
  },
  loadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    zIndex: 1000,
  },
  loadingText: {
    color: '#FFFFFF',
    marginTop: 10,
    fontSize: 16,
  },
  progressContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    padding: 20,
  },
  progressInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 10,
  },
  progressText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
  },
  progressBar: {
    height: 4,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    borderRadius: 2,
    marginBottom: 10,
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#FFD700',
    borderRadius: 2,
  },
  progressLabel: {
    color: '#FFFFFF',
    fontSize: 12,
    textAlign: 'center',
    marginBottom: 10,
  },
  mainButton: {
    backgroundColor: '#4CAF50',
    paddingVertical: 16,
    paddingHorizontal: 32,
    borderRadius: 30,
    alignSelf: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 5,
  },
  mainButtonText: {
    color: '#FFFFFF',
    fontWeight: 'bold',
    fontSize: 16,
    textAlign: 'center',
  },
});

export default VideoPlayerScreen;
