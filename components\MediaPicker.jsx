import React, { useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Image, ScrollView } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import * as ImagePicker from 'expo-image-picker';
import * as DocumentPicker from 'expo-document-picker';
import { useTheme } from '../hooks/useTheme';

const MediaPicker = ({ onMediaSelected, onClose }) => {
  const { theme } = useTheme();
  const [selectedMedia, setSelectedMedia] = useState(null);

  const pickImage = async () => {
    try {
      // Richiedi i permessi
      const permissionResult = await ImagePicker.requestMediaLibraryPermissionsAsync();

      if (!permissionResult.granted) {
        alert('È necessario concedere il permesso per accedere alla galleria.');
        return;
      }

      // Utilizziamo una stringa diretta per evitare problemi di compatibilità
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: 'all',
        allowsEditing: true,
        quality: 0.8,
        allowsMultipleSelection: true,
        selectionLimit: 10,
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        setSelectedMedia(result.assets);
        onMediaSelected(result.assets);
      }
    } catch (error) {
      console.error('Errore nella selezione dell\'immagine:', error);
    }
  };

  const pickDocument = async () => {
    try {
      const result = await DocumentPicker.getDocumentAsync({
        type: '*/*',
        multiple: true,
        copyToCacheDirectory: true,
      });

      if (result.type === 'success') {
        setSelectedMedia(result);
        onMediaSelected(result);
      }
    } catch (error) {
      console.error('Errore nella selezione del documento:', error);
    }
  };

  const takePhoto = async () => {
    try {
      const { status } = await ImagePicker.requestCameraPermissionsAsync();
      if (status !== 'granted') {
        alert('È necessario il permesso di accesso alla fotocamera');
        return;
      }

      const result = await ImagePicker.launchCameraAsync({
        allowsEditing: true,
        quality: 0.8,
      });

      if (!result.canceled) {
        setSelectedMedia(result.assets[0]);
        onMediaSelected(result.assets[0]);
      }
    } catch (error) {
      console.error('Errore nella cattura della foto:', error);
    }
  };

  const renderMediaPreview = () => {
    if (!selectedMedia) return null;

    if (Array.isArray(selectedMedia)) {
      return (
        <ScrollView horizontal style={styles.previewContainer}>
          {selectedMedia.map((media, index) => (
            <Image
              key={index}
              source={{ uri: media.uri }}
              style={styles.previewImage}
            />
          ))}
        </ScrollView>
      );
    }

    return (
      <Image
        source={{ uri: selectedMedia.uri }}
        style={styles.previewImage}
      />
    );
  };

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <View style={styles.header}>
        <Text style={[styles.title, { color: theme.colors.text }]}>
          Aggiungi media
        </Text>
        <TouchableOpacity onPress={onClose}>
          <Ionicons name="close" size={24} color={theme.colors.text} />
        </TouchableOpacity>
      </View>

      <View style={styles.optionsContainer}>
        <TouchableOpacity
          style={[styles.option, { backgroundColor: theme.colors.primary }]}
          onPress={takePhoto}
        >
          <Ionicons name="camera" size={24} color="white" />
          <Text style={styles.optionText}>Foto</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.option, { backgroundColor: theme.colors.primary }]}
          onPress={pickImage}
        >
          <Ionicons name="images" size={24} color="white" />
          <Text style={styles.optionText}>Galleria</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.option, { backgroundColor: theme.colors.primary }]}
          onPress={pickDocument}
        >
          <Ionicons name="document" size={24} color="white" />
          <Text style={styles.optionText}>Documento</Text>
        </TouchableOpacity>
      </View>

      {renderMediaPreview()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    height: 400,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 24,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
  },
  optionsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 24,
  },
  option: {
    width: 80,
    height: 80,
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
  },
  optionText: {
    color: 'white',
    marginTop: 4,
    fontSize: 12,
  },
  previewContainer: {
    flexDirection: 'row',
    marginTop: 16,
  },
  previewImage: {
    width: 100,
    height: 100,
    borderRadius: 8,
    marginRight: 8,
  },
});

export default MediaPicker;