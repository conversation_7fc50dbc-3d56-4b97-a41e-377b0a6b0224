import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  FlatList,
  KeyboardAvoidingView,
  Platform,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import useChatStore from '../store/useChatStore';
import useAuthStore from '../store/authStore';

const ChatBotScreen = () => {
  const [message, setMessage] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const flatListRef = useRef(null);
  const { user } = useAuthStore();
  const { sendMessage, messages, loading } = useChatStore();

  useEffect(() => {
    if (messages.length > 0) {
      flatListRef.current?.scrollToEnd();
    }
  }, [messages]);

  const handleSend = async () => {
    if (!message.trim()) return;

    const userMessage = {
      id: Date.now().toString(),
      text: message,
      sender: 'user',
      timestamp: new Date(),
    };

    setMessage('');
    setIsTyping(true);

    try {
      // Simulazione risposta del bot
      setTimeout(async () => {
        const botResponse = {
          id: (Date.now() + 1).toString(),
          text: getBotResponse(message),
          sender: 'bot',
          timestamp: new Date(),
        };

        await sendMessage(botResponse);
        setIsTyping(false);
      }, 1000);
    } catch (error) {
      setIsTyping(false);
    }
  };

  const getBotResponse = (userMessage) => {
    const lowerMessage = userMessage.toLowerCase();

    if (lowerMessage.includes('ciao') || lowerMessage.includes('salve')) {
      return `Ciao ${user?.displayName || 'utente'}! Come posso aiutarti oggi?`;
    }

    if (lowerMessage.includes('aiuto') || lowerMessage.includes('help')) {
      return 'Posso aiutarti con:\n- Informazioni sul tuo account\n- Gestione delle chat\n- Impostazioni dell\'app\n- Problemi tecnici';
    }

    if (lowerMessage.includes('account')) {
      return 'Per gestire il tuo account puoi:\n1. Modificare il profilo\n2. Cambiare la foto\n3. Aggiornare le impostazioni di privacy';
    }

    if (lowerMessage.includes('chat')) {
      return 'Per le chat puoi:\n1. Iniziare una nuova conversazione\n2. Creare un gruppo\n3. Gestire le notifiche';
    }

    if (lowerMessage.includes('grazie')) {
      return 'Di nulla! Sono qui per aiutarti se hai altre domande.';
    }

    return 'Mi dispiace, non ho capito. Puoi riformulare la domanda o chiedere "aiuto" per vedere le opzioni disponibili.';
  };

  const renderMessage = ({ item }) => (
    <View style={[
      styles.messageContainer,
      item.sender === 'user' ? styles.userMessage : styles.botMessage
    ]}>
      <Text style={styles.messageText}>{item.text}</Text>
      <Text style={styles.timestamp}>
        {new Date(item.timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
      </Text>
    </View>
  );

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={styles.container}
    >
      <FlatList
        ref={flatListRef}
        data={messages}
        renderItem={renderMessage}
        keyExtractor={item => item.id}
        contentContainerStyle={styles.messagesList}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Text style={styles.emptyText}>
              Ciao! Sono il tuo assistente virtuale. Come posso aiutarti?
            </Text>
          </View>
        }
      />

      {isTyping && (
        <View style={styles.typingContainer}>
          <Text style={styles.typingText}>Il bot sta scrivendo...</Text>
          <ActivityIndicator size="small" color="#128C7E" style={styles.typingIndicator} />
        </View>
      )}

      <View style={styles.inputContainer}>
        <TextInput
          style={styles.input}
          placeholder="Scrivi un messaggio..."
          value={message}
          onChangeText={setMessage}
          multiline
        />
        <TouchableOpacity
          style={[styles.sendButton, !message.trim() && styles.sendButtonDisabled]}
          onPress={handleSend}
          disabled={!message.trim() || loading}
        >
          <Ionicons
            name="send"
            size={24}
            color={message.trim() ? '#FFFFFF' : '#666666'}
          />
        </TouchableOpacity>
      </View>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  messagesList: {
    padding: 15,
  },
  messageContainer: {
    maxWidth: '80%',
    padding: 10,
    borderRadius: 10,
    marginBottom: 10,
  },
  userMessage: {
    alignSelf: 'flex-end',
    backgroundColor: '#DCF8C6',
  },
  botMessage: {
    alignSelf: 'flex-start',
    backgroundColor: '#F5F5F5',
  },
  messageText: {
    fontSize: 16,
    color: '#000000',
  },
  timestamp: {
    fontSize: 12,
    color: '#666666',
    alignSelf: 'flex-end',
    marginTop: 5,
  },
  inputContainer: {
    flexDirection: 'row',
    padding: 10,
    borderTopWidth: 1,
    borderTopColor: '#F5F5F5',
  },
  input: {
    flex: 1,
    backgroundColor: '#F5F5F5',
    borderRadius: 20,
    paddingHorizontal: 15,
    paddingVertical: 10,
    marginRight: 10,
    fontSize: 16,
    maxHeight: 100,
  },
  sendButton: {
    backgroundColor: '#128C7E',
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
  },
  sendButtonDisabled: {
    backgroundColor: '#F5F5F5',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 30,
  },
  emptyText: {
    fontSize: 16,
    color: '#666666',
    textAlign: 'center',
  },
  typingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 10,
  },
  typingText: {
    fontSize: 14,
    color: '#666666',
    marginRight: 10,
  },
  typingIndicator: {
    marginLeft: 5,
  },
});

export default ChatBotScreen;