import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Image,
  ActivityIndicator,
  Alert,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import useGroupStore from '../store/groupStore';
import useMediaStore from '../store/mediaStore';

const GroupsScreen = () => {
  const navigation = useNavigation();
  const { groups, loading, error, loadGroups, createGroup } = useGroupStore();
  const { pickImage, loading: mediaLoading } = useMediaStore();
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    console.log('🔄 GroupsScreen: Inizializzazione, caricamento gruppi...');
    loadGroups();
  }, []);

  const handleCreateGroup = async () => {
    try {
      setIsLoading(true);
      const imageUri = await pickImage();
      if (imageUri) {
        await createGroup('Nuovo Gruppo', imageUri);
        navigation.navigate('GroupChat', { groupId: groups[0].id });
      }
    } catch (error) {
      Alert.alert('Errore', 'Impossibile creare il gruppo');
    } finally {
      setIsLoading(false);
    }
  };

  const renderGroupItem = ({ item }) => (
    <TouchableOpacity
      style={styles.groupItem}
      onPress={() => navigation.navigate('GroupChat', { groupId: item.id })}
    >
      <Image
        source={{ uri: item.photoURL || 'https://via.placeholder.com/50' }}
        style={styles.groupImage}
      />
      <View style={styles.groupInfo}>
        <Text style={styles.groupName}>{item.name}</Text>
        <Text style={styles.groupMembers}>
          {item.members.length} membri
        </Text>
      </View>
    </TouchableOpacity>
  );

  if (loading || isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#128C7E" />
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <FlatList
        data={groups}
        renderItem={renderGroupItem}
        keyExtractor={(item) => item.id}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Text style={styles.emptyText}>Nessun gruppo disponibile</Text>
          </View>
        }
      />
      <TouchableOpacity
        style={styles.createButton}
        onPress={handleCreateGroup}
      >
        <Text style={styles.createButtonText}>Crea Nuovo Gruppo</Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#fff',
  },
  groupItem: {
    flexDirection: 'row',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
    alignItems: 'center',
  },
  groupImage: {
    width: 50,
    height: 50,
    borderRadius: 25,
  },
  groupInfo: {
    marginLeft: 16,
    flex: 1,
  },
  groupName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  groupMembers: {
    fontSize: 14,
    color: '#666',
    marginTop: 4,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
  createButton: {
    backgroundColor: '#128C7E',
    padding: 16,
    margin: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  createButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default GroupsScreen;