import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  TouchableWithoutFeedback,
  Dimensions,
  Platform,
  ScrollView
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';

const { width, height } = Dimensions.get('window');

const StatusMenuModal = ({
  visible,
  onClose,
  myStatus,
  onDeleteMyStory,
  onOpenPrivacy,
  onOpenSettings,
  onOpenNotifications
}) => {
  if (!visible) return null;

  const menuCategories = [
    {
      id: 'story-actions',
      title: 'La mia storia',
      items: myStatus ? [
        {
          id: 'delete-story',
          icon: 'trash-outline',
          label: 'Elimina la mia storia',
          description: 'Rimuovi la tua storia attuale',
          colors: ['#F44336', '#D32F2F'],
          onPress: () => {
            onClose();
            onDeleteMyStory();
          }
        }
      ] : []
    },
    {
      id: 'story-settings',
      title: 'Impostazioni',
      items: [
        {
          id: 'privacy',
          icon: 'shield-outline',
          label: 'Privacy storie',
          description: 'Gestisci chi può vedere le tue storie',
          colors: ['#1E88E5', '#D81B60'],
          onPress: () => {
            onClose();
            onOpenPrivacy && onOpenPrivacy();
          }
        },
        {
          id: 'settings',
          icon: 'settings-outline',
          label: 'Impostazioni storie',
          description: 'Configura durata e qualità',
          colors: ['#1E88E5', '#D81B60'],
          onPress: () => {
            onClose();
            onOpenSettings && onOpenSettings();
          }
        },
        {
          id: 'notifications',
          icon: 'notifications-outline',
          label: 'Notifiche storie',
          description: 'Gestisci le notifiche delle storie',
          colors: ['#1E88E5', '#D81B60'],
          onPress: () => {
            onClose();
            onOpenNotifications && onOpenNotifications();
          }
        }
      ]
    }
  ];

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={onClose}
      statusBarTranslucent
    >
      <TouchableWithoutFeedback onPress={onClose}>
        <View style={styles.modalContainer}>
          <TouchableWithoutFeedback>
            <View style={styles.modalContent}>
              <LinearGradient
                colors={['#1E88E5', '#D81B60']}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 0 }}
                style={styles.header}
              >
                <Text style={styles.headerTitle}>Opzioni storie</Text>
                <TouchableOpacity style={styles.closeButton} onPress={onClose}>
                  <Ionicons name="close" size={24} color="#FFFFFF" />
                </TouchableOpacity>
              </LinearGradient>

              <ScrollView style={styles.scrollContainer}>
                {menuCategories.map((category) => (
                  category.items.length > 0 && (
                    <View key={category.id} style={styles.categoryContainer}>
                      <Text style={styles.categoryTitle}>{category.title}</Text>

                      {category.items.map((item) => (
                        <TouchableOpacity
                          key={item.id}
                          style={styles.optionItem}
                          onPress={item.onPress}
                        >
                          <View style={styles.optionIconContainer}>
                            <LinearGradient
                              colors={item.colors || ['#1E88E5', '#D81B60']}
                              start={{ x: 0, y: 0 }}
                              end={{ x: 1, y: 0 }}
                              style={styles.optionIconGradient}
                            >
                              <Ionicons name={item.icon} size={18} color="#FFFFFF" />
                            </LinearGradient>
                          </View>
                          <View style={styles.optionTextContainer}>
                            <Text style={styles.optionLabel}>{item.label}</Text>
                            <Text style={styles.optionDescription}>{item.description}</Text>
                          </View>
                        </TouchableOpacity>
                      ))}
                    </View>
                  )
                ))}
              </ScrollView>
            </View>
          </TouchableWithoutFeedback>
        </View>
      </TouchableWithoutFeedback>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
  },
  modalContent: {
    width: width * 0.85,
    maxHeight: height * 0.8,
    backgroundColor: '#1E1E1E',
    borderRadius: 20,
    overflow: 'hidden',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 16,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  closeButton: {
    padding: 8,
  },
  scrollContainer: {
    maxHeight: height * 0.7,
  },
  categoryContainer: {
    marginBottom: 16,
    paddingHorizontal: 16,
  },
  categoryTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#AAAAAA',
    marginBottom: 8,
    textTransform: 'uppercase',
  },
  optionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#2A2A2A',
  },
  optionIconContainer: {
    marginRight: 16,
  },
  optionIconGradient: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  optionTextContainer: {
    flex: 1,
  },
  optionLabel: {
    fontSize: 16,
    color: '#FFFFFF',
    marginBottom: 2,
  },
  optionDescription: {
    fontSize: 12,
    color: '#AAAAAA',
  },
});

export default StatusMenuModal;
