import * as Notifications from 'expo-notifications';
import Constants from 'expo-constants';
import { Platform } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Configura le notifiche
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: true,
  }),
});

// Versione semplificata che non dipende da Firebase
export const registerForPushNotificationsAsync = async (userId) => {
  // Simuliamo un token per evitare errori
  console.log('Simulazione registrazione notifiche push per userId:', userId);

  // Restituisci un token simulato
  const mockToken = 'expo-push-token-' + Date.now();

  // Salva il token in AsyncStorage invece che in Firebase
  try {
    await AsyncStorage.setItem('push_token', mockToken);
    console.log('Token push simulato salvato in AsyncStorage:', mockToken);
  } catch (error) {
    console.error('Errore nel salvataggio del token push in AsyncStorage:', error);
  }

  return mockToken;
};

export const sendPushNotification = async (expoPushToken, title, body, data = {}) => {
  console.log('Simulazione invio notifica push:', { expoPushToken, title, body, data });

  // In un'implementazione reale, qui invieresti la notifica
  // Per ora, simuliamo solo il comportamento
  return true;
};

export const handleNotificationResponse = (response, navigation) => {
  console.log('Gestione risposta notifica:', response);

  try {
    const data = response.notification.request.content.data;

    if (data && data.type === 'chat_message' && data.chatId) {
      console.log('Navigazione alla chat:', data.chatId);
      navigation.navigate('ChatRoom', {
        chatId: data.chatId,
      });
    }
  } catch (error) {
    console.error('Errore nella gestione della risposta alla notifica:', error);
  }
};

// 🔧 FUNZIONE MANCANTE: setupBackgroundHandler
export const setupBackgroundHandler = () => {
  console.log('📱 Setup background handler per notifiche');

  // Simulazione setup background handler
  // In un'implementazione reale, qui configureresti la gestione delle notifiche in background

  return () => {
    console.log('📱 Cleanup background handler');
  };
};

// 🔧 FUNZIONE MANCANTE: setupNotificationChannel
export const setupNotificationChannel = () => {
  console.log('📱 Setup notification channel');

  // Simulazione setup notification channel
  // In un'implementazione reale, qui configureresti i canali di notifica per Android

  return true;
};

// 🔧 FUNZIONE MANCANTE: getIncomingCalls
export const getIncomingCalls = (callback) => {
  console.log('📱 Get incoming calls');

  // Simulazione get incoming calls
  // In un'implementazione reale, qui otterresti le chiamate in arrivo

  // Restituisce un array vuoto per ora
  const mockCalls = [];
  if (callback) {
    callback(mockCalls);
  }

  // Restituisce una funzione di cleanup
  return () => {
    console.log('📱 Cleanup incoming calls listener');
  };
};

// 🔧 FUNZIONE MANCANTE: setupForegroundHandler
export const setupForegroundHandler = (callback) => {
  console.log('📱 Setup foreground handler per notifiche');

  // Simulazione setup foreground handler
  // In un'implementazione reale, qui configureresti la gestione delle notifiche in foreground

  // Restituisce una funzione di cleanup
  return () => {
    console.log('📱 Cleanup foreground handler');
  };
};

export default {
  registerForPushNotificationsAsync,
  sendPushNotification,
  handleNotificationResponse,
  setupBackgroundHandler,
  setupNotificationChannel,
  getIncomingCalls,
  setupForegroundHandler,
};

// Esporto le variabili vuote per mantenere la compatibilità
export const db = null;
