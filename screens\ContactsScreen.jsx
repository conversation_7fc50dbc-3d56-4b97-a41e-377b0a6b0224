import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  TextInput,
  Image,
  ActivityIndicator,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import useContactStore from '../store/contactStore';
import useChatStore from '../store/useChatStore';
import useWebRTCStore from '../store/webrtcStore';

const ContactsScreen = ({ navigation }) => {
  const [searchQuery, setSearchQuery] = useState('');
  const { contacts, loading, error, loadContacts } = useContactStore();
  const { createChat } = useChatStore();
  const { startCall } = useWebRTCStore();

  useEffect(() => {
    loadContacts();
  }, []);

  const handleStartChat = async (contact) => {
    try {
      const chatId = await createChat(contact.id);
      navigation.navigate('Chat', {
        chatId,
        contact: contact  // Passa i dati del contatto alla ChatScreen
      });
    } catch (error) {
      Alert.alert('Errore', 'Impossibile avviare la chat');
    }
  };

  const handleStartCall = async (contact, isVideoCall = true) => {
    try {
      await startCall(contact.id, contact.name, isVideoCall);
    } catch (error) {
      Alert.alert('Errore', 'Impossibile avviare la chiamata');
    }
  };

  const filteredContacts = contacts.filter(contact =>
    contact.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    contact.email.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const renderContact = ({ item }) => (
    <TouchableOpacity
      style={styles.contactItem}
      onPress={() => handleStartChat(item)}
    >
      <View style={styles.contactInfo}>
        <Image
          source={item.photoURL ? { uri: item.photoURL } : require('../assets/default-avatar.png')}
          style={styles.avatar}
        />
        <View style={styles.contactDetails}>
          <Text style={styles.contactName}>{item.name}</Text>
          <Text style={styles.contactEmail}>{item.email}</Text>
        </View>
      </View>
      <View style={styles.contactActions}>
        <View style={[styles.statusDot, { backgroundColor: item.online ? '#4CAF50' : '#9E9E9E' }]} />
        <TouchableOpacity
          style={styles.callButton}
          onPress={() => handleStartCall(item, false)}
        >
          <Ionicons name="call" size={22} color="#128C7E" />
        </TouchableOpacity>
        <TouchableOpacity
          style={styles.callButton}
          onPress={() => handleStartCall(item, true)}
        >
          <Ionicons name="videocam" size={22} color="#128C7E" />
        </TouchableOpacity>
        <Ionicons name="chevron-forward" size={24} color="#128C7E" />
      </View>
    </TouchableOpacity>
  );

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#128C7E" />
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>Errore nel caricamento dei contatti</Text>
        <TouchableOpacity style={styles.retryButton} onPress={loadContacts}>
          <Text style={styles.retryButtonText}>Riprova</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.searchContainer}>
        <Ionicons name="search" size={24} color="#128C7E" style={styles.searchIcon} />
        <TextInput
          style={styles.searchInput}
          placeholder="Cerca contatti..."
          value={searchQuery}
          onChangeText={setSearchQuery}
        />
        {searchQuery.length > 0 && (
          <TouchableOpacity onPress={() => setSearchQuery('')}>
            <Ionicons name="close-circle" size={24} color="#128C7E" />
          </TouchableOpacity>
        )}
      </View>

      <FlatList
        data={filteredContacts}
        renderItem={renderContact}
        keyExtractor={item => item.id}
        contentContainerStyle={styles.listContainer}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Text style={styles.emptyText}>Nessun contatto trovato</Text>
          </View>
        }
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F5F5F5',
    margin: 15,
    paddingHorizontal: 15,
    borderRadius: 10,
    height: 50,
  },
  searchIcon: {
    marginRight: 10,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
  },
  listContainer: {
    paddingHorizontal: 15,
  },
  contactItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F5F5F5',
  },
  contactInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  avatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    marginRight: 15,
  },
  contactDetails: {
    flex: 1,
  },
  contactName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#000000',
    marginBottom: 4,
  },
  contactEmail: {
    fontSize: 14,
    color: '#666666',
  },
  contactActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusDot: {
    width: 10,
    height: 10,
    borderRadius: 5,
    marginRight: 10,
  },
  callButton: {
    padding: 8,
    marginHorizontal: 4,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 16,
    color: '#FF0000',
    marginBottom: 20,
    textAlign: 'center',
  },
  retryButton: {
    backgroundColor: '#128C7E',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 5,
  },
  retryButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 30,
  },
  emptyText: {
    fontSize: 16,
    color: '#666666',
  },
});

export default ContactsScreen;