module.exports = function(api) {
  api.cache(true);
  return {
    presets: ['babel-preset-expo'],
    plugins: [
      'react-native-reanimated/plugin',
      // Aggiungiamo altri plugin utili
      '@babel/plugin-proposal-export-namespace-from',
      [
        'module-resolver',
        {
          root: ['./'],
          extensions: ['.ios.js', '.android.js', '.js', '.ts', '.tsx', '.json'],
          alias: {
            '@': './',
            '@components': './components',
            '@screens': './screens',
            '@navigation': './navigation',
            '@store': './store',
            '@utils': './utils',
            '@assets': './assets',
            '@services': './services',
            // Sostituisci expo-permissions con un modulo vuoto
            'expo-permissions': './empty-module.js',
          },
        },
      ],
    ],
    // Rimuoviamo la configurazione env per evitare problemi con react-native-paper
    // env: {
    //   production: {
    //     plugins: ['react-native-paper/babel'],
    //   },
    // },
  };
};