// 🧪 Test WebRTC Failover per TrendyChat
// Testa la connessione ai server signaling con failover automatico

import callRouter from '../services/callRouter';
import signalingFailoverService from '../services/signalingFailoverService';

class WebRTCTest {
  constructor() {
    this.testResults = [];
  }

  // 🧪 Esegui tutti i test
  async runAllTests(userId = 'test_user') {
    console.log('🧪 Avviando test WebRTC Failover...');
    
    this.testResults = [];
    
    try {
      // Test 1: Connessione signaling failover
      await this.testSignalingFailover();
      
      // Test 2: Inizializzazione call router
      await this.testCallRouterInitialization(userId);
      
      // Test 3: Test server info
      await this.testServerInfo();
      
      // Test 4: Test disconnessione e riconnessione
      await this.testReconnection();
      
      console.log('✅ Tutti i test completati');
      return this.getTestSummary();
      
    } catch (error) {
      console.error('❌ Errore durante i test:', error);
      this.addTestResult('Test Suite', false, error.message);
      return this.getTestSummary();
    }
  }

  // 🔗 Test connessione signaling failover
  async testSignalingFailover() {
    try {
      console.log('🔗 Test: Connessione signaling failover...');
      
      const startTime = Date.now();
      await signalingFailoverService.initialize();
      const connectionTime = Date.now() - startTime;
      
      const serverInfo = signalingFailoverService.getCurrentServerInfo();
      
      if (serverInfo.isConnected) {
        this.addTestResult(
          'Signaling Failover', 
          true, 
          `Connesso a ${serverInfo.server.type} in ${connectionTime}ms`
        );
      } else {
        this.addTestResult('Signaling Failover', false, 'Connessione fallita');
      }
      
    } catch (error) {
      this.addTestResult('Signaling Failover', false, error.message);
    }
  }

  // 📞 Test inizializzazione call router
  async testCallRouterInitialization(userId) {
    try {
      console.log('📞 Test: Inizializzazione call router...');
      
      const startTime = Date.now();
      await callRouter.initialize(userId);
      const initTime = Date.now() - startTime;
      
      const serverInfo = callRouter.getServerInfo();
      
      if (serverInfo.isInitialized) {
        this.addTestResult(
          'Call Router Init', 
          true, 
          `Inizializzato in ${initTime}ms`
        );
      } else {
        this.addTestResult('Call Router Init', false, 'Inizializzazione fallita');
      }
      
    } catch (error) {
      this.addTestResult('Call Router Init', false, error.message);
    }
  }

  // 📊 Test informazioni server
  async testServerInfo() {
    try {
      console.log('📊 Test: Informazioni server...');
      
      const serverInfo = callRouter.getServerInfo();
      
      const tests = [
        {
          name: 'Signaling Server Info',
          condition: serverInfo.signaling && serverInfo.signaling.server,
          message: serverInfo.signaling ? 
            `${serverInfo.signaling.server.type}: ${serverInfo.signaling.server.url}` : 
            'Info non disponibili'
        },
        {
          name: 'Signaling Connected',
          condition: serverInfo.signaling && serverInfo.signaling.isConnected,
          message: serverInfo.signaling ? 
            `Connesso: ${serverInfo.signaling.isConnected}` : 
            'Stato non disponibile'
        }
      ];
      
      tests.forEach(test => {
        this.addTestResult(test.name, test.condition, test.message);
      });
      
    } catch (error) {
      this.addTestResult('Server Info', false, error.message);
    }
  }

  // 🔄 Test disconnessione e riconnessione
  async testReconnection() {
    try {
      console.log('🔄 Test: Disconnessione e riconnessione...');
      
      // Disconnetti
      signalingFailoverService.disconnect();
      await this.delay(1000);
      
      const disconnectedInfo = signalingFailoverService.getCurrentServerInfo();
      const isDisconnected = !disconnectedInfo.isConnected;
      
      this.addTestResult(
        'Disconnessione', 
        isDisconnected, 
        isDisconnected ? 'Disconnesso correttamente' : 'Disconnessione fallita'
      );
      
      // Riconnetti
      const startTime = Date.now();
      await signalingFailoverService.initialize();
      const reconnectionTime = Date.now() - startTime;
      
      const reconnectedInfo = signalingFailoverService.getCurrentServerInfo();
      const isReconnected = reconnectedInfo.isConnected;
      
      this.addTestResult(
        'Riconnessione', 
        isReconnected, 
        isReconnected ? 
          `Riconnesso a ${reconnectedInfo.server.type} in ${reconnectionTime}ms` : 
          'Riconnessione fallita'
      );
      
    } catch (error) {
      this.addTestResult('Reconnection Test', false, error.message);
    }
  }

  // 🧪 Test specifico per server Render
  async testRenderConnection() {
    try {
      console.log('🌐 Test: Connessione diretta a Render...');
      
      // Forza connessione a Render (primo server nella lista)
      const renderServer = {
        url: 'wss://trendychat-signaling.onrender.com',
        type: 'render',
        timeout: 5000
      };
      
      const startTime = Date.now();
      await signalingFailoverService._connectToServer(renderServer, 0);
      const connectionTime = Date.now() - startTime;
      
      this.addTestResult(
        'Render Direct', 
        true, 
        `Connesso in ${connectionTime}ms`
      );
      
    } catch (error) {
      this.addTestResult('Render Direct', false, error.message);
    }
  }

  // 🏠 Test specifico per server SuperMicron
  async testSupermicronConnection() {
    try {
      console.log('🏠 Test: Connessione diretta a SuperMicron...');
      
      // Forza connessione a SuperMicron (secondo server nella lista)
      const supermicronServer = {
        url: 'wss://signaling.trendychat.it',
        type: 'supermicron',
        timeout: 5000
      };
      
      const startTime = Date.now();
      await signalingFailoverService._connectToServer(supermicronServer, 1);
      const connectionTime = Date.now() - startTime;
      
      this.addTestResult(
        'SuperMicron Direct', 
        true, 
        `Connesso in ${connectionTime}ms`
      );
      
    } catch (error) {
      this.addTestResult('SuperMicron Direct', false, error.message);
    }
  }

  // 📝 Aggiungi risultato test
  addTestResult(testName, success, message) {
    this.testResults.push({
      name: testName,
      success,
      message,
      timestamp: new Date().toISOString()
    });
    
    const status = success ? '✅' : '❌';
    console.log(`${status} ${testName}: ${message}`);
  }

  // 📊 Ottieni riassunto test
  getTestSummary() {
    const total = this.testResults.length;
    const passed = this.testResults.filter(r => r.success).length;
    const failed = total - passed;
    
    return {
      total,
      passed,
      failed,
      successRate: total > 0 ? (passed / total * 100).toFixed(1) : 0,
      results: this.testResults
    };
  }

  // ⏱️ Utility delay
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // 🧹 Cleanup dopo i test
  cleanup() {
    try {
      callRouter.disconnect();
      console.log('🧹 Cleanup completato');
    } catch (error) {
      console.error('❌ Errore cleanup:', error);
    }
  }
}

export default new WebRTCTest();
