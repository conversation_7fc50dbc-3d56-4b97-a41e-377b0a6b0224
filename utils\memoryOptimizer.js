// Utility per ottimizzare l'uso della memoria
import { InteractionManager } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as FileSystem from 'expo-file-system';
import { PERFORMANCE_CONFIG } from '../config/constants';

// Costanti per la gestione della memoria
const MEMORY_CHECK_INTERVAL = 60000; // 1 minuto
const LAST_CLEANUP_KEY = '@trendychat:last_memory_cleanup';
const MEMORY_USAGE_LOG_KEY = '@trendychat:memory_usage_log';

// Variabili per tenere traccia dell'uso della memoria
let memoryCheckInterval = null;
let isCleanupRunning = false;

/**
 * Inizializza il monitoraggio della memoria
 */
export const initMemoryMonitoring = () => {
  if (memoryCheckInterval) {
    clearInterval(memoryCheckInterval);
  }

  // Esegui un controllo iniziale
  checkMemoryUsage();

  // Imposta un intervallo per il controllo periodico
  memoryCheckInterval = setInterval(() => {
    checkMemoryUsage();
  }, MEMORY_CHECK_INTERVAL);

  return () => {
    if (memoryCheckInterval) {
      clearInterval(memoryCheckInterval);
      memoryCheckInterval = null;
    }
  };
};

/**
 * Controlla l'uso della memoria e avvia la pulizia se necessario
 */
const checkMemoryUsage = async () => {
  try {
    // Verifica se è già in corso una pulizia
    if (isCleanupRunning) {
      return;
    }

    // Verifica quando è stata eseguita l'ultima pulizia
    const lastCleanupStr = await AsyncStorage.getItem(LAST_CLEANUP_KEY);
    const now = Date.now();
    
    if (lastCleanupStr) {
      const lastCleanup = parseInt(lastCleanupStr, 10);
      // Se l'ultima pulizia è stata eseguita meno di 30 minuti fa, salta
      if (now - lastCleanup < 30 * 60 * 1000) {
        return;
      }
    }

    // Esegui la pulizia in background
    InteractionManager.runAfterInteractions(() => {
      performMemoryCleanup();
    });
  } catch (error) {
    console.error('Errore nel controllo dell\'uso della memoria:', error);
  }
};

/**
 * Esegue la pulizia della memoria
 */
const performMemoryCleanup = async () => {
  try {
    isCleanupRunning = true;
    console.log('Avvio pulizia della memoria...');

    // 1. Pulisci la cache delle immagini vecchie
    await cleanupImageCache();

    // 2. Pulisci i dati temporanei
    await cleanupTempData();

    // 3. Pulisci i log vecchi
    await cleanupOldLogs();

    // Aggiorna il timestamp dell'ultima pulizia
    await AsyncStorage.setItem(LAST_CLEANUP_KEY, Date.now().toString());

    console.log('Pulizia della memoria completata');
  } catch (error) {
    console.error('Errore durante la pulizia della memoria:', error);
  } finally {
    isCleanupRunning = false;
  }
};

/**
 * Pulisce la cache delle immagini vecchie
 */
const cleanupImageCache = async () => {
  try {
    // Ottieni tutte le chiavi dallo storage
    const keys = await AsyncStorage.getAllKeys();

    // Filtra le chiavi relative alle immagini
    const imageKeys = keys.filter(key =>
      key.startsWith('image_cache_') ||
      key.startsWith('thumbnail_cache_')
    );

    if (imageKeys.length === 0) {
      return;
    }

    console.log(`Trovate ${imageKeys.length} immagini in cache`);

    // Ottieni i dati delle immagini in batch
    const now = Date.now();
    const keysToRemove = [];
    const batchSize = 20;
    
    for (let i = 0; i < imageKeys.length; i += batchSize) {
      const batch = imageKeys.slice(i, i + batchSize);
      const items = await AsyncStorage.multiGet(batch);
      
      for (const [key, value] of items) {
        if (!value) {
          keysToRemove.push(key);
          continue;
        }
        
        try {
          const data = JSON.parse(value);
          // Rimuovi le immagini più vecchie di 7 giorni
          if (data.timestamp && (now - data.timestamp > 7 * 24 * 60 * 60 * 1000)) {
            keysToRemove.push(key);
            
            // Se c'è un URI locale, prova a eliminare anche il file
            if (data.uri && data.uri.startsWith('file://')) {
              try {
                await FileSystem.deleteAsync(data.uri, { idempotent: true });
              } catch (e) {
                // Ignora errori nella cancellazione dei file
              }
            }
          }
        } catch (e) {
          // Se non è possibile analizzare i dati, rimuovi la chiave
          keysToRemove.push(key);
        }
      }
    }

    // Rimuovi le chiavi in batch
    if (keysToRemove.length > 0) {
      console.log(`Rimozione di ${keysToRemove.length} immagini dalla cache`);
      
      const removeBatchSize = 50;
      for (let i = 0; i < keysToRemove.length; i += removeBatchSize) {
        const batch = keysToRemove.slice(i, i + removeBatchSize);
        await AsyncStorage.multiRemove(batch);
      }
    }
  } catch (error) {
    console.error('Errore durante la pulizia della cache delle immagini:', error);
  }
};

/**
 * Pulisce i dati temporanei
 */
const cleanupTempData = async () => {
  try {
    // Pulisci la directory temporanea di FileSystem
    if (FileSystem.cacheDirectory) {
      const tempDir = `${FileSystem.cacheDirectory}temp/`;
      
      try {
        const info = await FileSystem.getInfoAsync(tempDir);
        if (info.exists) {
          const contents = await FileSystem.readDirectoryAsync(tempDir);
          
          // Rimuovi i file più vecchi di 1 giorno
          const now = Date.now();
          for (const file of contents) {
            try {
              const fileInfo = await FileSystem.getInfoAsync(`${tempDir}${file}`);
              if (fileInfo.modificationTime && (now - fileInfo.modificationTime > 24 * 60 * 60 * 1000)) {
                await FileSystem.deleteAsync(`${tempDir}${file}`, { idempotent: true });
              }
            } catch (e) {
              // Ignora errori nella cancellazione dei file
            }
          }
        }
      } catch (e) {
        console.warn('Errore durante la pulizia della directory temporanea:', e);
      }
    }
  } catch (error) {
    console.error('Errore durante la pulizia dei dati temporanei:', error);
  }
};

/**
 * Pulisce i log vecchi
 */
const cleanupOldLogs = async () => {
  try {
    // Pulisci i log di utilizzo della memoria
    const logsStr = await AsyncStorage.getItem(MEMORY_USAGE_LOG_KEY);
    
    if (logsStr) {
      try {
        const logs = JSON.parse(logsStr);
        const now = Date.now();
        
        // Mantieni solo i log degli ultimi 7 giorni
        const filteredLogs = logs.filter(log => 
          log.timestamp && (now - log.timestamp < 7 * 24 * 60 * 60 * 1000)
        );
        
        // Se ci sono troppi log, mantieni solo gli ultimi 100
        const trimmedLogs = filteredLogs.length > 100 
          ? filteredLogs.slice(filteredLogs.length - 100) 
          : filteredLogs;
        
        await AsyncStorage.setItem(MEMORY_USAGE_LOG_KEY, JSON.stringify(trimmedLogs));
      } catch (e) {
        // Se c'è un errore nel parsing, resetta i log
        await AsyncStorage.removeItem(MEMORY_USAGE_LOG_KEY);
      }
    }
  } catch (error) {
    console.error('Errore durante la pulizia dei log vecchi:', error);
  }
};

/**
 * Forza una pulizia immediata della memoria
 */
export const forceMemoryCleanup = async () => {
  if (isCleanupRunning) {
    console.log('Pulizia della memoria già in corso');
    return false;
  }
  
  try {
    await performMemoryCleanup();
    return true;
  } catch (error) {
    console.error('Errore durante la pulizia forzata della memoria:', error);
    return false;
  }
};

export default {
  initMemoryMonitoring,
  forceMemoryCleanup
};
