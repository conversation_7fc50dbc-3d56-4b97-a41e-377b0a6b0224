import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  RefreshControl
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import useBroadcastStore from '../store/useBroadcastStore';

const BroadcastScreen = ({ navigation }) => {
  const {
    broadcasts,
    loading,
    error,
    loadBroadcasts,
    deleteBroadcast,
    clearError
  } = useBroadcastStore();

  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    loadBroadcasts();
  }, []);

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadBroadcasts();
    setRefreshing(false);
  };

  const handleDeleteBroadcast = (broadcastId, broadcastName) => {
    Alert.alert(
      'Elimina Lista Broadcast',
      `Sei sicuro di voler eliminare "${broadcastName}"?`,
      [
        { text: 'Annulla', style: 'cancel' },
        {
          text: 'Elimina',
          style: 'destructive',
          onPress: async () => {
            try {
              await deleteBroadcast(broadcastId);
              Alert.alert('Successo', 'Lista broadcast eliminata');
            } catch (error) {
              Alert.alert('Errore', 'Impossibile eliminare la lista broadcast');
            }
          }
        }
      ]
    );
  };

  const renderBroadcastItem = ({ item }) => (
    <TouchableOpacity
      style={styles.broadcastCard}
      onPress={() => {
        // TODO: Navigare alla chat broadcast
        Alert.alert('Info', `Aprendo broadcast: ${item.name}`);
      }}
    >
      <LinearGradient
        colors={['#1E88E5', '#D81B60']}
        style={styles.broadcastIcon}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        <Ionicons name="radio" size={24} color="white" />
      </LinearGradient>

      <View style={styles.broadcastInfo}>
        <Text style={styles.broadcastName}>{item.name}</Text>
        <Text style={styles.broadcastDescription}>
          {item.description || 'Nessuna descrizione'}
        </Text>
        <View style={styles.broadcastStats}>
          <Text style={styles.statsText}>
            {item.stats?.totalRecipients || 0} destinatari
          </Text>
          <Text style={styles.statsText}>•</Text>
          <Text style={styles.statsText}>
            {item.stats?.totalMessages || 0} messaggi
          </Text>
        </View>
      </View>

      <TouchableOpacity
        style={styles.deleteButton}
        onPress={() => handleDeleteBroadcast(item.id, item.name)}
      >
        <Ionicons name="trash-outline" size={20} color="#FF6B6B" />
      </TouchableOpacity>
    </TouchableOpacity>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <LinearGradient
        colors={['#1E88E5', '#D81B60']}
        style={styles.emptyIcon}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        <Ionicons name="radio-outline" size={48} color="white" />
      </LinearGradient>
      <Text style={styles.emptyTitle}>Nessuna Lista Broadcast</Text>
      <Text style={styles.emptySubtitle}>
        Crea la tua prima lista broadcast per inviare messaggi a più contatti contemporaneamente
      </Text>
      <TouchableOpacity
        style={styles.createButton}
        onPress={() => navigation.navigate('CreateBroadcastScreen')}
      >
        <LinearGradient
          colors={['#1E88E5', '#D81B60']}
          style={styles.createButtonGradient}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
        >
          <Ionicons name="add" size={24} color="white" />
          <Text style={styles.createButtonText}>Crea Lista Broadcast</Text>
        </LinearGradient>
      </TouchableOpacity>
    </View>
  );

  if (loading && broadcasts.length === 0) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#1E88E5" />
        <Text style={styles.loadingText}>Caricando liste broadcast...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Header TrendyChat con Gradiente */}
      <LinearGradient
        colors={['#1E88E5', '#D81B60']}
        style={styles.header}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color="white" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Liste Broadcast</Text>
        <TouchableOpacity
          style={styles.addButton}
          onPress={() => navigation.navigate('CreateBroadcastScreen')}
        >
          <Ionicons name="add" size={24} color="white" />
        </TouchableOpacity>
      </LinearGradient>

      {/* Lista Broadcast */}
      <FlatList
        data={broadcasts}
        renderItem={renderBroadcastItem}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.listContainer}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={['#1E88E5']}
            tintColor="#1E88E5"
          />
        }
        ListEmptyComponent={renderEmptyState}
        showsVerticalScrollIndicator={false}
      />

      {/* Error Message */}
      {error && (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{error}</Text>
          <TouchableOpacity onPress={clearError}>
            <Text style={styles.errorDismiss}>Chiudi</Text>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#1E1E1E'
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
    paddingTop: 50, // Spazio per status bar
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 5
  },
  backButton: {
    padding: 8
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: 'white',
    flex: 1,
    textAlign: 'center'
  },
  addButton: {
    padding: 8
  },
  listContainer: {
    padding: 16,
    flexGrow: 1
  },
  broadcastCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#2A2A2A',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 5
  },
  broadcastIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16
  },
  broadcastInfo: {
    flex: 1
  },
  broadcastName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: 4
  },
  broadcastDescription: {
    fontSize: 14,
    color: '#CCCCCC',
    marginBottom: 8
  },
  broadcastStats: {
    flexDirection: 'row',
    alignItems: 'center'
  },
  statsText: {
    fontSize: 12,
    color: '#999999',
    marginRight: 8
  },
  deleteButton: {
    padding: 8
  },
  emptyContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 32
  },
  emptyIcon: {
    width: 80,
    height: 80,
    borderRadius: 40,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 24
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: 12,
    textAlign: 'center'
  },
  emptySubtitle: {
    fontSize: 14,
    color: '#CCCCCC',
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: 32
  },
  createButton: {
    borderRadius: 25,
    overflow: 'hidden'
  },
  createButtonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingVertical: 12
  },
  createButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 8
  },
  loadingContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#1E1E1E'
  },
  loadingText: {
    color: 'white',
    fontSize: 16,
    marginTop: 16
  },
  errorContainer: {
    backgroundColor: '#FF6B6B',
    padding: 16,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between'
  },
  errorText: {
    color: 'white',
    fontSize: 14,
    flex: 1
  },
  errorDismiss: {
    color: 'white',
    fontSize: 14,
    fontWeight: 'bold'
  }
});

export default BroadcastScreen;
