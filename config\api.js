// Configurazione API dell'applicazione

import { APP_CONSTANTS } from './constants';

// Indirizzo IP del server HP ProLiant
export const SERVER_IP = '************';
export const API_URL = `http://${SERVER_IP}:3001/api`;

// Log per debug
console.log('API URL:', API_URL);

const API_CONFIG = {
  // Configurazione base
  base: {
    url: API_URL,
    timeout: APP_CONSTANTS.api.timeout.request,
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    },
  },

  // Configurazione autenticazione
  auth: {
    endpoints: {
      login: '/auth/login',
      register: '/auth/register',
      logout: '/auth/logout',
      refresh: '/auth/refresh',
      verify: '/auth/verify',
      reset: '/auth/reset',
    },
    headers: {
      'Authorization': 'Bearer',
    },
  },

  // Configurazione utenti
  users: {
    endpoints: {
      profile: '/users/profile',
      update: '/users/update',
      delete: '/users/delete',
      search: '/users/search',
      contacts: '/users/contacts',
      block: '/users/block',
      unblock: '/users/unblock',
      findByPhone: '/users/find-by-phone',
      getByPhone: '/users/phone/:phoneNumber',
    },
  },

  // Configurazione chat
  chat: {
    endpoints: {
      list: '/chat/list',
      create: '/chat/create',
      delete: '/chat/delete',
      messages: '/chat/messages',
      send: '/chat/send',
      read: '/chat/read',
      typing: '/chat/typing',
      media: '/chat/media',
      files: '/chat/files',
      links: '/chat/links',
    },
  },

  // Configurazione gruppi
  groups: {
    endpoints: {
      list: '/groups/list',
      create: '/groups/create',
      update: '/groups/update',
      delete: '/groups/delete',
      join: '/groups/join',
      leave: '/groups/leave',
      members: '/groups/members',
      add: '/groups/add',
      remove: '/groups/remove',
      promote: '/groups/promote',
      demote: '/groups/demote',
    },
  },

  // Configurazione chiamate
  calls: {
    endpoints: {
      start: '/calls/start',
      end: '/calls/end',
      accept: '/calls/accept',
      reject: '/calls/reject',
      history: '/calls/history',
      status: '/calls/status',
    },
  },

  // Configurazione stati
  status: {
    endpoints: {
      create: '/status/create',
      update: '/status/update',
      delete: '/status/delete',
      list: '/status/list',
      view: '/status/view',
      reply: '/status/reply',
    },
  },

  // Configurazione notifiche
  notifications: {
    endpoints: {
      list: '/notifications/list',
      read: '/notifications/read',
      delete: '/notifications/delete',
      settings: '/notifications/settings',
      token: '/notifications/token',
    },
  },

  // Configurazione impostazioni
  settings: {
    endpoints: {
      get: '/settings/get',
      update: '/settings/update',
      privacy: '/settings/privacy',
      security: '/settings/security',
      theme: '/settings/theme',
      language: '/settings/language',
    },
  },

  // Configurazione media
  media: {
    endpoints: {
      upload: '/cloud/upload',
      download: '/media/download',
      delete: '/media/delete',
      compress: '/media/compress',
      convert: '/media/convert',
    },
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  },

  // Configurazione analytics
  analytics: {
    endpoints: {
      track: '/analytics/track',
      events: '/analytics/events',
      metrics: '/analytics/metrics',
      errors: '/analytics/errors',
    },
  },

  // Configurazione community
  communities: {
    endpoints: {
      list: '/communities',
      get: '/communities/:id',
      create: '/communities',
      update: '/communities/:id',
      delete: '/communities/:id',
      members: '/communities/:id/members',
      addMember: '/communities/:id/members',
      removeMember: '/communities/:id/members/:userId',
      admins: '/communities/:id/admins',
      addAdmin: '/communities/:id/admins',
      removeAdmin: '/communities/:id/admins/:userId',
      groups: '/communities/:id/groups',
      addGroup: '/communities/:id/groups',
      removeGroup: '/communities/:id/groups/:groupId',
      uploadImage: '/communities/upload-image',
    },
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  },

  // Configurazione gruppi
  groups: {
    endpoints: {
      list: '/groups',
      get: '/groups/:id',
      create: '/groups',
      update: '/groups/:id',
      delete: '/groups/:id',
      members: '/groups/:id/members',
      addMember: '/groups/:id/members',
      removeMember: '/groups/:id/members/:userId',
      admins: '/groups/:id/admins',
      addAdmin: '/groups/:id/admins',
      removeAdmin: '/groups/:id/admins/:userId',
      messages: '/groups/:id/messages',
      sendMessage: '/groups/:id/messages',
    },
  },

  // Configurazione storie
  stories: {
    endpoints: {
      list: '/stories',
      get: '/stories/:id',
      create: '/stories',
      delete: '/stories/:id',
      userStories: '/stories/user/:userId',
    },
  },
};

// Helper per ottenere un endpoint
export const getEndpoint = (path) => {
  const keys = path.split('.');
  let endpoint = API_CONFIG;

  for (const key of keys) {
    if (!endpoint[key]) return null;
    endpoint = endpoint[key];
  }

  return endpoint;
};

// Helper per verificare se un endpoint esiste
export const hasEndpoint = (path) => {
  const keys = path.split('.');
  let endpoint = API_CONFIG;

  for (const key of keys) {
    if (!endpoint[key]) return false;
    endpoint = endpoint[key];
  }

  return true;
};

// Helper per ottenere tutti gli endpoint di un percorso
export const getEndpoints = (path) => {
  const keys = path.split('.');
  let endpoint = API_CONFIG;

  for (const key of keys) {
    if (!endpoint[key]) return null;
    endpoint = endpoint[key];
  }

  return endpoint;
};

export default API_CONFIG;