import AsyncStorage from '@react-native-async-storage/async-storage';
import * as FileSystem from 'expo-file-system';
import { Platform } from 'react-native';

// URL del server cloud
const CLOUD_API_URL = 'http://************:3001/api/cloud';
console.log('CLOUD_API_URL:', CLOUD_API_URL);

/**
 * Servizio per il caricamento e la gestione dei file sul cloud
 */
class CloudService {
  constructor() {
    this.token = null;
  }

  /**
   * Inizializza il servizio
   */
  async initialize() {
    try {
      // Recupera il token da AsyncStorage
      // Prima prova con la chiave standard
      this.token = await AsyncStorage.getItem('auth_token');

      // Se non trova il token, prova con la chiave utilizzata in authService.js
      if (!this.token) {
        this.token = await AsyncStorage.getItem('@trendychat:token');
        console.log('Token recuperato da @trendychat:token:', this.token ? 'Sì' : 'No');
      } else {
        console.log('Token recuperato da auth_token:', this.token ? 'Sì' : 'No');
      }
    } catch (error) {
      console.error('Errore durante l\'inizializzazione del servizio cloud:', error);
    }
  }

  /**
   * Imposta il token di autenticazione
   * @param {string} token - Token JWT
   */
  setToken(token) {
    this.token = token;
  }

  /**
   * Carica un file sul server cloud
   * @param {string} fileUri - URI del file da caricare
   * @param {string} userId - ID dell'utente
   * @param {string} type - Tipo di file (profile, chat, ecc.)
   * @param {boolean} isPublic - Se il file è pubblico
   * @param {Object} metadata - Metadati aggiuntivi
   * @returns {Promise<Object>} - Risultato dell'operazione
   */
  async uploadFile(fileUri, userId, type = 'general', isPublic = false, metadata = {}) {
    try {
      // Se non abbiamo un token, proviamo a inizializzare
      if (!this.token) {
        await this.initialize();
      }

      console.log(`Caricamento file sul cloud: ${fileUri}`);
      console.log(`Tipo: ${type}, UserId: ${userId}, isPublic: ${isPublic}`);

      // Ottieni informazioni sul file
      const fileInfo = await FileSystem.getInfoAsync(fileUri);
      if (!fileInfo.exists) {
        throw new Error('File non trovato');
      }

      // Estrai il nome del file dall'URI
      const fileName = fileUri.split('/').pop();

      // Determina il tipo MIME in base all'estensione
      const mimeType = this._getMimeType(fileName);

      // Assicurati che il tipo sia incluso nei metadati per il middleware di autenticazione
      if (!metadata.type) {
        metadata.type = type;
      }

      // Aggiungi userId ai metadati
      if (!metadata.userId) {
        metadata.userId = userId;
      }

      // Aggiungi la data di scadenza (7 giorni da oggi)
      if (!metadata.expirationDate) {
        const expirationDate = new Date();
        expirationDate.setDate(expirationDate.getDate() + 7); // 7 giorni
        metadata.expirationDate = expirationDate.toISOString();
      }

      // Prepara gli headers
      const headers = {
        'Content-Type': 'multipart/form-data',
      };

      // Aggiungi il token di autorizzazione se disponibile
      if (this.token) {
        headers['Authorization'] = `Bearer ${this.token}`;
        console.log('Token di autenticazione aggiunto agli headers');
      } else {
        console.warn('Token di autenticazione mancante, l\'upload potrebbe fallire');
      }

      // Parametri aggiuntivi per il caricamento
      const params = {
        userId: userId,
        type: type,
        isPublic: isPublic.toString(),
        metadata: JSON.stringify(metadata)
      };

      console.log(`Dimensione file: ${this.formatSize(fileInfo.size)}`);

      // Utilizziamo sempre l'upload sincrono per evitare problemi di compatibilità
      return this.uploadFileSynchronously(fileUri, userId, type, isPublic, metadata, headers, fileInfo, fileName, mimeType);
    } catch (error) {
      console.error('Errore durante il caricamento del file:', error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Carica un file in modo sincrono
   * @private
   */
  async uploadFileSynchronously(fileUri, userId, type, isPublic, metadata, headers, fileInfo, fileName, mimeType) {
    try {
      console.log('Utilizzo upload sincrono');

      // Crea un FormData per il caricamento multipart
      const formData = new FormData();

      // Aggiungi il file al FormData
      formData.append('file', {
        uri: fileUri,
        name: fileName,
        type: mimeType,
      });

      // Aggiungi i metadati di base
      formData.append('userId', userId);
      formData.append('type', type);
      formData.append('isPublic', isPublic.toString());

      // Aggiungi i metadati aggiuntivi
      if (metadata) {
        formData.append('metadata', JSON.stringify(metadata));
      }

      console.log('FormData preparato per il caricamento');

      // Effettua la richiesta al server
      console.log(`Preparazione richiesta con token: ${this.token ? this.token.substring(0, 10) : 'nessun token'}...`);

      // Utilizza l'endpoint standard per tutti i tipi di file
      const uploadUrl = `${CLOUD_API_URL}/upload`;
      console.log('Utilizzo endpoint per upload:', uploadUrl);

      // Timeout più lungo per il caricamento (30 secondi)
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 secondi

      try {
        console.log(`Tentativo di caricamento...`);

        const response = await fetch(uploadUrl, {
          method: 'POST',
          headers: headers,
          body: formData,
          signal: controller.signal
        });

        // Pulisci il timeout
        clearTimeout(timeoutId);

        // Prova a parsare la risposta come JSON
        const responseText = await response.text();
        console.log('Risposta dal server (testo):', responseText);

        let data;
        try {
          data = JSON.parse(responseText);
          console.log('Risposta dal server cloud (JSON):', data);
        } catch (parseError) {
          console.log('Errore nel parsing della risposta JSON:', parseError.message);
          throw new Error('Errore nel parsing della risposta JSON');
        }

        if (!response.ok) {
          console.warn('Risposta non OK dal server:', response.status, data);
          throw new Error(data.message || 'Errore durante il caricamento del file');
        }

        // Aggiungi l'URI locale al risultato
        if (data.file) {
          data.file.localUri = fileUri;

          // Log dettagliato dell'URL dell'immagine
          console.log('DEBUG - URL immagine caricata:', {
            url: data.file.url,
            path: data.file.path,
            filename: data.file.filename
          });
        }

        return {
          success: true,
          file: data.file
        };
      } catch (error) {
        // Pulisci il timeout in caso di errore
        clearTimeout(timeoutId);

        // Se l'errore è dovuto al timeout, mostra un messaggio specifico
        if (error.name === 'AbortError') {
          console.log('La richiesta è scaduta');
          throw new Error('Timeout della richiesta');
        }

        // Rilancia l'errore
        throw error;
      }
    } catch (fetchError) {
      console.log('Errore nella richiesta fetch, utilizzo URL locale:', fetchError.message);

      // In caso di errore, restituisci comunque un risultato di successo con l'URI locale
      return {
        success: true,
        file: {
          id: `local_${Date.now()}`,
          url: fileUri, // Usa l'URI locale come URL
          localUri: fileUri,
          name: fileName,
          type: mimeType,
          size: fileInfo.size,
          userId: userId,
          isPublic: isPublic,
          metadata: metadata
        }
      };
    }
  }

  /**
   * Formatta la dimensione in bytes in una stringa leggibile
   * @param {number} bytes - Dimensione in bytes
   * @returns {string} Dimensione formattata
   */
  formatSize(bytes) {
    if (bytes < 1024) return bytes + ' B';
    if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(2) + ' KB';
    if (bytes < 1024 * 1024 * 1024) return (bytes / (1024 * 1024)).toFixed(2) + ' MB';
    return (bytes / (1024 * 1024 * 1024)).toFixed(2) + ' GB';
  }

  /**
   * Ottiene un file dal server cloud
   * @param {string} fileId - ID del file
   * @returns {Promise<Object>} - Risultato dell'operazione
   */
  async getFile(fileId) {
    try {
      if (!this.token) {
        await this.initialize();
        if (!this.token) {
          throw new Error('Token di autenticazione mancante');
        }
      }

      // Utilizza l'URL standard per tutti i tipi di file
      const apiUrl = CLOUD_API_URL;

      const response = await fetch(`${apiUrl}/${fileId}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${this.token}`,
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Errore durante il recupero del file');
      }

      return {
        success: true,
        url: response.url,
      };
    } catch (error) {
      console.error('Errore durante il recupero del file:', error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Estrae l'ID del file dall'URL
   * @param {string} url - URL del file
   * @returns {string|null} - ID del file o null se non trovato
   */
  extractFileIdFromUrl(url) {
    if (!url) return null;

    try {
      // L'URL è nel formato http://************:3001/api/files/FILENAME
      const parts = url.split('/');
      return parts[parts.length - 1];
    } catch (error) {
      console.error('Errore nell\'estrazione dell\'ID del file dall\'URL:', error);
      return null;
    }
  }

  /**
   * Elimina un file dal server cloud
   * @param {string} fileId - ID del file
   * @returns {Promise<Object>} - Risultato dell'operazione
   */
  async deleteFile(fileId) {
    try {
      if (!this.token) {
        await this.initialize();
        if (!this.token) {
          throw new Error('Token di autenticazione mancante');
        }
      }

      // Utilizza l'URL standard per tutti i tipi di file
      const apiUrl = CLOUD_API_URL;

      const response = await fetch(`${apiUrl}/${fileId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${this.token}`,
        },
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Errore durante l\'eliminazione del file');
      }

      return {
        success: true,
      };
    } catch (error) {
      console.error('Errore durante l\'eliminazione del file:', error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Elimina un file dal server cloud usando l'URL
   * @param {string} url - URL del file
   * @returns {Promise<Object>} - Risultato dell'operazione
   */
  async deleteFileByUrl(url) {
    const fileId = this.extractFileIdFromUrl(url);
    if (!fileId) {
      return {
        success: false,
        error: 'URL non valido'
      };
    }

    return this.deleteFile(fileId);
  }

  /**
   * Determina il tipo MIME in base all'estensione del file
   * @param {string} fileName - Nome del file
   * @returns {string} - Tipo MIME
   * @private
   */
  _getMimeType(fileName) {
    const fileExtension = fileName.split('.').pop().toLowerCase();

    if (['jpg', 'jpeg'].includes(fileExtension)) {
      return 'image/jpeg';
    } else if (fileExtension === 'png') {
      return 'image/png';
    } else if (fileExtension === 'gif') {
      return 'image/gif';
    } else if (fileExtension === 'webp') {
      return 'image/webp';
    } else if (['mp4', 'mov'].includes(fileExtension)) {
      return `video/${fileExtension}`;
    } else if (['mp3', 'wav', 'ogg'].includes(fileExtension)) {
      return `audio/${fileExtension}`;
    } else if (fileExtension === 'pdf') {
      return 'application/pdf';
    } else if (['doc', 'docx'].includes(fileExtension)) {
      return 'application/msword';
    }

    return 'application/octet-stream';
  }
}

// Esporta un'istanza singleton del servizio
export default new CloudService();
