// 📞 UTILITY PER SALVARE CRONOLOGIA CHIAMATE
// NON TOCCA IL SISTEMA IBRIDO ESISTENTE

/**
 * 💾 Salva una chiamata nella cronologia del server HP
 * @param {Object} callData - <PERSON><PERSON> della chiamata
 */
export const saveCallToHistory = async (callData) => {
  try {
    console.log('💾 CRONOLOGIA: Salvando chiamata:', callData.id);

    // 🏠 SALVA NEL SERVER HP
    const serverUrl = 'http://************:3001';
    const endpoint = `${serverUrl}/api/calls/save`;

    // Prepara i dati per il server HP
    const payload = {
      id: callData.id,
      userId: callData.participants?.[0] || 'unknown',
      type: callData.type || 'audio',
      direction: callData.direction || 'outgoing',
      status: callData.status || 'completed',
      duration: callData.duration || 0,
      startTime: callData.startTime || new Date().toISOString(),
      endTime: callData.endTime || new Date().toISOString(),
      participants: callData.participants || [],
      contactName: callData.contactName || 'Sconosciuto',
      contactAvatar: callData.contactAvatar || null,
      server: callData.server || 'render'
    };

    const response = await fetch(endpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(payload)
    });

    if (response.ok) {
      const result = await response.json();
      console.log('✅ CRONOLOGIA: Chiamata salvata nel server HP:', result);
      return true;
    } else {
      console.error('❌ CRONOLOGIA: Errore salvataggio nel server HP:', response.status);
      return false;
    }

  } catch (error) {
    console.error('❌ CRONOLOGIA: Errore salvataggio chiamata:', error);
    return false;
  }
};

/**
 * 📞 Carica cronologia chiamate dal server HP
 * @param {string} filter - 'all', 'missed', 'incoming', 'outgoing'
 * @returns {Array} Array di chiamate
 */
export const loadCallHistory = async (filter = 'all') => {
  try {
    console.log(`📞 CRONOLOGIA: Caricando dal server HP (filtro: ${filter})`);

    // 🏠 CARICA DAL SERVER HP
    const serverUrl = 'http://************:3001';
    let endpoint = `${serverUrl}/api/calls/history/user_1748339520304`; // Hardcoded per ora
    
    if (filter !== 'all') {
      endpoint += `?filter=${filter}`;
    }

    const response = await fetch(endpoint, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    });

    if (response.ok) {
      const data = await response.json();
      if (data.success && data.calls) {
        console.log(`✅ CRONOLOGIA: Caricate ${data.calls.length} chiamate dal server HP`);
        return data.calls;
      }
    } else {
      console.error('❌ CRONOLOGIA: Errore caricamento dal server HP:', response.status);
    }

    return [];

  } catch (error) {
    console.error('❌ CRONOLOGIA: Errore caricamento cronologia:', error);
    return [];
  }
};

/**
 * 🗑️ Elimina singola chiamata dal server HP
 * @param {string} callId - ID della chiamata da eliminare
 * @returns {boolean} Successo eliminazione
 */
export const deleteCallFromHistory = async (callId) => {
  try {
    console.log(`🗑️ CRONOLOGIA: Eliminando chiamata: ${callId}`);

    const serverUrl = 'http://************:3001';
    const endpoint = `${serverUrl}/api/calls/delete/${callId}`;

    const response = await fetch(endpoint, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json'
      }
    });

    if (response.ok) {
      const result = await response.json();
      console.log('✅ CRONOLOGIA: Chiamata eliminata dal server HP:', result);
      return true;
    } else {
      console.error('❌ CRONOLOGIA: Errore eliminazione dal server HP:', response.status);
      return false;
    }

  } catch (error) {
    console.error('❌ CRONOLOGIA: Errore eliminazione chiamata:', error);
    return false;
  }
};

/**
 * 🗑️ Cancella tutta la cronologia dal server HP
 * @param {string} userId - ID utente
 * @returns {Object} Risultato eliminazione
 */
export const clearAllCallHistory = async (userId) => {
  try {
    console.log(`🗑️ CRONOLOGIA: Cancellando tutta la cronologia per utente: ${userId}`);

    const serverUrl = 'http://************:3001';
    const endpoint = `${serverUrl}/api/calls/clear/${userId}`;

    const response = await fetch(endpoint, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json'
      }
    });

    if (response.ok) {
      const result = await response.json();
      console.log(`✅ CRONOLOGIA: Cronologia cancellata: ${result.deletedCount} chiamate eliminate`);
      return { success: true, deletedCount: result.deletedCount };
    } else {
      console.error('❌ CRONOLOGIA: Errore cancellazione cronologia dal server HP:', response.status);
      return { success: false, deletedCount: 0 };
    }

  } catch (error) {
    console.error('❌ CRONOLOGIA: Errore cancellazione cronologia:', error);
    return { success: false, deletedCount: 0 };
  }
};

/**
 * ⏱️ Formatta la durata della chiamata
 * @param {number} seconds - Durata in secondi
 * @returns {string} Durata formattata
 */
export const formatCallDuration = (seconds) => {
  if (!seconds || seconds === 0) return '';

  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;

  if (minutes === 0) {
    return `${remainingSeconds}s`;
  }

  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
};
