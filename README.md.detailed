# TrendyChat

TrendyChat è un'applicazione di messaggistica moderna sviluppata con React Native ed Expo, ispirata a WhatsApp ma con un design distintivo caratterizzato da un elegante schema di colori con gradiente blu-rosa. L'app offre funzionalità complete di messaggistica, chiamate audio/video, condivisione di media, streaming live e un assistente AI integrato, il tutto con un'interfaccia utente moderna e reattiva.

## 🚀 Funzionalità Principali

- 🔐 **Autenticazione completa**: Login con email/password, verifica telefono con OTP
- 💬 **Chat avanzate**: Chat singole e di gruppo, messaggi vocali, condivisione media
- 📞 **Chiamate**: Chiamate audio e video con WebRTC, supporto PiP e finestra trascinabile
- 📹 **Streaming live**: Trasmissione video in diretta per i tuoi contatti
- 📸 **Stati**: Condivisione di stati con tracciamento visualizzazioni
- 🤖 **Assistente AI**: Assistente virtuale integrato per aiutare gli utenti
- 🔔 **Notifiche push**: Notifiche in tempo reale per nuovi messaggi
- 🌙 **Tema personalizzabile**: Interfaccia con gradiente blu-rosa personalizzabile
- 🔄 **Sincronizzazione contatti**: Importazione e sincronizzazione dei contatti
- 🔒 **Sicurezza**: Crittografia end-to-end per i messaggi
- 🌐 **Localizzazione italiana**: Interfaccia completamente localizzata in italiano
- ⚡ **Prestazioni ottimizzate**: Caricamento veloce e animazioni fluide

## 🛠️ Tecnologie Utilizzate

- **Frontend**: React Native, Expo SDK 52
- **Backend**: Firebase (Authentication, Firestore, Storage, Cloud Messaging)
- **Chiamate e Video**: WebRTC con server di segnalazione Socket.io
- **Streaming**: Implementazione personalizzata basata su WebRTC
- **Animazioni**: Lottie, React Native Animatable
- **Stato**: Zustand per la gestione dello stato
- **Navigazione**: React Navigation v6
- **UI**: Componenti personalizzati con gradiente blu-rosa
- **Media**: Expo AV, Expo Camera, Expo Image Picker, Expo Document Picker
- **Notifiche**: Firebase Cloud Messaging

## 📋 Requisiti

- Node.js 14+
- Expo CLI
- Account Firebase
- Xcode (per iOS)
- Android Studio (per Android)

## 📦 Installazione

1. Clona il repository:
```bash
git clone https://github.com/tuousername/trendychat.git
cd trendychat
```

2. Installa le dipendenze:
```bash
npm install
```

3. Configura Firebase:
   - Crea un nuovo progetto su Firebase Console
   - Abilita Authentication, Firestore e Storage
   - Copia le credenziali in `firebase/config.js`

4. Avvia l'app:
```bash
npx expo start
```

## 🏗️ Build

Per creare una build di produzione:

```bash
# Per Android
npx eas build --platform android --profile production

# Per iOS
npx eas build --platform ios --profile production
```

Per creare una build di sviluppo:

```bash
# Per Android
npx eas build --platform android --profile development

# Per iOS
npx eas build --platform ios --profile development
```

## 📱 Struttura del Progetto

```
/TrendyChat
├── /assets                  # Risorse statiche (immagini, font, animazioni)
│   ├── /animations         # Animazioni Lottie
│   ├── /fonts              # Font personalizzati
│   └── /images             # Immagini e icone
├── /components             # Componenti riutilizzabili
│   ├── ChatBot.jsx         # Componente dell'assistente AI
│   ├── MenuModal.jsx       # Menu principale dell'app
│   └── ...                 # Altri componenti
├── /data                   # Dati mock e costanti
│   ├── mockChats.js        # Dati di esempio per le chat
│   └── ...                 # Altri dati
├── /firebase               # Configurazione e servizi Firebase
│   ├── config.js           # Configurazione Firebase
│   └── ...                 # Altri servizi Firebase
├── /hooks                  # Hook personalizzati
│   ├── useTheme.js         # Hook per la gestione del tema
│   └── ...                 # Altri hook
├── /navigation             # Configurazione della navigazione
│   ├── AppNavigator.jsx    # Navigatore principale dell'app
│   ├── AuthNavigator.jsx   # Navigatore per l'autenticazione
│   └── ...                 # Altri navigatori
├── /screens                # Schermate dell'app
│   ├── /AuthScreens        # Schermate di autenticazione
│   │   ├── LoginScreen.jsx # Schermata di login
│   │   └── ...             # Altre schermate di autenticazione
│   ├── HomeScreen.jsx      # Schermata principale
│   ├── ChatRoomScreen.jsx  # Schermata della chat
│   ├── CallScreenWebRTC.jsx # Schermata delle chiamate
│   ├── SettingsScreen.jsx  # Schermata delle impostazioni
│   └── ...                 # Altre schermate
├── /services               # Servizi esterni
│   ├── aiService.js        # Servizio per l'assistente AI
│   └── ...                 # Altri servizi
├── /store                  # Gestione dello stato
│   ├── authStore.js        # Store per l'autenticazione
│   ├── chatStore.js        # Store per le chat
│   ├── mediaStore.js       # Store per i media
│   ├── webrtcStore.js      # Store per WebRTC
│   └── ...                 # Altri store
├── /theme                  # Configurazione del tema
│   ├── colors.js           # Colori dell'app
│   └── ...                 # Altre configurazioni del tema
├── /utils                  # Utilità e helper
│   ├── formatters.js       # Funzioni di formattazione
│   └── ...                 # Altre utilità
├── App.js                  # Punto di ingresso dell'app
└── package.json            # Dipendenze e script
```

## 🔄 Funzionalità Implementate in Dettaglio

### Autenticazione

- **Registrazione e login**: Supporto per registrazione e login con email/password e numero di telefono
- **Verifica OTP**: Verifica del numero di telefono tramite OTP
- **Persistenza della sessione**: Mantenimento della sessione utente
- **Recupero password**: Funzionalità di recupero password

### Chat

- **Chat individuali**: Messaggistica in tempo reale tra due utenti
- **Chat di gruppo**: Supporto per conversazioni con più partecipanti
- **Stato online**: Indicatore di stato online/offline
- **Indicatore di digitazione**: Notifica quando un utente sta digitando
- **Conferme di lettura**: Conferme di consegna e lettura dei messaggi
- **Ricerca messaggi**: Ricerca all'interno delle conversazioni
- **Archiviazione chat**: Possibilità di archiviare le conversazioni
- **Chat preferite**: Possibilità di contrassegnare le chat come preferite
- **Messaggi importanti**: Funzionalità per salvare messaggi importanti

### Media

- **Condivisione di foto e video**: Invio di foto e video nelle chat
- **Condivisione di documenti**: Invio di documenti nelle chat
- **Condivisione della posizione**: Invio della posizione attuale o di una posizione specifica
- **Registrazione audio**: Invio di messaggi vocali
- **Visualizzazione media**: Galleria integrata per visualizzare i media condivisi

### Chiamate

- **Chiamate audio**: Supporto per chiamate audio one-to-one
- **Videochiamate**: Supporto per videochiamate one-to-one
- **Chiamate di gruppo**: Supporto per chiamate con più partecipanti
- **Picture-in-Picture**: Modalità PiP durante le videochiamate, con finestra trascinabile
- **Cambio camera**: Possibilità di passare dalla camera frontale a quella posteriore
- **Mute/Unmute**: Controllo del microfono durante le chiamate
- **Attivazione/disattivazione video**: Controllo della camera durante le videochiamate

### Streaming Live

- **Trasmissione in diretta**: Possibilità di avviare uno streaming live
- **Visualizzazione streaming**: Possibilità di visualizzare gli streaming dei contatti
- **Interazione durante lo streaming**: Commenti e reazioni durante lo streaming
- **Filtri e effetti**: Applicazione di filtri ed effetti durante lo streaming

### Assistente AI

- **Assistente virtuale**: Assistente AI integrato (TrendyChat AI) con icona della nuvola nella barra superiore
- **Risposte intelligenti**: Risposte basate sul contesto e sulle domande dell'utente
- **Suggerimenti**: Suggerimenti per l'utilizzo dell'app
- **Informazioni**: Informazioni sulle funzionalità dell'app
- **Interfaccia chat**: Interfaccia di chat completa per interagire con l'assistente
- **Animazioni**: Effetti animati sull'icona dell'assistente

### Impostazioni

- **Profilo utente**: Gestione del profilo utente (nome, foto, stato)
- **Notifiche**: Configurazione delle notifiche
- **Privacy**: Impostazioni di privacy (ultimo accesso, conferme di lettura, ecc.)
- **Sicurezza**: Impostazioni di sicurezza (autenticazione a due fattori, ecc.)
- **Backup**: Configurazione dei backup
- **Tema**: Personalizzazione del tema dell'app
- **Lingua**: Selezione della lingua dell'app

## 🔄 Roadmap

1. **Fase 1: Setup e Autenticazione**
   - [x] Setup progetto
   - [x] Configurazione Firebase
   - [x] Schermata di login/registrazione
   - [x] Verifica OTP

2. **Fase 2: Chat Base**
   - [x] Lista chat
   - [x] Chat singola
   - [x] Invio messaggi
   - [x] Upload media

3. **Fase 3: Funzionalità Avanzate**
   - [x] Stati
   - [x] Chiamate audio e video
   - [x] Notifiche push
   - [x] Tema personalizzato

4. **Fase 4: Funzionalità Speciali**
   - [x] Streaming live
   - [x] Assistente AI
   - [x] Picture-in-Picture nelle videochiamate
   - [x] Messaggi importanti

5. **Fase 5: Ottimizzazione**
   - [x] Performance
   - [x] UI/UX
   - [x] Documentazione
   - [ ] Test completi

6. **Fase 6: Funzionalità Future**
   - [ ] Messaggi effimeri
   - [ ] Modalità scura completa
   - [ ] Reazioni ai messaggi
   - [ ] Sondaggi nelle chat
   - [ ] Versione web

## 🔒 Sicurezza e Privacy

- **Crittografia end-to-end**: I messaggi sono crittografati end-to-end
- **Controllo accessi Firebase**: Regole di sicurezza per controllare l'accesso ai dati
- **Autenticazione sicura**: Processo di autenticazione sicuro con Firebase
- **Protezione dei media**: I media sono archiviati in modo sicuro su Firebase Storage
- **Controllo della privacy**: Impostazioni per controllare chi può vedere le informazioni dell'utente

## 🌐 Infrastruttura


### WebRTC

- **Chiamate e videochiamate**: Implementazione basata su WebRTC
- **Server di segnalazione**: Server Socket.io su Render (https://trendychat-signaling.onrender.com)
- **Server STUN/TURN**: Utilizzo dei server Google STUN per la connettività

### Streaming

- **Implementazione personalizzata**: Basata su WebRTC per lo streaming live
- **Server di segnalazione**: Utilizzo dello stesso server di segnalazione delle chiamate

## 📄 Licenza

Questo progetto è protetto da copyright. Tutti i diritti sono riservati.

---

Fatto con ❤️ e React Native
