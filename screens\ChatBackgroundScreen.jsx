import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Image,
  ScrollView,
  Alert,
  SafeAreaView,
  StatusBar,
  Platform,
} from 'react-native';
import { useRoute, useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import * as ImagePicker from 'expo-image-picker';
import { LinearGradient } from 'expo-linear-gradient';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useTheme } from '../hooks/useTheme';
import ChatBackground from '../components/chat/ChatBackground';
import ChatBubble from '../components/ChatBubble';

const BACKGROUND_COLORS = [
  '#FFFFFF',
  '#E5DDD5',
  '#DCF8C6',
  '#D5F5E3',
  '#D6EAF8',
  '#E8DAEF',
  '#FADBD8',
  '#F5EEF8',
  '#EAEDED',
  '#212121',
];

const BACKGROUND_GRADIENTS = [
  ['#E3F2FD', '#BBDEFB'],
  ['#E8F5E9', '#C8E6C9'],
  ['#F3E5F5', '#E1BEE7'],
  ['#FFF3E0', '#FFE0B2'],
  ['#E0F7FA', '#B2EBF2'],
  ['#1A237E', '#4A148C'],
  ['#004D40', '#006064'],
  ['#880E4F', '#4A148C'],
  ['#3E2723', '#212121'],
];

// Utilizziamo pattern semplici invece di immagini
const BACKGROUND_PATTERNS = [
  { color1: '#E5DDD5', color2: '#D4C9C0' },
  { color1: '#DCF8C6', color2: '#C5E1B0' },
  { color1: '#D5F5E3', color2: '#ABEBC6' },
  { color1: '#D6EAF8', color2: '#AED6F1' },
];

const ChatBackgroundScreen = () => {
  const route = useRoute();
  const navigation = useNavigation();
  const { chatId } = route.params || {};

  const { theme, isDark } = useTheme();
  const [activeTab, setActiveTab] = useState('colors');
  const [selectedBackground, setSelectedBackground] = useState({
    type: 'default',
    value: null,
  });
  const [currentBackground, setCurrentBackground] = useState({
    type: 'default',
    value: null,
  });

  useEffect(() => {
    loadCurrentBackground();
  }, [chatId]);

  const loadCurrentBackground = async () => {
    try {
      const key = chatId ? `chat_background_${chatId}` : 'default_chat_background';
      const savedBackground = await AsyncStorage.getItem(key);

      if (savedBackground) {
        setCurrentBackground(JSON.parse(savedBackground));
        setSelectedBackground(JSON.parse(savedBackground));
      }
    } catch (error) {
      console.error('Errore nel caricamento dello sfondo:', error);
    }
  };

  const saveBackground = async () => {
    try {
      const key = chatId ? `chat_background_${chatId}` : 'default_chat_background';
      console.log('💾 TrendyChat: Salvando sfondo con chiave:', key);
      console.log('🎨 TrendyChat: Dati sfondo da salvare:', selectedBackground);

      await AsyncStorage.setItem(key, JSON.stringify(selectedBackground));
      setCurrentBackground(selectedBackground);

      console.log('✅ TrendyChat: Sfondo salvato con successo!');

      Alert.alert(
        'Sfondo impostato',
        'Lo sfondo della chat è stato aggiornato con successo',
        [
          { text: 'OK', onPress: () => navigation.goBack() }
        ]
      );
    } catch (error) {
      console.error('❌ TrendyChat: Errore nel salvataggio dello sfondo:', error);
      Alert.alert('Errore', 'Impossibile salvare lo sfondo');
    }
  };

  const handleSelectColor = (color) => {
    setSelectedBackground({
      type: 'color',
      value: color,
    });
  };

  const handleSelectGradient = (gradient) => {
    setSelectedBackground({
      type: 'gradient',
      value: gradient,
    });
  };

  const handleSelectPattern = (pattern) => {
    setSelectedBackground({
      type: 'pattern',
      value: pattern,
    });
  };

  const handleSelectImage = async () => {
    try {
      const permissionResult = await ImagePicker.requestMediaLibraryPermissionsAsync();

      if (!permissionResult.granted) {
        Alert.alert(
          'Permesso negato',
          'È necessario concedere il permesso per accedere alla galleria'
        );
        return;
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: 'images',
        allowsEditing: true,
        quality: 0.8,
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        setSelectedBackground({
          type: 'image',
          value: result.assets[0].uri,
        });
      }
    } catch (error) {
      console.error('Errore nella selezione dell\'immagine:', error);
      Alert.alert('Errore', 'Impossibile selezionare l\'immagine');
    }
  };

  const handleResetBackground = () => {
    setSelectedBackground({
      type: 'default',
      value: null,
    });
  };

  const renderColorItem = ({ item }) => (
    <TouchableOpacity
      style={[
        styles.colorItem,
        { backgroundColor: item },
        selectedBackground.type === 'color' && selectedBackground.value === item && styles.selectedItem,
      ]}
      onPress={() => handleSelectColor(item)}
    >
      {selectedBackground.type === 'color' && selectedBackground.value === item && (
        <Ionicons name="checkmark" size={24} color={isDark ? '#FFFFFF' : '#000000'} />
      )}
    </TouchableOpacity>
  );

  const renderGradientItem = ({ item }) => (
    <TouchableOpacity
      style={[
        styles.gradientItem,
        selectedBackground.type === 'gradient' &&
        JSON.stringify(selectedBackground.value) === JSON.stringify(item) &&
        styles.selectedItem,
      ]}
      onPress={() => handleSelectGradient(item)}
    >
      <LinearGradient
        colors={item}
        style={styles.gradientContent}
      >
        {selectedBackground.type === 'gradient' &&
         JSON.stringify(selectedBackground.value) === JSON.stringify(item) && (
          <Ionicons name="checkmark" size={24} color="#FFFFFF" />
        )}
      </LinearGradient>
    </TouchableOpacity>
  );

  const renderPatternItem = ({ item, index }) => (
    <TouchableOpacity
      style={[
        styles.patternItem,
        selectedBackground.type === 'pattern' &&
        selectedBackground.value === index &&
        styles.selectedItem,
      ]}
      onPress={() => handleSelectPattern(index)}
    >
      <LinearGradient
        colors={[item.color1, item.color2]}
        style={styles.patternImage}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        <View style={styles.patternOverlay}>
          {/* Pattern di punti o linee */}
          {Array.from({ length: 10 }).map((_, i) => (
            <View key={i} style={styles.patternRow}>
              {Array.from({ length: 10 }).map((_, j) => (
                <View key={j} style={styles.patternDot} />
              ))}
            </View>
          ))}
        </View>
      </LinearGradient>
      {selectedBackground.type === 'pattern' && selectedBackground.value === index && (
        <View style={styles.selectedOverlay}>
          <Ionicons name="checkmark" size={24} color="#FFFFFF" />
        </View>
      )}
    </TouchableOpacity>
  );

  const renderPreview = () => {
    let backgroundComponent;

    switch (selectedBackground.type) {
      case 'color':
        backgroundComponent = (
          <View style={[styles.previewBackground, { backgroundColor: selectedBackground.value }]}>
            {renderChatBubbles()}
          </View>
        );
        break;

      case 'gradient':
        backgroundComponent = (
          <LinearGradient
            colors={selectedBackground.value}
            style={styles.previewBackground}
          >
            {renderChatBubbles()}
          </LinearGradient>
        );
        break;

      case 'pattern':
        const pattern = BACKGROUND_PATTERNS[selectedBackground.value];
        backgroundComponent = (
          <LinearGradient
            colors={[pattern.color1, pattern.color2]}
            style={styles.previewBackground}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
          >
            <View style={styles.patternOverlay}>
              {/* Pattern di punti o linee */}
              {Array.from({ length: 20 }).map((_, i) => (
                <View key={i} style={styles.patternRow}>
                  {Array.from({ length: 20 }).map((_, j) => (
                    <View key={j} style={styles.patternDot} />
                  ))}
                </View>
              ))}
            </View>
            <View style={[styles.overlay, { backgroundColor: 'rgba(255, 255, 255, 0.2)' }]}>
              {renderChatBubbles()}
            </View>
          </LinearGradient>
        );
        break;

      case 'image':
        backgroundComponent = (
          <Image
            source={{ uri: selectedBackground.value }}
            style={styles.previewBackground}
            resizeMode="cover"
          >
            <View style={[styles.overlay, { backgroundColor: 'rgba(0, 0, 0, 0.2)' }]}>
              {renderChatBubbles()}
            </View>
          </Image>
        );
        break;

      default:
        // Preview sfondo default TrendyChat con gradiente blu-rosa
        backgroundComponent = (
          <LinearGradient
            colors={['#1E88E5', '#D81B60']}
            style={styles.previewBackground}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
          >
            <View style={[styles.overlay, { backgroundColor: 'rgba(0, 0, 0, 0.1)' }]}>
              {renderChatBubbles()}
            </View>
          </LinearGradient>
        );
    }

    return backgroundComponent;
  };

  const renderChatBubbles = () => (
    <View style={styles.bubblesContainer}>
      <ChatBubble
        message={{
          text: 'Ciao, come stai?',
          timestamp: new Date(),
          isOwnMessage: false,
        }}
      />
      <ChatBubble
        message={{
          text: 'Tutto bene, grazie! E tu?',
          timestamp: new Date(),
          isOwnMessage: true,
        }}
      />
    </View>
  );

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent={true} />

      <LinearGradient
        colors={['#1E88E5', '#D81B60']}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 0 }}
        style={styles.header}
      >
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color="#FFFFFF" />
        </TouchableOpacity>

        <Text style={styles.headerTitle}>
          Sfondo chat
        </Text>

        <TouchableOpacity
          style={styles.saveButton}
          onPress={saveBackground}
        >
          <Text style={styles.saveButtonText}>Salva</Text>
        </TouchableOpacity>
      </LinearGradient>

      <View style={styles.previewContainer}>
        {renderPreview()}
      </View>

      <View style={styles.tabContainer}>
        <TouchableOpacity
          style={[styles.tab, activeTab === 'colors' && styles.activeTab]}
          onPress={() => setActiveTab('colors')}
        >
          <Text style={[styles.tabText, activeTab === 'colors' && styles.activeTabText]}>
            Colori
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.tab, activeTab === 'gradients' && styles.activeTab]}
          onPress={() => setActiveTab('gradients')}
        >
          <Text style={[styles.tabText, activeTab === 'gradients' && styles.activeTabText]}>
            Gradienti
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.tab, activeTab === 'patterns' && styles.activeTab]}
          onPress={() => setActiveTab('patterns')}
        >
          <Text style={[styles.tabText, activeTab === 'patterns' && styles.activeTabText]}>
            Pattern
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.tab, activeTab === 'images' && styles.activeTab]}
          onPress={() => setActiveTab('images')}
        >
          <Text style={[styles.tabText, activeTab === 'images' && styles.activeTabText]}>
            Immagini
          </Text>
        </TouchableOpacity>
      </View>

      <View style={styles.optionsContainer}>
        {activeTab === 'colors' && (
          <FlatList
            data={BACKGROUND_COLORS}
            renderItem={renderColorItem}
            keyExtractor={(item) => item}
            numColumns={5}
            contentContainerStyle={styles.colorsList}
          />
        )}

        {activeTab === 'gradients' && (
          <FlatList
            data={BACKGROUND_GRADIENTS}
            renderItem={renderGradientItem}
            keyExtractor={(item, index) => `gradient-${index}`}
            numColumns={3}
            contentContainerStyle={styles.gradientsList}
          />
        )}

        {activeTab === 'patterns' && (
          <FlatList
            data={BACKGROUND_PATTERNS}
            renderItem={renderPatternItem}
            keyExtractor={(item, index) => `pattern-${index}`}
            numColumns={2}
            contentContainerStyle={styles.patternsList}
          />
        )}

        {activeTab === 'images' && (
          <View style={styles.imagesContainer}>
            <TouchableOpacity
              style={styles.selectImageButton}
              onPress={handleSelectImage}
            >
              <Ionicons name="image" size={32} color={theme.colors.primary} />
              <Text style={[styles.selectImageText, { color: theme.colors.text }]}>
                Seleziona dalla galleria
              </Text>
            </TouchableOpacity>

            {selectedBackground.type === 'image' && (
              <Image
                source={{ uri: selectedBackground.value }}
                style={styles.selectedImage}
                resizeMode="cover"
              />
            )}
          </View>
        )}
      </View>

      <TouchableOpacity
        style={styles.resetButton}
        onPress={handleResetBackground}
      >
        <Text style={styles.resetButtonText}>🎨 Ripristina sfondo TrendyChat</Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#121212',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingTop: Platform.OS === 'ios' ? 50 : 30,
    paddingBottom: 10,
    paddingHorizontal: 15,
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  saveButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 20,
  },
  saveButtonText: {
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  previewContainer: {
    height: 200,
    margin: 16,
    borderRadius: 12,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.1)',
    backgroundColor: 'rgba(30, 136, 229, 0.05)',
  },
  previewBackground: {
    flex: 1,
    justifyContent: 'center',
    padding: 16,
  },
  overlay: {
    flex: 1,
    justifyContent: 'center',
    padding: 16,
  },
  bubblesContainer: {
    flex: 1,
    justifyContent: 'center',
  },
  tabContainer: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 255, 255, 0.1)',
    backgroundColor: 'rgba(30, 136, 229, 0.05)',
    marginHorizontal: 10,
    marginTop: 10,
    borderTopLeftRadius: 10,
    borderTopRightRadius: 10,
  },
  tab: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
  },
  activeTab: {
    borderBottomWidth: 2,
    borderBottomColor: '#D81B60',
  },
  tabText: {
    fontSize: 14,
    color: '#AAAAAA',
  },
  activeTabText: {
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  optionsContainer: {
    flex: 1,
    padding: 16,
    backgroundColor: 'rgba(30, 136, 229, 0.05)',
    marginHorizontal: 10,
    marginBottom: 10,
    borderBottomLeftRadius: 10,
    borderBottomRightRadius: 10,
  },
  colorsList: {
    paddingVertical: 8,
  },
  colorItem: {
    width: 50,
    height: 50,
    borderRadius: 25,
    margin: 8,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#EEEEEE',
  },
  gradientsList: {
    paddingVertical: 8,
  },
  gradientItem: {
    width: 100,
    height: 100,
    borderRadius: 8,
    margin: 8,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: '#EEEEEE',
  },
  gradientContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  patternsList: {
    paddingVertical: 8,
  },
  patternItem: {
    width: 150,
    height: 150,
    borderRadius: 8,
    margin: 8,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: '#EEEEEE',
  },
  patternImage: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  patternOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    flexDirection: 'column',
  },
  patternRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    flex: 1,
  },
  patternDot: {
    width: 2,
    height: 2,
    borderRadius: 1,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
  },
  selectedItem: {
    borderWidth: 2,
    borderColor: '#1E88E5',
  },
  selectedOverlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  imagesContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  selectImageButton: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    borderWidth: 2,
    borderColor: 'rgba(30, 136, 229, 0.5)',
    borderStyle: 'dashed',
    borderRadius: 10,
    marginBottom: 16,
    backgroundColor: 'rgba(30, 136, 229, 0.1)',
  },
  selectImageText: {
    marginTop: 8,
    fontSize: 16,
    color: '#FFFFFF',
  },
  selectedImage: {
    width: '100%',
    height: 200,
    borderRadius: 8,
  },
  resetButton: {
    alignItems: 'center',
    paddingVertical: 16,
    marginHorizontal: 16,
    marginVertical: 16,
    backgroundColor: 'rgba(244, 67, 54, 0.1)',
    borderRadius: 10,
    borderWidth: 1,
    borderColor: 'rgba(244, 67, 54, 0.3)',
  },
  resetButtonText: {
    color: '#F44336',
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default ChatBackgroundScreen;
