import { manipulateAsync, SaveFormat } from 'expo-image-manipulator';
import { Video } from 'expo-av';
import * as FileSystem from 'expo-file-system';
import * as MediaLibrary from 'expo-media-library';

/**
 * Servizio centralizzato per la compressione di tutti i media
 * Funziona per: Chat, Gruppi, Community, Storie, Live
 */
class MediaCompressionService {
  constructor() {
    // Configurazioni di compressione per diversi contesti
    this.compressionSettings = {
      // Storie - Massima compressione per durata 24h
      stories: {
        video: {
          maxWidth: 720,
          maxHeight: 1280,
          maxDuration: 15, // 15 secondi
          quality: 0.6,
          bitrate: 500000 // 500 kbps
        },
        image: {
          maxWidth: 1080,
          maxHeight: 1920,
          quality: 0.7,
          format: SaveFormat.JPEG
        }
      },
      
      // Chat e Gruppi - Bilanciamento qualità/dimensione
      chat: {
        video: {
          maxWidth: 720,
          maxHeight: 1280,
          maxDuration: 30, // 30 secondi
          quality: 0.7,
          bitrate: 800000 // 800 kbps
        },
        image: {
          maxWidth: 1600,
          maxHeight: 1200,
          quality: 0.8,
          format: SaveFormat.JPEG
        }
      },
      
      // Community - Qualità media per condivisione pubblica
      community: {
        video: {
          maxWidth: 720,
          maxHeight: 1280,
          maxDuration: 60, // 1 minuto
          quality: 0.75,
          bitrate: 1000000 // 1 Mbps
        },
        image: {
          maxWidth: 1920,
          maxHeight: 1080,
          quality: 0.85,
          format: SaveFormat.JPEG
        }
      },
      
      // Audio per tutti i contesti
      audio: {
        bitrate: 128000, // 128 kbps
        sampleRate: 44100,
        format: 'mp3'
      }
    };
  }

  /**
   * Comprimi un'immagine in base al contesto
   * @param {string} uri - URI dell'immagine
   * @param {string} context - Contesto: 'stories', 'chat', 'community'
   * @returns {Promise<Object>} - Immagine compressa
   */
  async compressImage(uri, context = 'chat') {
    try {
      console.log(`🖼️ Comprimendo immagine per ${context}...`);
      
      const settings = this.compressionSettings[context]?.image || this.compressionSettings.chat.image;
      
      // Ottieni informazioni sull'immagine originale
      const originalInfo = await FileSystem.getInfoAsync(uri);
      console.log(`📊 Dimensione originale: ${(originalInfo.size / 1024 / 1024).toFixed(2)} MB`);
      
      // Comprimi l'immagine
      const compressedImage = await manipulateAsync(
        uri,
        [
          {
            resize: {
              width: settings.maxWidth,
              height: settings.maxHeight
            }
          }
        ],
        {
          compress: settings.quality,
          format: settings.format,
          base64: false
        }
      );
      
      // Verifica la nuova dimensione
      const compressedInfo = await FileSystem.getInfoAsync(compressedImage.uri);
      const compressionRatio = ((originalInfo.size - compressedInfo.size) / originalInfo.size * 100).toFixed(1);
      
      console.log(`✅ Immagine compressa: ${(compressedInfo.size / 1024 / 1024).toFixed(2)} MB`);
      console.log(`📉 Riduzione: ${compressionRatio}%`);
      
      return {
        uri: compressedImage.uri,
        width: compressedImage.width,
        height: compressedImage.height,
        originalSize: originalInfo.size,
        compressedSize: compressedInfo.size,
        compressionRatio: parseFloat(compressionRatio),
        type: 'image'
      };
      
    } catch (error) {
      console.error('❌ Errore nella compressione immagine:', error);
      throw new Error(`Impossibile comprimere l'immagine: ${error.message}`);
    }
  }

  /**
   * Comprimi un video in base al contesto
   * @param {string} uri - URI del video
   * @param {string} context - Contesto: 'stories', 'chat', 'community'
   * @returns {Promise<Object>} - Video compresso
   */
  async compressVideo(uri, context = 'chat') {
    try {
      console.log(`🎥 Comprimendo video per ${context}...`);
      
      const settings = this.compressionSettings[context]?.video || this.compressionSettings.chat.video;
      
      // Ottieni informazioni sul video originale
      const originalInfo = await FileSystem.getInfoAsync(uri);
      console.log(`📊 Dimensione originale: ${(originalInfo.size / 1024 / 1024).toFixed(2)} MB`);
      
      // Per ora usiamo una compressione base con expo-av
      // In futuro si può integrare FFmpeg per compressione avanzata
      const compressedVideo = await this.basicVideoCompression(uri, settings);
      
      return compressedVideo;
      
    } catch (error) {
      console.error('❌ Errore nella compressione video:', error);
      throw new Error(`Impossibile comprimere il video: ${error.message}`);
    }
  }

  /**
   * Compressione base del video
   * @param {string} uri - URI del video
   * @param {Object} settings - Impostazioni di compressione
   * @returns {Promise<Object>} - Video compresso
   */
  async basicVideoCompression(uri, settings) {
    try {
      // Implementazione base - in futuro integrare FFmpeg
      const originalInfo = await FileSystem.getInfoAsync(uri);
      
      // Per ora restituiamo il video originale con metadati
      // TODO: Implementare compressione reale con FFmpeg
      console.log(`⚠️ Compressione video non ancora implementata, usando originale`);
      
      return {
        uri: uri,
        originalSize: originalInfo.size,
        compressedSize: originalInfo.size,
        compressionRatio: 0,
        type: 'video',
        needsCompression: true // Flag per implementazione futura
      };
      
    } catch (error) {
      throw error;
    }
  }

  /**
   * Comprimi audio
   * @param {string} uri - URI dell'audio
   * @param {string} context - Contesto
   * @returns {Promise<Object>} - Audio compresso
   */
  async compressAudio(uri, context = 'chat') {
    try {
      console.log(`🎵 Comprimendo audio per ${context}...`);
      
      const originalInfo = await FileSystem.getInfoAsync(uri);
      console.log(`📊 Dimensione originale: ${(originalInfo.size / 1024 / 1024).toFixed(2)} MB`);
      
      // TODO: Implementare compressione audio
      console.log(`⚠️ Compressione audio non ancora implementata, usando originale`);
      
      return {
        uri: uri,
        originalSize: originalInfo.size,
        compressedSize: originalInfo.size,
        compressionRatio: 0,
        type: 'audio',
        needsCompression: true
      };
      
    } catch (error) {
      console.error('❌ Errore nella compressione audio:', error);
      throw new Error(`Impossibile comprimere l'audio: ${error.message}`);
    }
  }

  /**
   * Comprimi qualsiasi tipo di media automaticamente
   * @param {string} uri - URI del file
   * @param {string} type - Tipo: 'image', 'video', 'audio'
   * @param {string} context - Contesto: 'stories', 'chat', 'community'
   * @returns {Promise<Object>} - Media compresso
   */
  async compressMedia(uri, type, context = 'chat') {
    try {
      console.log(`🔄 Avvio compressione ${type} per ${context}`);
      
      switch (type.toLowerCase()) {
        case 'image':
        case 'photo':
          return await this.compressImage(uri, context);
          
        case 'video':
          return await this.compressVideo(uri, context);
          
        case 'audio':
          return await this.compressAudio(uri, context);
          
        default:
          throw new Error(`Tipo di media non supportato: ${type}`);
      }
      
    } catch (error) {
      console.error('❌ Errore nella compressione media:', error);
      throw error;
    }
  }

  /**
   * Ottieni le impostazioni di compressione per un contesto
   * @param {string} context - Contesto
   * @returns {Object} - Impostazioni
   */
  getCompressionSettings(context) {
    return this.compressionSettings[context] || this.compressionSettings.chat;
  }

  /**
   * Calcola la dimensione stimata dopo compressione
   * @param {number} originalSize - Dimensione originale in bytes
   * @param {string} type - Tipo di media
   * @param {string} context - Contesto
   * @returns {number} - Dimensione stimata
   */
  estimateCompressedSize(originalSize, type, context) {
    const estimatedReduction = {
      image: 0.6, // 60% di riduzione media
      video: 0.7, // 70% di riduzione media
      audio: 0.5  // 50% di riduzione media
    };
    
    const reduction = estimatedReduction[type] || 0.5;
    return Math.round(originalSize * (1 - reduction));
  }
}

// Esporta istanza singleton
const mediaCompressionService = new MediaCompressionService();
export default mediaCompressionService;
