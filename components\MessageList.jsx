import React, { useRef, useEffect } from 'react';
import {
  View,
  FlatList,
  StyleSheet,
  ActivityIndicator,
} from 'react-native';
import MessageBubble from './MessageBubble';

const MessageList = ({ messages, isLoading, currentUser }) => {
  const flatListRef = useRef(null);

  useEffect(() => {
    if (messages.length > 0) {
      // Con FlatList invertito, scrollToIndex(0) porta all'ultimo messaggio
      flatListRef.current?.scrollToIndex({ index: 0, animated: true });
    }
  }, [messages]);

  const renderMessage = ({ item }) => (
    <MessageBubble
      message={item}
      isUser={item.senderId === currentUser?.id}
    />
  );

  const renderFooter = () => {
    if (!isLoading) return null;
    return (
      <View style={styles.footer}>
        <ActivityIndicator size="small" color="#128C7E" />
      </View>
    );
  };

  return (
    <FlatList
      ref={flatListRef}
      data={messages}
      renderItem={renderMessage}
      keyExtractor={(item) => item.id}
      contentContainerStyle={styles.container}
      onEndReachedThreshold={0.5}
      ListFooterComponent={renderFooter}
      showsVerticalScrollIndicator={false}
      inverted={true}
      onContentSizeChange={() => {
        // Auto-scroll ai nuovi messaggi
        if (flatListRef.current && messages.length > 0) {
          flatListRef.current.scrollToIndex({
            index: 0,
            animated: true
          });
        }
      }}
    />
  );
};

const styles = StyleSheet.create({
  container: {
    flexGrow: 1,
    padding: 16,
    // Con inverted={true}, i messaggi più recenti appaiono in basso
    paddingTop: 16,
    paddingBottom: 80, // Spazio extra per l'input in basso
  },
  footer: {
    paddingVertical: 20,
    alignItems: 'center',
  },
});

export default MessageList;