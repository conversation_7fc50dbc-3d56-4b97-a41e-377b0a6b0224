import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Image
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { useNavigation } from '@react-navigation/native';
import { getUserCalls } from '../services/callService';
import { formatDistanceToNow } from 'date-fns';
import { it } from 'date-fns/locale';

const CallHistory = () => {
  const [calls, setCalls] = useState([]);
  const navigation = useNavigation();

  useEffect(() => {
    const unsubscribe = getUserCalls((updatedCalls) => {
      setCalls(updatedCalls);
    });

    return () => unsubscribe();
  }, []);

  const getCallIcon = (call) => {
    if (call.missed) {
      return 'phone-missed';
    }
    if (call.rejected) {
      return 'phone-hangup';
    }
    return call.type === 'video' ? 'video' : 'phone';
  };

  const getCallStatus = (call) => {
    if (call.missed) {
      return 'Chiamata persa';
    }
    if (call.rejected) {
      return 'Chiamata rifiutata';
    }
    if (call.duration > 0) {
      const minutes = Math.floor(call.duration / 60);
      const seconds = call.duration % 60;
      return `${minutes}:${seconds.toString().padStart(2, '0')}`;
    }
    return 'Chiamata non risposta';
  };

  const handleCallPress = (call) => {
    navigation.navigate('Call', {
      callId: call.id,
      isIncoming: false,
      recipientId: call.recipientId
    });
  };

  const renderCallItem = ({ item: call }) => (
    <TouchableOpacity
      style={styles.callItem}
      onPress={() => handleCallPress(call)}
    >
      <Image
        source={{ uri: call.recipientPhotoURL }}
        style={styles.avatar}
      />
      <View style={styles.callInfo}>
        <Text style={styles.name}>{call.recipientName}</Text>
        <View style={styles.callDetails}>
          <Icon
            name={getCallIcon(call)}
            size={16}
            color={call.missed ? '#ff4444' : '#4CAF50'}
            style={styles.callIcon}
          />
          <Text style={styles.time}>
            {formatDistanceToNow(call.startTime.toDate(), {
              addSuffix: true,
              locale: it
            })}
          </Text>
        </View>
      </View>
      <Text style={styles.duration}>{getCallStatus(call)}</Text>
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      <FlatList
        data={calls}
        renderItem={renderCallItem}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.listContent}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  listContent: {
    paddingVertical: 10,
  },
  callItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  avatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    marginRight: 15,
  },
  callInfo: {
    flex: 1,
  },
  name: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  callDetails: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  callIcon: {
    marginRight: 5,
  },
  time: {
    fontSize: 14,
    color: '#666',
  },
  duration: {
    fontSize: 14,
    color: '#666',
  },
});

export default CallHistory; 