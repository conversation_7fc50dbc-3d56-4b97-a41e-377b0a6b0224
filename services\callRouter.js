// 📞 Call Router per TrendyChat - Architettura Ibrida
// Gestisce l'instradamento intelligente delle chiamate

import webrtcService from './webrtcService';
import sfuService from './sfuService';

const webrtcConfig = require('../webrtc.config');

class CallRouter {
  constructor() {
    this.activeCall = null;
    this.callType = null; // 'p2p' | 'sfu'
    this.participants = [];
    this.isInitialized = false;
  }

  // 🚀 Inizializza il router
  async initialize(userId) {
    try {
      console.log('📞 Inizializzando Call Router...');

      this.userId = userId;

      // Inizializza entrambi i servizi
      await Promise.all([
        webrtcService.initialize(),
        sfuService.initialize(userId)
      ]);

      this.isInitialized = true;
      console.log('✅ Call Router inizializzato');

    } catch (error) {
      console.error('❌ Errore inizializzazione Call Router:', error);
      throw error;
    }
  }

  // 📞 Avvia una chiamata (decide automaticamente P2P o SFU)
  async startCall(participants, options = {}) {
    try {
      if (!this.isInitialized) {
        throw new Error('Call Router non inizializzato');
      }

      console.log('📞 Avviando chiamata con partecipanti:', participants);

      this.participants = Array.isArray(participants) ? participants : [participants];

      // 🎯 LOGICA DI INSTRADAMENTO
      const callType = this._determineCallType(this.participants.length, options);

      console.log(`🎯 Tipo chiamata determinato: ${callType}`);

      this.callType = callType;

      if (callType === 'p2p') {
        return await this._startP2PCall(this.participants[0], options);
      } else {
        return await this._startSFUCall(this.participants, options);
      }

    } catch (error) {
      console.error('❌ Errore avvio chiamata:', error);
      throw error;
    }
  }

  // 📞 Unisciti a una chiamata esistente
  async joinCall(callId, callType, options = {}) {
    try {
      console.log(`📞 Unendosi alla chiamata ${callId} (tipo: ${callType})`);

      this.callType = callType;

      if (callType === 'p2p') {
        return await this._joinP2PCall(callId, options);
      } else {
        return await this._joinSFUCall(callId, options);
      }

    } catch (error) {
      console.error('❌ Errore unione chiamata:', error);
      throw error;
    }
  }

  // 🔚 Termina la chiamata attiva
  async endCall() {
    try {
      console.log('🔚 Terminando chiamata...');

      if (!this.activeCall) {
        console.log('⚠️ Nessuna chiamata attiva');
        return;
      }

      if (this.callType === 'p2p') {
        await webrtcService.endCall();
      } else {
        await sfuService.leaveRoom();
      }

      this._resetCallState();
      console.log('✅ Chiamata terminata');

    } catch (error) {
      console.error('❌ Errore termine chiamata:', error);
      throw error;
    }
  }

  // 🎤 Attiva/disattiva audio
  async toggleAudio(enabled) {
    try {
      if (this.callType === 'p2p') {
        return await webrtcService.toggleAudio(enabled);
      } else {
        return await sfuService.toggleAudio(enabled);
      }
    } catch (error) {
      console.error('❌ Errore toggle audio:', error);
      return false;
    }
  }

  // 📹 Attiva/disattiva video
  async toggleVideo(enabled) {
    try {
      if (this.callType === 'p2p') {
        return await webrtcService.toggleVideo(enabled);
      } else {
        return await sfuService.toggleVideo(enabled);
      }
    } catch (error) {
      console.error('❌ Errore toggle video:', error);
      return false;
    }
  }

  // 📊 Ottieni statistiche della chiamata
  getCallStats() {
    if (!this.activeCall) {
      return null;
    }

    const baseStats = {
      callId: this.activeCall.id,
      callType: this.callType,
      participants: this.participants,
      duration: Date.now() - this.activeCall.startTime
    };

    if (this.callType === 'p2p') {
      return {
        ...baseStats,
        ...webrtcService.getConnectionStats(),
        signalingServer: webrtcService.getSignalingServerInfo()
      };
    } else {
      return {
        ...baseStats,
        ...sfuService.getCallStats()
      };
    }
  }

  // 📡 Ottieni informazioni sui server attivi
  getServerInfo() {
    return {
      signaling: webrtcService.getSignalingServerInfo(),
      sfu: sfuService.getServerInfo ? sfuService.getServerInfo() : null,
      isInitialized: this.isInitialized
    };
  }

  // 🎯 Determina il tipo di chiamata (P2P vs SFU)
  _determineCallType(participantCount, options = {}) {
    // Forza un tipo specifico se richiesto
    if (options.forceP2P) {
      return 'p2p';
    }

    if (options.forceSFU) {
      return 'sfu';
    }

    // Logica automatica basata sulla configurazione
    const routing = webrtcConfig.callRouting;

    if (participantCount <= routing.oneToOne.maxParticipants) {
      // Chiamata 1-a-1: usa P2P (Render)
      return 'p2p';
    } else if (participantCount >= routing.group.minParticipantsForSFU) {
      // Chiamata di gruppo: usa SFU (SuperMicron)
      return 'sfu';
    } else {
      // Caso intermedio: usa SFU per sicurezza
      return 'sfu';
    }
  }

  // 📞 Avvia chiamata P2P (Render)
  async _startP2PCall(targetUserId, options) {
    try {
      console.log('📞 Avviando chiamata P2P con:', targetUserId);

      const callId = await webrtcService.startCall(targetUserId);

      this.activeCall = {
        id: callId,
        type: 'p2p',
        participants: [this.userId, targetUserId],
        startTime: Date.now(),
        server: 'render'
      };

      console.log('✅ Chiamata P2P avviata:', this.activeCall);
      return this.activeCall;

    } catch (error) {
      console.error('❌ Errore chiamata P2P:', error);
      throw error;
    }
  }

  // 🏠 Avvia chiamata SFU (SuperMicron)
  async _startSFUCall(participants, options) {
    try {
      console.log('🏠 Avviando chiamata SFU con:', participants);

      // Genera room ID unico
      const roomId = `room_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

      // Unisciti alla room SFU
      const roomData = await sfuService.joinRoom(
        roomId,
        options.isVideoEnabled !== false,
        options.isAudioEnabled !== false
      );

      this.activeCall = {
        id: roomId,
        type: 'sfu',
        participants: [this.userId, ...participants],
        startTime: Date.now(),
        server: 'supermicron',
        roomData
      };

      console.log('✅ Chiamata SFU avviata:', this.activeCall);
      return this.activeCall;

    } catch (error) {
      console.error('❌ Errore chiamata SFU:', error);
      throw error;
    }
  }

  // 📞 Unisciti a chiamata P2P
  async _joinP2PCall(callId, options) {
    try {
      console.log('📞 Unendosi a chiamata P2P:', callId);

      const result = await webrtcService.answerCall(callId);

      this.activeCall = {
        id: callId,
        type: 'p2p',
        participants: [this.userId], // Altri partecipanti verranno aggiunti
        startTime: Date.now(),
        server: 'render'
      };

      return result;

    } catch (error) {
      console.error('❌ Errore unione P2P:', error);
      throw error;
    }
  }

  // 🏠 Unisciti a chiamata SFU
  async _joinSFUCall(roomId, options) {
    try {
      console.log('🏠 Unendosi a chiamata SFU:', roomId);

      const roomData = await sfuService.joinRoom(
        roomId,
        options.isVideoEnabled !== false,
        options.isAudioEnabled !== false
      );

      this.activeCall = {
        id: roomId,
        type: 'sfu',
        participants: [this.userId], // Altri partecipanti verranno aggiunti
        startTime: Date.now(),
        server: 'supermicron',
        roomData
      };

      return roomData;

    } catch (error) {
      console.error('❌ Errore unione SFU:', error);
      throw error;
    }
  }

  // 🔄 Reset stato chiamata
  _resetCallState() {
    this.activeCall = null;
    this.callType = null;
    this.participants = [];
  }

  // 🔌 Disconnetti tutti i servizi
  disconnect() {
    try {
      console.log('🔌 Disconnessione Call Router...');

      if (this.activeCall) {
        this.endCall();
      }

      webrtcService.disconnect();
      sfuService.disconnect();

      this.isInitialized = false;
      console.log('✅ Call Router disconnesso');

    } catch (error) {
      console.error('❌ Errore disconnessione Call Router:', error);
    }
  }
}

export default new CallRouter();
