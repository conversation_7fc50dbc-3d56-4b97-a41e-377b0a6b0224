import React, { useEffect, useState, useRef } from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { StatusBar } from 'expo-status-bar';
import { LogBox, View, Text, ActivityIndicator, Alert } from 'react-native';
import * as Notifications from 'expo-notifications';
import * as SplashScreen from 'expo-splash-screen';
import { registerForPushNotificationsAsync, handleNotificationResponse } from './services/NotificationService';
import useWebRTCStore from './store/webrtcStore';
import usePresenceStore from './store/usePresenceStore';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import MainNavigator from './navigation/MainNavigator';
import AuthNavigator from './navigation/AuthNavigator';
import useAuthStore from './store/authStore'; // Utilizziamo solo authStore, non useAuthStore
import { ThemeProvider } from './contexts/ThemeContext';
import { AuthProvider } from './contexts/AuthContext';
import { CallProvider } from './contexts/CallContext';
import { loadFonts } from './utils/loadFonts';
import { theme } from './theme';

// Importa i servizi
import authService from './services/authService';
import * as cloudBackupService from './services/cloudBackupService';
import callRouter from './services/callRouter';

// Previeni la chiusura automatica della schermata di splash
SplashScreen.preventAutoHideAsync().catch(() => {});

// Funzione per mostrare l'alert di Expo Go rimossa perché non necessaria nella build


// Ignora warning specifici
LogBox.ignoreLogs([
  'Setting a timer',
  'AsyncStorage has been extracted',
  'Require cycle:',
  'Possible Unhandled Promise Rejection',
  'Non-serializable values were found in the navigation state',
]);

// Configura le notifiche
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: true,
  }),
});

export default function App() {
  // Inizializzazione silenziosa senza log ripetitivi
  const { user, isLoading, initialize } = useAuthStore();
  const [fontsLoaded, setFontsLoaded] = useState(false);
  const [error, setError] = useState(null);
  const [expoPushToken, setExpoPushToken] = useState('');
  const [notification, setNotification] = useState(false);
  const notificationListener = useRef();
  const responseListener = useRef();
  const navigationRef = useRef();

  // Flag per tracciare se l'app è già stata inizializzata
  const isInitialized = useRef(false);

  useEffect(() => {
    // Evita inizializzazioni multiple
    if (isInitialized.current) return;
    isInitialized.current = true;

    console.log('App.js: Inizializzazione dell\'app');

    // Carica i font e inizializza l'autenticazione
    const initApp = async () => {
      try {
        // Carica i font
        await loadFonts();
        setFontsLoaded(true);

        // Inizializza l'autenticazione
        initialize();

        // Nascondi la schermata di splash quando tutto è pronto
        try {
          await SplashScreen.hideAsync();
        } catch (splashError) {
          console.error('Errore durante la chiusura della schermata di splash:', splashError);
        }
      } catch (err) {
        console.error('Errore durante l\'inizializzazione dell\'app:', err);
        setError(err.message);
        // Nascondi comunque la schermata di splash in caso di errore
        try {
          await SplashScreen.hideAsync();
        } catch (splashError) {
          console.error('Errore durante la chiusura della schermata di splash dopo errore:', splashError);
        }
      }
    };

    initApp();
  }, []);

  // Effetto per gestire le notifiche push
  useEffect(() => {
    // Registra il dispositivo per le notifiche push solo se l'utente è autenticato
    if (user) {
      // Passa l'ID utente corretto alla funzione di registrazione
      registerForPushNotificationsAsync(user.id)
        .then(token => setExpoPushToken(token))
        .catch(error => console.error('Errore nella registrazione per notifiche push:', error));
    }

    // Ascolta le notifiche in arrivo
    notificationListener.current = Notifications.addNotificationReceivedListener(notification => {
      setNotification(notification);
    });

    // Ascolta le risposte alle notifiche
    responseListener.current = Notifications.addNotificationResponseReceivedListener(response => {
      if (navigationRef.current) {
        handleNotificationResponse(response, navigationRef.current);
      }
    });

    return () => {
      Notifications.removeNotificationSubscription(notificationListener.current);
      Notifications.removeNotificationSubscription(responseListener.current);
    };
  }, [user]);

  // Flag per tracciare se l'architettura ibrida è già stata inizializzata
  const hybridInitialized = useRef(false);

  // Effetto separato per inizializzare WebRTC quando navigationRef è disponibile
  useEffect(() => {
    // Verifica che l'utente sia autenticato, che navigationRef.current sia disponibile
    // e che l'architettura ibrida NON sia già stata inizializzata
    if (user && navigationRef.current && !hybridInitialized.current) {
      try {
        // 🚀 Inizializza l'architettura ibrida WebRTC SOLO UNA VOLTA
        console.log('🚀 Inizializzando architettura ibrida per utente:', user.id);
        hybridInitialized.current = true; // Marca come inizializzata

        // Inizializza WebRTC Store (legacy)
        useWebRTCStore.getState().initialize(user.id, navigationRef.current);

        // 📞 Inizializza Call Router (architettura ibrida)
        callRouter.initialize(user.id).then(() => {
          console.log('✅ Architettura ibrida WebRTC inizializzata con successo');
        }).catch((error) => {
          console.error('❌ Errore inizializzazione architettura ibrida:', error);
        });

        // 🟢 Inizializza sistema presenza (Online/Ultimo accesso)
        const presenceCleanup = usePresenceStore.getState().initialize();
        console.log('🟢 Sistema presenza inizializzato');

        // Verifica se è necessario eseguire un backup automatico
        const checkBackup = async () => {
          try {
            const shouldBackup = await cloudBackupService.shouldPerformBackup();
            if (shouldBackup) {
              console.log('Esecuzione backup automatico programmato...');
              await cloudBackupService.createLocalBackup();
              console.log('Backup automatico completato con successo');
            }
          } catch (backupError) {
            console.error('Errore durante la verifica/esecuzione del backup automatico:', backupError);
          }
        };

        // Esegui la verifica del backup dopo un breve ritardo per dare priorità all'avvio dell'app
        setTimeout(checkBackup, 10000);
      } catch (error) {
        console.error('Errore nell\'inizializzazione dei servizi:', error);
      }
    }
  }, [user?.id, navigationRef.current]); // Dipende solo dall'ID utente, non dall'intero oggetto user

  // Reset del flag quando l'utente si disconnette
  useEffect(() => {
    if (!user) {
      hybridInitialized.current = false;
      console.log('🔄 Reset flag architettura ibrida - utente disconnesso');
    }
  }, [user]);

  // Mostra schermata di caricamento mentre i font vengono caricati
  if (!fontsLoaded || isLoading) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: theme.colors.background }}>
        {error ? (
          <Text style={{ color: theme.colors.error, textAlign: 'center', margin: 20 }}>
            Si è verificato un errore: {error}
          </Text>
        ) : (
          <>
            <ActivityIndicator size="large" color={theme.colors.primary} />
            <Text style={{ marginTop: 20, color: theme.colors.text }}>Caricamento TrendyChat...</Text>
          </>
        )}
      </View>
    );
  }

  // Controlla se l'utente è autenticato e reindirizza alla schermata appropriata
  return (
    <SafeAreaProvider>
      <NavigationContainer ref={navigationRef}>
        <ThemeProvider>
          <AuthProvider>
            <CallProvider>
              <StatusBar style="light-content" translucent={true} backgroundColor="transparent" hidden={false} />
              {user && user.displayName && user.displayName.trim() !== '' && user.avatar && user.avatar !== null && !user.avatar.includes('placeholder') ? <MainNavigator /> : <AuthNavigator />}
            </CallProvider>
          </AuthProvider>
        </ThemeProvider>
      </NavigationContainer>
    </SafeAreaProvider>
  );
}