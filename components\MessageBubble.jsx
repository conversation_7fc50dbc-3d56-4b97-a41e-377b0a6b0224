import React from 'react';
import {
  View,
  Text,
  Image,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
} from 'react-native';
import { Audio } from 'expo-av';
import { Ionicons } from '@expo/vector-icons';
import colors from '../theme/colors';

const { width } = Dimensions.get('window');
const MAX_BUBBLE_WIDTH = width * 0.7;

const MessageBubble = ({ message, isUser }) => {
  const [sound, setSound] = React.useState();
  const [isPlaying, setIsPlaying] = React.useState(false);

  async function playSound() {
    if (sound) {
      if (isPlaying) {
        await sound.pauseAsync();
      } else {
        await sound.playAsync();
      }
      setIsPlaying(!isPlaying);
    } else if (message.audio) {
      const { sound: newSound } = await Audio.Sound.createAsync(
        { uri: message.audio.uri },
        { shouldPlay: true }
      );
      setSound(newSound);
      setIsPlaying(true);
    }
  }

  React.useEffect(() => {
    return sound
      ? () => {
          sound.unloadAsync();
        }
      : undefined;
  }, [sound]);

  const renderContent = () => {
    if (message.image) {
      return (
        <Image
          source={{ uri: message.image.uri }}
          style={styles.image}
          resizeMode="cover"
        />
      );
    }

    if (message.audio) {
      return (
        <TouchableOpacity
          style={styles.audioContainer}
          onPress={playSound}
        >
          <Ionicons
            name={isPlaying ? 'pause' : 'play'}
            size={24}
            color={isUser ? colors.text : colors.icon}
          />
          <View style={styles.audioWaveform} />
        </TouchableOpacity>
      );
    }

    return (
      <Text
        style={[
          styles.text,
          isUser && styles.ownText
        ]}
        allowFontScaling={true}
      >
        {message.text}
      </Text>
    );
  };

  return (
    <View style={[
      styles.container,
      isUser ? styles.ownContainer : styles.otherContainer
    ]}>
      {renderContent()}
      <Text style={[
        styles.timestamp,
        isUser && styles.ownTimestamp
      ]}>
        {new Date(message.timestamp).toLocaleTimeString([], {
          hour: '2-digit',
          minute: '2-digit'
        })}
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    maxWidth: MAX_BUBBLE_WIDTH,
    marginVertical: 4,
    padding: 8,
    borderRadius: 16,
  },
  ownContainer: {
    alignSelf: 'flex-end',
    backgroundColor: colors.messageSent,
  },
  otherContainer: {
    alignSelf: 'flex-start',
    backgroundColor: colors.messageReceived,
  },
  text: {
    fontSize: 16,
    color: colors.messageText,
  },
  ownText: {
    color: colors.messageText,
  },
  timestamp: {
    fontSize: 12,
    color: colors.messageTime,
    marginTop: 4,
    alignSelf: 'flex-end',
  },
  ownTimestamp: {
    color: colors.messageTime,
  },
  image: {
    width: MAX_BUBBLE_WIDTH - 16,
    height: MAX_BUBBLE_WIDTH - 16,
    borderRadius: 8,
  },
  audioContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 8,
  },
  audioWaveform: {
    height: 20,
    flex: 1,
    backgroundColor: colors.surface,
    marginLeft: 8,
    borderRadius: 10,
  },
});

export default MessageBubble;