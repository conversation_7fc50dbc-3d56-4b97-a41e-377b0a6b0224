import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  TouchableWithoutFeedback,
  Dimensions,
  Platform,
  ScrollView
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';

const { width, height } = Dimensions.get('window');

const MenuModal = ({ visible, onClose, navigation }) => {
  if (!visible) return null;

  const menuCategories = [
    {
      id: 'communication',
      title: 'Comunicazione',
      items: [
        {
          id: 'new-group',
          icon: 'people-outline',
          label: 'Nuovo gruppo',
          description: 'Crea un gruppo con i tuoi contatti',
          onPress: () => {
            onClose();
            navigation.navigate('NewGroup');
          }
        },
        {
          id: 'my-groups',
          icon: 'people',
          label: 'I miei gruppi',
          description: 'Visualizza e gestisci i tuoi gruppi',
          onPress: () => {
            onClose();
            navigation.navigate('MyGroups');
          }
        },
        {
          id: 'broadcast',
          icon: 'megaphone-outline',
          label: 'Nuovo broadcast',
          description: 'Invia messaggi a più contatti',
          onPress: () => {
            onClose();
            navigation.navigate('BroadcastScreen');
          }
        },
      ]
    },
    {
      id: 'tools',
      title: 'Strumenti',
      items: [
        {
          id: 'starred',
          icon: 'star-outline',
          label: 'Messaggi importanti',
          description: 'Visualizza i messaggi salvati',
          onPress: () => {
            onClose();
            alert('Funzionalità in arrivo');
          }
        },
      ]
    },
    {
      id: 'settings',
      title: 'Impostazioni',
      items: [
        {
          id: 'settings',
          icon: 'settings-outline',
          label: 'Impostazioni',
          description: 'Personalizza la tua esperienza',
          onPress: () => {
            onClose();
            navigation.navigate('SettingsTab');
          }
        },
      ]
    },
  ];

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={onClose}
      statusBarTranslucent
    >
      <TouchableWithoutFeedback onPress={onClose}>
        <View style={styles.modalContainer}>
          <TouchableWithoutFeedback>
            <View style={styles.modalContent}>
              <LinearGradient
                colors={['#1E88E5', '#D81B60']}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 0 }}
                style={styles.header}
              >
                <Text style={styles.headerTitle}>Menu</Text>
                <TouchableOpacity style={styles.closeButton} onPress={onClose}>
                  <Ionicons name="close" size={24} color="#FFFFFF" />
                </TouchableOpacity>
              </LinearGradient>

              <ScrollView style={styles.scrollContainer}>
                {menuCategories.map((category) => (
                  <View key={category.id} style={styles.categoryContainer}>
                    <Text style={styles.categoryTitle}>{category.title}</Text>

                    {category.items.map((item) => (
                      <TouchableOpacity
                        key={item.id}
                        style={styles.optionItem}
                        onPress={item.onPress}
                      >
                        <View style={styles.optionIconContainer}>
                          <LinearGradient
                            colors={['#1E88E5', '#D81B60']}
                            start={{ x: 0, y: 0 }}
                            end={{ x: 1, y: 0 }}
                            style={styles.optionIconGradient}
                          >
                            <Ionicons name={item.icon} size={18} color="#FFFFFF" />
                          </LinearGradient>
                        </View>
                        <View style={styles.optionTextContainer}>
                          <Text style={styles.optionLabel}>{item.label}</Text>
                          <Text style={styles.optionDescription}>{item.description}</Text>
                        </View>
                      </TouchableOpacity>
                    ))}
                  </View>
                ))}
              </ScrollView>
            </View>
          </TouchableWithoutFeedback>
        </View>
      </TouchableWithoutFeedback>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
  },
  modalContent: {
    width: width * 0.85,
    maxHeight: height * 0.8,
    backgroundColor: '#1E1E1E',
    borderRadius: 20,
    overflow: 'hidden',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 16,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  closeButton: {
    padding: 8,
  },
  scrollContainer: {
    maxHeight: height * 0.7,
  },
  categoryContainer: {
    marginBottom: 16,
    paddingHorizontal: 16,
  },
  categoryTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#AAAAAA',
    marginBottom: 8,
    textTransform: 'uppercase',
  },
  optionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#2A2A2A',
  },
  optionIconContainer: {
    marginRight: 16,
  },
  optionIconGradient: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  optionTextContainer: {
    flex: 1,
  },
  optionLabel: {
    fontSize: 16,
    color: '#FFFFFF',
    marginBottom: 2,
  },
  optionDescription: {
    fontSize: 12,
    color: '#AAAAAA',
  },
});

export default MenuModal;
