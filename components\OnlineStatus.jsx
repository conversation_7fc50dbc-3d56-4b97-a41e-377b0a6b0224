import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import usePresenceStore from '../store/usePresenceStore';

const OnlineStatus = ({ userId, showLastSeen = true, textStyle = {}, showDot = false }) => {
  const [presence, setPresence] = useState(null);
  const [loading, setLoading] = useState(true);
  const { getUserPresence, formatLastSeen } = usePresenceStore();

  useEffect(() => {
    if (!userId) {
      setLoading(false);
      return;
    }

    const fetchPresence = async () => {
      try {
        setLoading(true);
        const presenceData = await getUserPresence(userId);
        setPresence(presenceData);
      } catch (error) {
        console.error('Errore nel recupero presenza:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchPresence();

    // Aggiorna ogni 30 secondi
    const interval = setInterval(fetchPresence, 30000);

    return () => clearInterval(interval);
  }, [userId, getUserPresence]);

  if (loading || !presence) {
    return null;
  }

  const isOnline = presence.online;
  const lastSeenText = presence.lastSeenFormatted ||
                      formatLastSeen(presence.lastSeen, isOnline);

  return (
    <View style={styles.container}>
      {showDot && (
        <View style={[
          styles.dot,
          { backgroundColor: isOnline ? '#4CAF50' : '#9E9E9E' }
        ]} />
      )}

      {isOnline ? (
        <Text style={[styles.onlineText, textStyle]}>
          online
        </Text>
      ) : (
        showLastSeen && (
          <Text style={[styles.lastSeenText, textStyle]}>
            {lastSeenText}
          </Text>
        )
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  dot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 6,
  },
  onlineText: {
    fontSize: 13,
    color: '#4CAF50',
  },
  lastSeenText: {
    fontSize: 13,
    color: '#757575',
  },
});

export default OnlineStatus;
