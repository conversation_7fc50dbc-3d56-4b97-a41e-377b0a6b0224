import React, { useEffect, useRef, useState } from 'react';
import {
  View,
  FlatList,
  StyleSheet,
  KeyboardAvoidingView,
  Platform,
  ActivityIndicator,
  SafeAreaView,
  StatusBar,
  Alert,
  Dimensions,
  TouchableOpacity,
  Text
} from 'react-native';
import { useRoute, useNavigation } from '@react-navigation/native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import * as ImagePicker from 'expo-image-picker';
import * as DocumentPicker from 'expo-document-picker';
import * as FileSystem from 'expo-file-system';
import * as MediaLibrary from 'expo-media-library';
import { Audio } from 'expo-av';
import * as VideoThumbnails from 'expo-video-thumbnails';

import useAuthStore from '../store/authStore';
import useChatStore from '../store/useChatStore';
import useWebRTCStore from '../store/webrtcStore';
import usePresenceStore from '../store/usePresenceStore';
import ChatBubble from '../components/ChatBubble';
import InputBox from '../components/InputBox';
import ChatHeader from '../components/ChatHeader';
import AttachmentMenu from '../components/AttachmentMenu';
import TypingIndicator from '../components/chat/TypingIndicator';
import ChatBackground from '../components/chat/ChatBackground';
import RecordingManager from '../utils/RecordingManager';
import AsyncStorage from '@react-native-async-storage/async-storage';
import axios from 'axios';
import { API_URL } from '../config/api';
import { io } from 'socket.io-client';

const { width, height } = Dimensions.get('window');

const ChatRoomScreen = () => {
  const route = useRoute();
  const navigation = useNavigation();
  const { chatId, name, avatar, isOnline, userId = null } = route.params;

  // DEBUG: Log dei parametri ricevuti
  console.log('🔍 ChatRoomScreen parametri ricevuti:', {
    chatId,
    name,
    avatar,
    isOnline,
    userId
  });
  const { user } = useAuthStore();
  const { messages, loadMessages, sendMessage, sendImageMessage, sendVideoMessage, sendAudioMessage, sendDocumentMessage, addReaction, removeReaction, deleteMessageForEveryone, deleteMessageForMe, loading, setTypingStatus, isUserTyping, chats, startEphemeralMessageCleanup, stopEphemeralMessageCleanup, removeExpiredMessages, setCurrentChat } = useChatStore();
  // isRecording e recording ora gestiti da RecordingManager
  const [isTyping, setIsTyping] = useState(false);
  const [loadingMedia, setLoadingMedia] = useState(false);

  // 🟢 STATO UTENTE REALE
  const [userStatus, setUserStatus] = useState({
    isOnline: false,
    message: 'Offline'
  });

  // 📝 NOME E AVATAR AGGIORNABILI
  const [currentName, setCurrentName] = useState(name);
  const [currentAvatar, setCurrentAvatar] = useState(avatar);

  // 📡 WEBSOCKET SOLO PER STATI
  const [socket, setSocket] = useState(null);
  const [otherUserTyping, setOtherUserTyping] = useState(false);
  const [otherUserRecording, setOtherUserRecording] = useState(false);

  // 📡 RECUPERA STATO E DATI UTENTE DAL SERVER
  const fetchUserStatus = async () => {
    console.log('🔍 fetchUserStatus chiamato per userId:', userId);

    // Controlla se è una chat con se stessi
    const { user: currentUser } = useAuthStore.getState();
    if (userId === currentUser?.id) {
      // Chat con se stessi - sempre online quando sei nell'app
      console.log('🟢 Chat con se stessi - impostando Online per userId:', userId);
      setUserStatus({
        isOnline: true,
        message: 'Online'
      });

      // Aggiorna anche nome e avatar dal currentUser aggiornato
      setCurrentName(currentUser.displayName || currentUser.name || name);
      setCurrentAvatar(currentUser.photoURL || currentUser.avatar || avatar);
      console.log('🔄 Aggiornati nome e avatar per chat con se stessi:', {
        nome: currentUser.displayName || currentUser.name,
        avatar: currentUser.photoURL || currentUser.avatar
      });

      console.log('🟢 Stato impostato:', { isOnline: true, message: 'Online' });
      return;
    }

    if (userId === 'unknown_user') {
      // Fallback per utenti sconosciuti
      console.log('⚠️ Utente sconosciuto - impostando Offline');
      setUserStatus({
        isOnline: false,
        message: 'Offline'
      });
      return;
    }

    try {
      // Usa il sistema di presenza esistente invece dell'endpoint sbagliato
      const { getUserPresence, formatLastSeen } = usePresenceStore.getState();
      const presenceData = await getUserPresence(userId);

      if (presenceData && presenceData.success) {
        const statusMessage = presenceData.online ? 'Online' : formatLastSeen(presenceData.lastSeen, false);
        setUserStatus({
          isOnline: presenceData.online,
          message: statusMessage
        });

        // Aggiorna anche nome e avatar se disponibili
        if (presenceData.displayName) {
          setCurrentName(presenceData.displayName);
        }
        if (presenceData.avatar || presenceData.photoURL) {
          setCurrentAvatar(presenceData.avatar || presenceData.photoURL);
        }

        console.log('✅ Stato utente aggiornato:', {
          isOnline: presenceData.online,
          message: statusMessage,
          lastSeen: presenceData.lastSeen,
          nome: presenceData.displayName,
          avatar: presenceData.avatar || presenceData.photoURL
        });
      } else {
        setUserStatus({
          isOnline: false,
          message: 'Offline'
        });
      }
    } catch (error) {
      console.error('Errore recupero stato:', error);
      setUserStatus({
        isOnline: false,
        message: 'Offline'
      });
    }
  };
  const [replyTo, setReplyTo] = useState(null);
  const [showAttachmentMenu, setShowAttachmentMenu] = useState(false);
  const [chatBackground, setChatBackground] = useState({ type: 'default', value: null });
  const flatListRef = useRef(null);

  useEffect(() => {
    const unsubscribe = loadMessages(chatId);
    return () => {
      // Gestisci il caso in cui unsubscribe non sia una funzione
      if (typeof unsubscribe === 'function') {
        unsubscribe();
      } else {
        console.log('Unsubscribe non è una funzione, ignoro la pulizia');
      }
    };
  }, [chatId]);

  // 🔧 IMPOSTA CHAT CORRENTE NEL CHATSTORE (IMPORTANTE PER CHAT ARCHIVIATE)
  useEffect(() => {
    // Trova la chat corrente (anche se archiviata)
    const currentChatData = chats.find(c => c.id === chatId);

    if (currentChatData) {
      // Imposta la chat corrente nel ChatStore
      setCurrentChat(currentChatData);
      console.log('✅ Chat corrente impostata nel ChatStore:', chatId);
    } else {
      // Se la chat non è in chats (potrebbe essere archiviata), crea un oggetto base
      const basicChatData = {
        id: chatId,
        name: name,
        participants: [{ id: userId, name: name }],
        isPrivateChat: true
      };
      setCurrentChat(basicChatData);
      console.log('✅ Chat archiviata impostata nel ChatStore:', chatId);
    }
  }, [chatId, chats, setCurrentChat, name, userId]);

  // 🔧 GESTIONE MESSAGGI EFFIMERI (LATO APP)
  useEffect(() => {
    // Controlla se la chat corrente ha i messaggi effimeri abilitati
    const currentChatData = chats.find(c => c.id === chatId);

    if (currentChatData?.ephemeralMessages?.enabled) {
      console.log('⏰ Chat con messaggi effimeri rilevata, avvio controllo periodico');
      startEphemeralMessageCleanup();

      // Rimuovi immediatamente eventuali messaggi già scaduti
      removeExpiredMessages(chatId);
    } else {
      console.log('⏹️ Chat senza messaggi effimeri, fermo controllo periodico');
      stopEphemeralMessageCleanup();
    }

    return () => {
      // Cleanup quando si esce dalla chat
      stopEphemeralMessageCleanup();
    };
  }, [chatId, chats]);

  // Carica lo sfondo della chat salvato
  useEffect(() => {
    const loadChatBackground = async () => {
      try {
        const key = chatId ? `chat_background_${chatId}` : 'default_chat_background';
        console.log('🔍 TrendyChat: Cercando sfondo con chiave:', key);

        const savedBackground = await AsyncStorage.getItem(key);
        console.log('📱 TrendyChat: Sfondo trovato in AsyncStorage:', savedBackground);

        if (savedBackground) {
          const background = JSON.parse(savedBackground);
          setChatBackground(background);
          console.log('🎨 TrendyChat: Sfondo chat caricato e applicato:', background);
        } else {
          console.log('🎨 TrendyChat: Nessuno sfondo personalizzato, uso default');
          // Verifica se ci sono sfondi salvati con altre chiavi
          const allKeys = await AsyncStorage.getAllKeys();
          const backgroundKeys = allKeys.filter(k => k.includes('chat_background'));
          console.log('🔍 TrendyChat: Tutte le chiavi sfondo trovate:', backgroundKeys);
        }
      } catch (error) {
        console.error('❌ TrendyChat: Errore caricamento sfondo chat:', error);
      }
    };

    loadChatBackground();
    // 🟢 CARICA STATO UTENTE QUANDO ENTRI NELLA CHAT
    fetchUserStatus();
    // 📡 CONNETTI WEBSOCKET PER STATI
    console.log('🔍 Chiamando connectWebSocket...');
    connectWebSocket();
  }, [chatId]);

  // 🔄 AGGIORNA STATO OGNI 30 SECONDI
  useEffect(() => {
    const interval = setInterval(fetchUserStatus, 30000);
    return () => clearInterval(interval);
  }, []);

  // 📡 CONNESSIONE WEBSOCKET PER STATI
  const connectWebSocket = () => {
    console.log('🔍 connectWebSocket chiamato!');

    if (socket) {
      console.log('🔄 Disconnettendo socket esistente...');
      socket.disconnect();
    }

    console.log('🔗 Connettendo WebSocket per stati...');
    console.log('🔗 URL WebSocket:', 'http://192.168.1.66:3001');

    try {
      const newSocket = io('http://192.168.1.66:3001');
      console.log('✅ Socket.IO creato:', newSocket ? 'OK' : 'ERRORE');

    newSocket.on('connect', () => {
      console.log('✅ WebSocket connesso per stati:', newSocket.id);

      // Autentica per impostarti online
      newSocket.emit('authenticate', {
        userId: user?.id || user?.uid,
        userName: user?.displayName || 'Utente'
      });

      // 💬 UNISCITI ALLA CHAT PER RICEVERE EVENTI TYPING
      newSocket.emit('join_chat', chatId);
      console.log('💬 CHAT PRIVATA - Unito alla chat per typing:', chatId);
    });

    // Ricevi stati typing
    newSocket.on('user_typing', (data) => {
      console.log('📨 CHAT PRIVATA - Ricevuto user_typing:', data);
      console.log('🔍 CHAT PRIVATA - DEBUG user_typing:', {
        dataChatId: data.chatId,
        chatIdCorrente: chatId,
        dataUserId: data.userId,
        mioUserId: user?.id || user?.uid,
        èPerQuestaChat: data.chatId === chatId,
        èAltroUtente: data.userId !== (user?.id || user?.uid)
      });

      if (data.chatId === chatId && data.userId !== (user?.id || user?.uid)) {
        console.log('✅ CHAT PRIVATA - AGGIORNANDO otherUserTyping =', data.isTyping);
        setOtherUserTyping(data.isTyping);
        if (data.isTyping) {
          setTimeout(() => {
            console.log('⏰ CHAT PRIVATA - Timeout: rimuovo typing');
            setOtherUserTyping(false);
          }, 5000);
        }
      } else {
        console.log('❌ CHAT PRIVATA - Evento typing ignorato');
      }
    });

    // Ricevi stati recording
    newSocket.on('user_recording', (data) => {
      console.log('🎤 Ricevuto recording:', data);
      if (data.chatId === chatId && data.userId !== (user?.id || user?.uid)) {
        setOtherUserRecording(data.isRecording);
      }
    });

    newSocket.on('disconnect', () => {
      console.log('🔴 WebSocket disconnesso');
    });

    setSocket(newSocket);
    console.log('✅ Socket salvato nello stato');

    } catch (error) {
      console.error('❌ Errore creazione WebSocket:', error);
    }
  };

  // 🧹 CLEANUP WEBSOCKET E USCITA CHAT
  useEffect(() => {
    return () => {
      console.log('🚪 USCENDO DALLA CHAT:', chatId);
      console.log('👤 Utente che esce:', user?.displayName || 'Sconosciuto');
      console.log('⏰ Timestamp uscita:', new Date().toLocaleTimeString());

      if (socket) {
        console.log('🧹 Disconnettendo WebSocket...');
        // Invia evento di uscita dalla chat
        socket.emit('leave_chat', {
          chatId,
          userId: user?.id || user?.uid,
          userName: user?.displayName || 'Utente'
        });
        socket.disconnect();
        console.log('📡 Evento leave_chat inviato');
      }
    };
  }, [socket, chatId, user]);

  // Listener per ricaricare lo sfondo quando si torna alla chat
  useEffect(() => {
    const unsubscribe = navigation.addListener('focus', () => {
      console.log('🔄 TrendyChat: Chat tornata in focus, ricarico sfondo...');
      console.log('🚪 RIENTRANDO NELLA CHAT:', chatId);
      console.log('👤 Utente che rientra:', user?.displayName || 'Sconosciuto');
      console.log('⏰ Timestamp rientro:', new Date().toLocaleTimeString());
      const loadChatBackground = async () => {
        try {
          const key = chatId ? `chat_background_${chatId}` : 'default_chat_background';
          console.log('🔍 TrendyChat: [FOCUS] Cercando sfondo con chiave:', key);

          const savedBackground = await AsyncStorage.getItem(key);
          console.log('📱 TrendyChat: [FOCUS] Sfondo trovato in AsyncStorage:', savedBackground);

          if (savedBackground) {
            const background = JSON.parse(savedBackground);
            setChatBackground(background);
            console.log('🎨 TrendyChat: [FOCUS] Sfondo chat caricato e applicato:', background);
          } else {
            console.log('🎨 TrendyChat: [FOCUS] Nessuno sfondo personalizzato, uso default');
          }
        } catch (error) {
          console.error('❌ TrendyChat: [FOCUS] Errore caricamento sfondo chat:', error);
        }
      };

      loadChatBackground();

      // 🔄 RICARICA STATO UTENTE DOPO RIENTRO
      console.log('🔄 Ricaricando stato utente dopo rientro...');
      setTimeout(() => {
        fetchUserStatus();
      }, 1000); // Aspetta 1 secondo per dare tempo al server
    });

    return unsubscribe;
  }, [navigation, chatId]);

  // ✅ TYPING GESTITO SOLO DA WEBSOCKET - Rimuovo polling che causa conflitti

  const handleSend = async (text, replyToMessage = null) => {
    try {
      if (!text.trim()) return;

      // 📡 INVIA EVENTO STOP TYPING
      if (socket) {
        socket.emit('typing', {
          chatId,
          userId: user?.id || user?.uid,
          userName: user?.displayName || 'Utente',
          isTyping: false
        });
      }

      // Prepara i dati della risposta se presente (SUPPORTA TUTTI I MEDIA)
      let replyData = null;
      if (replyToMessage) {
        const repliedMessage = messages.find(m => m.id === replyToMessage.id);
        if (repliedMessage) {
          replyData = {
            id: repliedMessage.id,
            text: repliedMessage.text,
            type: repliedMessage.type,
            senderName: repliedMessage.senderId === (user.id || user.uid) ? 'Tu' : name,
            senderId: repliedMessage.senderId,
            // Supporta tutti i tipi di media
            uri: repliedMessage.imageUrl || repliedMessage.videoUrl || repliedMessage.audioUrl || repliedMessage.documentUrl || repliedMessage.uri,
            name: repliedMessage.fileName || repliedMessage.name
          };
        }
      }

      // Crea un oggetto messaggio con la proprietà text
      const messageObj = {
        text: text,
        replyTo: replyData
      };

      // Usa user.id invece di user.uid per papareddu
      const userId = user.id || user.uid || 'user_unknown';
      console.log('👤 Invio messaggio con userId:', userId, 'da utente:', user);

      await sendMessage(chatId, userId, messageObj);

      // Resetta lo stato della risposta
      setReplyTo(null);

      // Con FlatList invertito, scrollToIndex(0) porta all'ultimo messaggio
      flatListRef.current?.scrollToIndex({ index: 0, animated: true });
    } catch (error) {
      console.error('Errore nell\'invio del messaggio:', error);
      Alert.alert('Errore', 'Impossibile inviare il messaggio');
    }
  };

  const handleAttachment = () => {
    setShowAttachmentMenu(true);
  };

  const handleSelectMedia = async (media) => {
    try {
      setLoadingMedia(true);
      const userId = user.id || user.uid;

      switch (media.type) {
        case 'image':
          await sendImageMessage(chatId, userId, media.uri);
          break;
        case 'video':
          await sendVideoMessage(chatId, userId, media.uri, media.uri);
          break;
        case 'document':
          await sendDocumentMessage(chatId, userId, media.uri, media.name, media.size || 0, 'application/octet-stream');
          break;
        case 'contact_picker':
          // Naviga alla selezione contatti
          navigation.navigate('ContactPicker', {
            onSelectContact: (contact) => {
              sendMessage(chatId, `👤 Contatto: ${contact.name}\n📞 ${contact.phone}`);
            }
          });
          break;
        case 'location':
          // Invia messaggio con posizione
          await sendMessage(chatId, `📍 Posizione condivisa\nLat: ${media.latitude}\nLng: ${media.longitude}`);
          break;
        default:
          console.log('Tipo media non supportato:', media.type);
      }
    } catch (error) {
      console.error('Errore nell\'invio del media:', error);
      Alert.alert('Errore', 'Impossibile inviare il media');
    } finally {
      setLoadingMedia(false);
    }
  };

  const pickImage = async (useCamera) => {
    try {
      setLoadingMedia(true);
      let result;

      if (useCamera) {
        let cameraPermission;

        if (ImagePicker.requestCameraPermissionsAsync) {
          cameraPermission = await ImagePicker.requestCameraPermissionsAsync();
        } else {
          console.log('API di permessi della fotocamera non trovata, assumo permessi concessi');
          cameraPermission = { status: 'granted' };
        }

        if (cameraPermission.status !== 'granted') {
          Alert.alert('Permesso negato', 'Permesso di accesso alla fotocamera negato');
          return;
        }

        result = await ImagePicker.launchCameraAsync({
          mediaTypes: ImagePicker.MediaTypeOptions.Images,
          allowsEditing: true,
          quality: 0.8,
        });
      } else {
        let galleryPermission;

        if (ImagePicker.requestMediaLibraryPermissionsAsync) {
          galleryPermission = await ImagePicker.requestMediaLibraryPermissionsAsync();
        } else {
          console.log('API di permessi della galleria non trovata, assumo permessi concessi');
          galleryPermission = { status: 'granted' };
        }

        if (galleryPermission.status !== 'granted') {
          Alert.alert('Permesso negato', 'Permesso di accesso alla galleria negato');
          return;
        }

        result = await ImagePicker.launchImageLibraryAsync({
          mediaTypes: 'images',
          allowsEditing: true,
          quality: 0.8,
        });
      }

      if (!result.canceled && result.assets && result.assets.length > 0) {
        const asset = result.assets[0];
        await sendImageMessage(chatId, user.uid, asset.uri);
      }
    } catch (error) {
      console.error('Errore nella selezione dell\'immagine:', error);
      Alert.alert('Errore', 'Impossibile selezionare l\'immagine');
    } finally {
      setLoadingMedia(false);
    }
  };

  const pickVideo = async (useCamera) => {
    try {
      setLoadingMedia(true);
      let result;

      if (useCamera) {
        const { status } = await ImagePicker.requestCameraPermissionsAsync();
        if (status !== 'granted') {
          Alert.alert('Permesso negato', 'Permesso di accesso alla fotocamera negato');
          return;
        }

        result = await ImagePicker.launchCameraAsync({
          mediaTypes: ImagePicker.MediaTypeOptions.Videos,
          allowsEditing: true,
          quality: 0.8,
          videoMaxDuration: 60,
        });
      } else {
        const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
        if (status !== 'granted') {
          Alert.alert('Permesso negato', 'Permesso di accesso alla galleria negato');
          return;
        }

        result = await ImagePicker.launchImageLibraryAsync({
          mediaTypes: ImagePicker.MediaTypeOptions.Videos,
          allowsEditing: true,
          quality: 0.8,
        });
      }

      if (!result.canceled && result.assets && result.assets.length > 0) {
        const asset = result.assets[0];

        // Genera una vera thumbnail dal video
        console.log('🎬 Generando thumbnail per video:', asset.uri);
        try {
          const { uri: thumbnailUri } = await VideoThumbnails.getThumbnailAsync(asset.uri, {
            time: 1000, // Prendi il frame a 1 secondo
            quality: 0.8,
          });
          console.log('✅ Thumbnail generata:', thumbnailUri);
          await sendVideoMessage(chatId, user.uid, asset.uri, thumbnailUri);
        } catch (thumbnailError) {
          console.error('❌ Errore generazione thumbnail:', thumbnailError);
          // Fallback: invia senza thumbnail
          await sendVideoMessage(chatId, user.uid, asset.uri, null);
        }
      }
    } catch (error) {
      console.error('Errore nella selezione del video:', error);
      Alert.alert('Errore', 'Impossibile selezionare il video');
    } finally {
      setLoadingMedia(false);
    }
  };

  const pickDocument = async () => {
    try {
      setLoadingMedia(true);
      console.log('📄 Aprendo DocumentPicker...');

      const result = await DocumentPicker.getDocumentAsync({
        type: '*/*',
        copyToCacheDirectory: true,
      });

      console.log('📄 Risultato DocumentPicker:', result);

      // Usa la nuova API: !result.canceled invece di result.type === 'success'
      if (!result.canceled && result.assets && result.assets.length > 0) {
        const asset = result.assets[0];
        console.log('📄 Documento selezionato:', asset);

        const fileInfo = await FileSystem.getInfoAsync(asset.uri);
        console.log('📄 Info file:', fileInfo);

        const userId = user.id || user.uid;
        console.log('📤 Inviando documento per utente:', userId);

        await sendDocumentMessage(
          chatId,
          userId,
          asset.uri,
          asset.name,
          fileInfo.size || 0,
          asset.mimeType || 'application/octet-stream'
        );

        console.log('✅ Documento inviato con successo');
      } else {
        console.log('📄 Selezione documento annullata o fallita');
      }
    } catch (error) {
      console.error('❌ Errore nella selezione del documento:', error);
      Alert.alert('Errore', 'Impossibile selezionare il documento');
    } finally {
      setLoadingMedia(false);
    }
  };

  const startRecording = async (uri) => {
    try {
      console.log('🎤 ChatRoomScreen: Ricevuto URI audio:', uri ? 'presente' : 'assente');

      if (!uri) {
        console.log('❌ ChatRoomScreen: URI audio mancante');
        return;
      }

      const info = await FileSystem.getInfoAsync(uri);
      if (!info.exists) {
        console.log('❌ ChatRoomScreen: File audio non esiste');
        return;
      }

      // Se la registrazione è troppo breve (meno di 1 secondo), non inviarla
      if (info.size < 1000) {
        Alert.alert('Registrazione troppo breve', 'La registrazione audio è troppo breve');
        return;
      }

      const userId = user.id || user.uid;
      console.log('📤 ChatRoomScreen: Invio messaggio audio per utente:', userId);
      await sendAudioMessage(chatId, userId, uri);
    } catch (error) {
      console.error('Errore nell\'invio del messaggio audio:', error);
      Alert.alert('Errore', 'Impossibile inviare il messaggio audio');
    }
  };

  // 🚀 ARCHITETTURA IBRIDA: Render + SuperMicron
  const callRouter = require('../services/callRouter').default;

  const handleCall = async () => {
    try {
      console.log('📞 IBRIDA: Avviando chiamata audio con architettura ibrida');
      console.log('📞 IBRIDA: Target userId:', route.params.userId);
      console.log('📞 IBRIDA: ChatId:', chatId);

      // 📞 NAVIGA ALLA SCHERMATA DI CHIAMATA PRIMA
      console.log('📞 IBRIDA: Navigando alla schermata di chiamata audio...');
      navigation.navigate('CallWebRTC', {
        chatId: chatId,
        name: name,
        avatar: avatar,
        isVideoCall: false,
        targetUserId: route.params.userId
      });

      // 🚀 AVVIA ARCHITETTURA IBRIDA
      await callRouter.startCall([route.params.userId], {
        isVideoEnabled: false,
        isAudioEnabled: true,
        chatId: chatId,
        targetName: name
      });

      console.log('✅ IBRIDA: Chiamata audio avviata con successo');
    } catch (error) {
      console.error('❌ IBRIDA: Errore chiamata audio:', error);
      Alert.alert('Errore', 'Impossibile avviare la chiamata');
    }
  };

  const handleVideoCall = async () => {
    try {
      console.log('📹 IBRIDA: Avviando videochiamata con architettura ibrida');
      console.log('📹 IBRIDA: Target userId:', route.params.userId);
      console.log('📹 IBRIDA: ChatId:', chatId);

      // 🎥 NAVIGA ALLA SCHERMATA DI CHIAMATA PRIMA
      console.log('🎥 IBRIDA: Navigando alla schermata di videochiamata...');
      navigation.navigate('CallWebRTC', {
        chatId: chatId,
        name: name,
        avatar: avatar,
        isVideoCall: true,
        targetUserId: route.params.userId
      });

      // 🚀 AVVIA ARCHITETTURA IBRIDA
      await callRouter.startCall([route.params.userId], {
        isVideoEnabled: true,
        isAudioEnabled: true,
        chatId: chatId,
        targetName: name
      });

      console.log('✅ IBRIDA: Videochiamata avviata con successo');
    } catch (error) {
      console.error('❌ IBRIDA: Errore videochiamata:', error);
      Alert.alert('Errore', 'Impossibile avviare la videochiamata');
    }
  };

  const handleInfo = () => {
    navigation.navigate('ContactInfo', {
      chatId,
      contactId: route.params.userId,
      name,
      avatar,
      userId: route.params.userId,
      isPrivateChat: true // 🔧 Indica che è una chat privata
    });
  };

  const handleReply = (message) => {
    setReplyTo(message);
  };

  const handleCancelReply = () => {
    setReplyTo(null);
  };

  const handleReplyPress = (messageId) => {
    // Trova il messaggio originale e scorri fino ad esso
    const index = messages.findIndex(m => m.id === messageId);
    if (index !== -1) {
      // Con FlatList invertito, scorri direttamente all'indice
      flatListRef.current?.scrollToIndex({
        index: index,
        animated: true,
        viewPosition: 0.5
      });
    }
  };

  const handleReaction = async (messageId, reactionType) => {
    try {
      // Controlla se l'utente ha già reagito con questo tipo di reazione
      const message = messages.find(m => m.id === messageId);
      if (message && message.reactions && message.reactions[user.uid] === reactionType) {
        // Se l'utente ha già reagito con questo tipo, rimuovi la reazione
        await removeReaction(chatId, messageId, user.uid);
      } else {
        // Altrimenti aggiungi o aggiorna la reazione
        await addReaction(chatId, messageId, user.uid, reactionType);
      }
    } catch (error) {
      console.error('Errore nella gestione della reazione:', error);
      Alert.alert('Errore', 'Impossibile aggiungere la reazione');
    }
  };

  const handleImagePress = (imageUrl) => {
    console.log('🖼️ Navigando al MediaViewer per immagine:', imageUrl);
    navigation.navigate('MediaViewer', {
      mediaUri: imageUrl,
      mediaType: 'image'
    });
  };

  const handleDeleteMessage = (message) => {
    const isOwnMessage = message.senderId === (user.id || user.uid);
    const messageAge = Date.now() - new Date(message.createdAt).getTime();
    const canDeleteForEveryone = isOwnMessage && messageAge < 7 * 60 * 1000; // 7 minuti

    console.log('🗑️ TrendyChat: Opzioni cancellazione per messaggio:', message.id);
    console.log('👤 Messaggio proprio:', isOwnMessage);
    console.log('⏰ Età messaggio (ms):', messageAge);
    console.log('✅ Può cancellare per tutti:', canDeleteForEveryone);

    const options = [
      { text: 'Cancella solo per te', onPress: () => handleDeleteForMe(message.id) },
    ];

    if (canDeleteForEveryone) {
      options.unshift({ text: 'Cancella per tutti', onPress: () => handleDeleteForEveryone(message.id), style: 'destructive' });
    }

    options.push({ text: 'Annulla', style: 'cancel' });

    Alert.alert(
      'Cancella messaggio',
      canDeleteForEveryone
        ? 'Vuoi cancellare questo messaggio per tutti o solo per te?'
        : 'Vuoi cancellare questo messaggio solo per te?',
      options
    );
  };

  const handleDeleteForEveryone = async (messageId) => {
    try {
      console.log('🗑️ TrendyChat: Cancellando messaggio per tutti:', messageId);
      await deleteMessageForEveryone(chatId, messageId);
      Alert.alert('Successo', 'Messaggio cancellato per tutti');
    } catch (error) {
      console.error('❌ Errore cancellazione per tutti:', error);
      Alert.alert('Errore', 'Impossibile cancellare il messaggio per tutti');
    }
  };

  const handleDeleteForMe = async (messageId) => {
    try {
      console.log('👤 TrendyChat: Cancellando messaggio solo per me:', messageId);
      await deleteMessageForMe(chatId, messageId);
      // Non mostrare alert per "cancella solo per te" (come WhatsApp)
    } catch (error) {
      console.error('❌ Errore cancellazione per me:', error);
      Alert.alert('Errore', 'Impossibile cancellare il messaggio');
    }
  };

  // 📱 CALCOLA SUBTITLE CON STATI WEBSOCKET
  const getSubtitle = () => {
    if (otherUserRecording) return 'Sta registrando un audio...';
    if (otherUserTyping) return 'Sta scrivendo...';
    if (isTyping) return 'Sta scrivendo...'; // Fallback sistema vecchio
    return userStatus.message;
  };

  const currentSubtitle = getSubtitle();

  // Log rimossi per pulizia console

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#1E88E5" />

      <ChatHeader
        title={currentName || name || 'Chat'}
        subtitle={currentSubtitle}
        avatar={currentAvatar || avatar}
        isOnline={userStatus.isOnline || otherUserTyping || otherUserRecording}
        onCallPress={handleCall}
        onVideoCallPress={handleVideoCall}
        onInfoPress={handleInfo}
        chatId={chatId}
        isPrivateChat={true} // 🔧 ChatRoomScreen è sempre per chat private
      />

      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'padding'}
        style={styles.keyboardAvoidingView}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 90 : 80}
        enabled
      >
        <ChatBackground
          type={chatBackground.type}
          customImage={chatBackground.type === 'image' ? chatBackground.value : null}
          customColor={chatBackground.type === 'color' ? chatBackground.value : null}
          customGradient={chatBackground.type === 'gradient' ? chatBackground.value : null}
          customPattern={chatBackground.type === 'pattern' ? chatBackground.value : null}
        >
          <View style={styles.chatBackground}>
            {loading ? (
              <View style={styles.loadingContainer}>
                <ActivityIndicator size="large" color="#1E88E5" />
              </View>
            ) : (
              <FlatList
                ref={flatListRef}
                data={messages}
                keyExtractor={(item) => item.id}
                inverted={true}
                renderItem={({ item }) => {
                  return (
                    <ChatBubble
                      message={item}
                      isOwn={item.senderId === (user.id || user.uid)}
                      onReplyPress={handleReplyPress}
                      onReactionPress={(reactionType) => handleReaction(item.id, reactionType)}
                      onImagePress={handleImagePress}
                      onLongPress={() => {
                        // Logica WhatsApp: menu diverso per messaggi propri vs altrui
                        const isOwnMessage = item.senderId === (user.id || user.uid);
                        console.log('📱 TrendyChat: Menu opzioni per messaggio:', item.id, 'tipo:', item.type, 'proprio:', isOwnMessage);

                        const options = [];

                        // MESSAGGI ALTRUI: Rispondi + Elimina (solo per me) + Inoltra
                        if (!isOwnMessage) {
                          options.push({ text: 'Rispondi', onPress: () => handleReply(item) });
                          options.push({ text: 'Inoltra', onPress: () => console.log('Inoltra messaggio:', item.id) });
                          options.push({ text: 'Elimina', onPress: () => handleDeleteForMe(item.id), style: 'destructive' });
                        }
                        // MESSAGGI PROPRI: Elimina (per tutti/per me) + Inoltra
                        else {
                          options.push({ text: 'Inoltra', onPress: () => console.log('Inoltra messaggio:', item.id) });
                          options.push({ text: 'Elimina', onPress: () => handleDeleteMessage(item), style: 'destructive' });
                        }

                        options.push({ text: 'Annulla', style: 'cancel' });

                        Alert.alert(
                          'Opzioni messaggio',
                          '',
                          options
                        );
                      }}
                    />
                  );
                }}
                contentContainerStyle={styles.messageList}
                ListFooterComponent={
                  isTyping ? (
                    <TypingIndicator
                      isTyping={isTyping}
                      userName={name}
                      style={styles.typingIndicator}
                    />
                  ) : null
                }
              />
            )}
          </View>
        </ChatBackground>



        <InputBox
          onSend={handleSend}
          onAttachment={handleAttachment}
          onVoiceMessage={startRecording}
          placeholder="Messaggio"
          replyTo={replyTo}
          onCancelReply={handleCancelReply}
          chatId={chatId}
          userId={user?.id || user?.uid}
          onTypingStatusChange={(chatId, userId, isTyping) => {
            // 📡 INVIA EVENTO TYPING VIA WEBSOCKET
            console.log('📝 CHAT PRIVATA - Typing status changed:', { chatId, userId, isTyping });
            if (socket) {
              socket.emit('typing', {
                chatId,
                userId,
                userName: user?.displayName || 'Utente',
                isTyping
              });
              console.log('📡 CHAT PRIVATA - Evento typing inviato via WebSocket');


            } else {
              console.log('❌ CHAT PRIVATA - Socket NON connesso');
            }
            // Mantieni anche il sistema vecchio
            setTypingStatus(chatId, userId, isTyping);
          }}
        />

        {loadingMedia && (
          <View style={styles.loadingOverlay}>
            <ActivityIndicator size="large" color="#FFFFFF" />
          </View>
        )}

        <AttachmentMenu
          visible={showAttachmentMenu}
          onClose={() => setShowAttachmentMenu(false)}
          onSelectMedia={handleSelectMedia}
        />
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#121212',
  },
  keyboardAvoidingView: {
    flex: 1,
    position: 'relative',
  },
  chatBackground: {
    flex: 1,
    backgroundColor: 'transparent',
  },
  messageList: {
    padding: 10,
    // Con inverted={true}, i messaggi più recenti appaiono in basso
    paddingTop: 10,
    paddingBottom: 80, // Spazio extra per l'input in basso
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyText: {
    fontSize: 16,
    color: '#AAAAAA',
    textAlign: 'center',
    marginTop: 10,
  },
  typingIndicator: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: 'rgba(0, 0, 0, 0.1)',
    borderRadius: 16,
    marginHorizontal: 16,
    marginBottom: 8,
  },
});

export default ChatRoomScreen;