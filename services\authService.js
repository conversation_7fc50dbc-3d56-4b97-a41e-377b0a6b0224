/**
 * Servizio per la gestione dell'autenticazione tramite OTP
 * Questo servizio gestisce la connessione al server di autenticazione e la verifica dell'OTP
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import { Alert } from 'react-native';

// URL del server di autenticazione
// Utilizziamo l'indirizzo IP del nostro server HP ProLiant
const AUTH_API_URL = 'http://************:3001';

// Log per debug
console.log('URL del server di autenticazione:', AUTH_API_URL);

// Chiavi per AsyncStorage
const TOKEN_KEY = '@trendychat:token';
const USER_KEY = '@trendychat:user';
const PHONE_USER_MAP_KEY = '@trendychat:phone_user_mapping'; // Mappa telefono → user ID

/**
 * Classe per la gestione dell'autenticazione
 */
class AuthService {
  constructor() {
    this.token = null;
    this.user = null;
    this.isAuthenticated = false;
    this.listeners = [];
    this.phoneUserMap = new Map(); // Cache locale telefono → user ID
  }

  /**
   * Salva il mapping telefono → user ID per evitare duplicati
   * @param {string} phoneNumber - Numero di telefono
   * @param {string} userId - ID utente
   */
  async savePhoneUserMapping(phoneNumber, userId) {
    try {
      // Carica la mappa esistente
      const existingMapJson = await AsyncStorage.getItem(PHONE_USER_MAP_KEY);
      const existingMap = existingMapJson ? JSON.parse(existingMapJson) : {};

      // Aggiungi il nuovo mapping
      existingMap[phoneNumber] = userId;

      // Salva la mappa aggiornata
      await AsyncStorage.setItem(PHONE_USER_MAP_KEY, JSON.stringify(existingMap));

      // Aggiorna anche la cache locale
      this.phoneUserMap.set(phoneNumber, userId);

      console.log(`📱 Mapping salvato: ${phoneNumber} → ${userId}`);
    } catch (error) {
      console.error('Errore nel salvataggio del mapping telefono-utente:', error);
    }
  }

  /**
   * Recupera l'user ID associato a un numero di telefono
   * @param {string} phoneNumber - Numero di telefono
   * @returns {string|null} - User ID o null se non trovato
   */
  async getExistingUserId(phoneNumber) {
    try {
      // Controlla prima la cache locale
      if (this.phoneUserMap.has(phoneNumber)) {
        const userId = this.phoneUserMap.get(phoneNumber);
        console.log(`📱 User ID trovato in cache locale: ${phoneNumber} → ${userId}`);
        return userId;
      }

      // Carica dalla AsyncStorage
      const mapJson = await AsyncStorage.getItem(PHONE_USER_MAP_KEY);
      if (mapJson) {
        const map = JSON.parse(mapJson);
        const userId = map[phoneNumber];

        if (userId) {
          // Aggiorna la cache locale
          this.phoneUserMap.set(phoneNumber, userId);
          console.log(`📱 User ID trovato in AsyncStorage: ${phoneNumber} → ${userId}`);
          return userId;
        }
      }

      console.log(`📱 Nessun mapping trovato per: ${phoneNumber}`);
      return null;
    } catch (error) {
      console.error('Errore nel recupero del mapping telefono-utente:', error);
      return null;
    }
  }

  /**
   * Inizializza il servizio di autenticazione
   * @returns {Promise<boolean>} - true se l'utente è autenticato
   */
  async initialize() {
    try {
      console.log('Inizializzazione del servizio di autenticazione');

      // Recupera il token e l'utente da AsyncStorage
      const token = await AsyncStorage.getItem(TOKEN_KEY);
      const userJson = await AsyncStorage.getItem(USER_KEY);

      // Prova anche a recuperare i dati utente dalla vecchia chiave
      const oldUserJson = await AsyncStorage.getItem('user_data');

      console.log('Token trovato:', token ? 'Sì' : 'No');
      console.log('Dati utente trovati:', userJson ? 'Sì' : 'No');
      console.log('Dati utente vecchi trovati:', oldUserJson ? 'Sì' : 'No');

      // Se abbiamo dati utente nella vecchia chiave ma non nella nuova, migriamo
      if (oldUserJson && !userJson) {
        console.log('Migrazione dati utente dalla vecchia chiave');
        const oldUser = JSON.parse(oldUserJson);

        // Crea un nuovo oggetto utente con la struttura corretta
        const migratedUser = {
          id: oldUser.uid || ('user_' + Date.now()),
          phoneNumber: oldUser.phoneNumber,
          displayName: oldUser.displayName,
          avatar: oldUser.photoURL || oldUser.avatar,
          photoBase64: oldUser.photoBase64,
          createdAt: new Date().toISOString()
        };

        // Salva l'utente migrato
        await AsyncStorage.setItem(USER_KEY, JSON.stringify(migratedUser));
        console.log('Dati utente migrati con successo');

        // Usa i dati migrati
        this.user = migratedUser;

        // Crea un token temporaneo se non esiste
        if (!token) {
          const tempToken = 'migrated-token-' + Date.now();
          await AsyncStorage.setItem(TOKEN_KEY, tempToken);
          this.token = tempToken;
        } else {
          this.token = token;
        }

        this.isAuthenticated = true;
        this._notifyListeners();
        return true;
      }

      if (token && userJson) {
        this.token = token;
        this.user = JSON.parse(userJson);

        // Correggi la data futura se necessario
        if (this.user && this.user.createdAt) {
          const createdDate = new Date(this.user.createdAt);
          const now = new Date();

          // Se la data di creazione è nel futuro, correggila
          if (createdDate > now) {
            console.log('Correzione data di creazione futura:', this.user.createdAt);
            this.user.createdAt = now.toISOString();
            // Salva l'utente aggiornato in AsyncStorage
            await AsyncStorage.setItem(USER_KEY, JSON.stringify(this.user));
            console.log('Data di creazione corretta:', this.user.createdAt);
          }
        }

        this.isAuthenticated = true;

        console.log(`Utente autenticato dal localStorage: ${this.user.phoneNumber} ${this.user.id}`, this.user);

        // Per ora, non verifichiamo il token con il server
        // In un'app reale, questo verrebbe fatto dal backend
        console.log('Simulazione verifica token');

        this._notifyListeners();
        return true;
      }

      return false;
    } catch (error) {
      console.error('Errore durante l\'inizializzazione del servizio di autenticazione:', error);
      return false;
    }
  }

  /**
   * Invia un codice OTP al numero di telefono specificato
   * @param {string} phoneNumber - Numero di telefono
   * @returns {Promise<Object>} - Risultato dell'operazione
   */
  async sendOTP(phoneNumber) {
    try {
      console.log(`Invio OTP per ${phoneNumber}`);

      // Chiamata all'API del server per inviare l'OTP
      const response = await fetch(`${AUTH_API_URL}/api/otp/generate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ userId: phoneNumber }),
      });

      const data = await response.json();
      console.log('Risposta invio OTP:', data);

      if (!response.ok) {
        throw new Error(data.error || 'Errore durante l\'invio dell\'OTP');
      }

      // In modalità sviluppo, possiamo usare l'OTP restituito dal server
      // In produzione, l'OTP viene inviato via SMS e non è incluso nella risposta
      const devOtp = data.otp;

      console.log('OTP ricevuto dal server:', devOtp);

      return {
        success: true,
        message: data.message || 'OTP inviato con successo',
        otp: devOtp, // Solo per sviluppo
        isNewUser: data.isNewUser || false,
      };
    } catch (error) {
      console.error('Errore durante l\'invio dell\'OTP:', error);

      // In modalità sviluppo, forniamo un messaggio di errore più dettagliato
      // Non forniamo più un OTP fisso in caso di errore
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Verifica un codice OTP
   * @param {string} phoneNumber - Numero di telefono
   * @param {string} otp - Codice OTP
   * @returns {Promise<Object>} - Risultato dell'operazione
   */
  async verifyOTP(phoneNumber, otp) {
    try {
      console.log(`[DEBUG] authService.verifyOTP chiamato con phoneNumber=${phoneNumber}, otp=${otp}`);
      console.log(`[DEBUG] Tipo di phoneNumber: ${typeof phoneNumber}, Tipo di otp: ${typeof otp}`);
      console.log(`[DEBUG] Lunghezza otp: ${otp ? otp.length : 'N/A'}`);
      console.log(`[DEBUG] URL endpoint: ${AUTH_API_URL}/api/otp/verify`);

      // Per test/sviluppo, se l'OTP è 123456, consideriamolo valido anche senza chiamare il server
      if (__DEV__ && otp === '123456') {
        console.log('[DEBUG] Usando OTP di test 123456');
        // Creiamo un utente fittizio per test
        const mockUser = {
          id: 'user_' + Date.now(),
          phoneNumber: phoneNumber,
          displayName: 'Utente Test',
        };

        // Salviamo l'utente in AsyncStorage
        await AsyncStorage.setItem('user', JSON.stringify(mockUser));

        return {
          success: true,
          user: mockUser,
          message: 'OTP verificato con successo (modalità test)'
        };
      }

      // Chiamata all'API del server per verificare l'OTP
      const response = await fetch(`${AUTH_API_URL}/api/otp/verify`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ userId: phoneNumber, otp }),
      });

      let data;
      try {
        // Prima ottieni il testo della risposta per il debug
        const responseText = await response.text();
        console.log('[DEBUG] Risposta dal server (testo):', responseText);
        console.log('[DEBUG] Status code:', response.status);
        console.log('[DEBUG] Headers:', JSON.stringify(Object.fromEntries([...response.headers])));

        // Poi prova a parsare il JSON
        try {
          data = JSON.parse(responseText);
          console.log('[DEBUG] Risposta verifica OTP (parsed):', JSON.stringify(data));
        } catch (parseError) {
          console.log('[DEBUG] Errore nel parsing della risposta JSON:', parseError.message);

          // Se siamo in modalità sviluppo e c'è un errore di parsing, procediamo comunque
          if (__DEV__) {
            console.log('Modalità sviluppo: procedo nonostante l\'errore di parsing');
            data = { success: true };
          } else {
            throw parseError;
          }
        }
      } catch (error) {
        console.error('Errore nel recupero della risposta:', error);
        throw new Error('Errore nella comunicazione con il server');
      }

      if (!response.ok && !(__DEV__ && data.success)) {
        throw new Error(data?.error || 'Errore durante la verifica dell\'OTP');
      }

      // Completa la registrazione se necessario
      let completeResponse;
      let userData;

      try {
        completeResponse = await fetch(`${AUTH_API_URL}/api/auth/register/complete`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            phone: phoneNumber,
            username: `user_${Date.now()}`, // Username temporaneo
            password: `pwd_${Date.now()}`, // Password temporanea
          }),
        });

        try {
          // Prima ottieni il testo della risposta per il debug
          const responseText = await completeResponse.text();
          console.log('Risposta completamento registrazione (testo):', responseText);

          // Poi prova a parsare il JSON
          try {
            userData = JSON.parse(responseText);
            console.log('Risposta completamento registrazione:', userData);
          } catch (parseError) {
            console.log('Errore nel parsing della risposta JSON per completamento:', parseError.message);

            // Se siamo in modalità sviluppo e c'è un errore di parsing, procediamo comunque
            if (__DEV__) {
              console.log('Modalità sviluppo: procedo nonostante l\'errore di parsing per completamento');
              userData = { success: true };
            }
          }
        } catch (textError) {
          console.error('Errore nel recupero della risposta di completamento:', textError);
        }

        if (!completeResponse.ok && !(__DEV__ && userData?.success)) {
          console.warn('Errore durante il completamento della registrazione:', userData?.error);
          // Continua comunque, potrebbe essere che l'utente esista già
        }
      } catch (completeError) {
        console.error('Errore durante il completamento della registrazione:', completeError);
        // Continua comunque, potrebbe essere che l'utente esista già
      }

      // Effettua il login
      let loginResponse;
      let loginData;

      try {
        loginResponse = await fetch(`${AUTH_API_URL}/api/auth/login`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            phone: phoneNumber,
            // Il server TrendyChat dovrebbe riconoscere l'utente solo dal numero di telefono
            // Non inviamo password per evitare di creare utenti duplicati
          }),
        });

        try {
          // Prima ottieni il testo della risposta per il debug
          const responseText = await loginResponse.text();
          console.log('Risposta login (testo):', responseText);

          // Poi prova a parsare il JSON
          try {
            loginData = JSON.parse(responseText);
            console.log('Risposta login:', loginData);
          } catch (parseError) {
            console.log('Errore nel parsing della risposta JSON per login:', parseError.message);

            // Se siamo in modalità sviluppo e c'è un errore di parsing, procediamo comunque
            if (__DEV__) {
              console.log('Modalità sviluppo: procedo nonostante l\'errore di parsing per login');
              loginData = { success: true };
            }
          }
        } catch (textError) {
          console.error('Errore nel recupero della risposta di login:', textError);
        }

        if (!loginResponse.ok && !(__DEV__ && loginData?.success)) {
          console.warn('Errore durante il login:', loginData?.error);
        }
      } catch (loginError) {
        console.error('Errore durante il login:', loginError);
      }

      // Usa i dati del login se disponibili
      let token = loginData?.token;
      let user = loginData?.user;

      if (!token || !user) {
        console.error('Errore: token o dati utente mancanti dalla risposta del server');
        throw new Error('Impossibile completare l\'autenticazione. Token o dati utente mancanti.');
      }

      console.log('Token JWT ricevuto dal server:', token);

      // Formatta il token JWT
      if (token) {
        // Se il token contiene il prefisso jwt_token_, rimuovilo
        if (token.startsWith('jwt_token_')) {
          token = token.replace('jwt_token_', '');
          console.log('Prefisso jwt_token_ rimosso dal token ricevuto dal server');
        }
      }

      // Controlla se abbiamo già un user ID per questo telefono
      const existingUserId = await this.getExistingUserId(phoneNumber);

      // Assicurati che l'utente abbia tutti i campi necessari
      user = {
        id: existingUserId || user.id || user._id, // Usa l'ID esistente se disponibile
        phoneNumber: user.phone || phoneNumber,
        displayName: user.username || user.displayName || '',
        avatar: user.avatar || null, // Non impostare placeholder per forzare setup profilo
        photoURL: user.photoURL || user.avatar || null, // Non impostare placeholder per forzare setup profilo
        createdAt: user.createdAt || new Date().toISOString()
      };

      // Salva il mapping telefono → user ID per il futuro
      await this.savePhoneUserMapping(phoneNumber, user.id);

      if (existingUserId && existingUserId !== user.id) {
        console.log(`🔄 IMPORTANTE: User ID cambiato dal server! Locale: ${existingUserId}, Server: ${user.id}`);
        console.log(`📱 Mantengo l'ID esistente: ${existingUserId}`);

        // Salva l'ID del server per eventuale migrazione dati
        const serverUserId = user.id;
        user.id = existingUserId; // Forza l'uso dell'ID esistente

        // TODO: Implementare migrazione dati dal serverUserId all'existingUserId
        console.log(`🔄 NOTA: I dati potrebbero essere associati a ${serverUserId} sul server`);
        console.log(`🔄 SOLUZIONE: Contattare l'admin per migrare i dati da ${serverUserId} a ${existingUserId}`);
      }

      // Il server TrendyChat non usa JWT, salva il token così com'è
      if (token) {
        console.log('Token ricevuto dal server TrendyChat:', token ? token.substring(0, 10) + '...' : 'Nessun token');
      }

      // Salva il token e l'utente
      this.token = token;
      this.user = user;
      this.isAuthenticated = true;

      // Salva in AsyncStorage per la persistenza
      await AsyncStorage.setItem(TOKEN_KEY, token);
      await AsyncStorage.setItem(USER_KEY, JSON.stringify(user));

      console.log('Dati utente salvati in AsyncStorage:', this.user);
      console.log('Token JWT salvato:', token ? token.substring(0, 10) + '...' : 'Nessun token');

      // Notifica i listener
      this._notifyListeners();

      return {
        success: true,
        message: 'OTP verificato con successo',
        user: user
      };


    } catch (error) {
      console.error('Errore durante la verifica dell\'OTP:', error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Ottiene il profilo dell'utente
   * @returns {Promise<Object>} - Profilo dell'utente
   */
  async getProfile() {
    try {
      if (!this.isAuthenticated) {
        throw new Error('Utente non autenticato');
      }

      const response = await fetch(`${AUTH_API_URL}/api/users/profile`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${this.token}`,
        },
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Errore durante il recupero del profilo');
      }

      // Aggiorna l'utente
      this.user = data.user;

      // Salva in AsyncStorage
      await AsyncStorage.setItem(USER_KEY, JSON.stringify(data.user));

      // Notifica i listener
      this._notifyListeners();

      return {
        success: true,
        user: data.user,
      };
    } catch (error) {
      console.error('Errore durante il recupero del profilo:', error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Aggiorna il profilo dell'utente
   * @param {Object} profileData - Dati del profilo
   * @returns {Promise<Object>} - Risultato dell'operazione
   */
  async updateProfile(profileData) {
    try {
      // Verifica se l'utente è autenticato
      if (!this.isAuthenticated) {
        console.warn('Tentativo di aggiornare il profilo senza autenticazione. Tentativo di recupero utente da AsyncStorage...');

        // Prova a recuperare l'utente da AsyncStorage
        const userJson = await AsyncStorage.getItem(USER_KEY);
        const token = await AsyncStorage.getItem(TOKEN_KEY);

        if (userJson && token) {
          console.log('Utente trovato in AsyncStorage, ripristino sessione');
          this.user = JSON.parse(userJson);
          this.token = token;
          this.isAuthenticated = true;
        } else {
          throw new Error('Utente non autenticato');
        }
      }

      console.log('Aggiornamento profilo con dati:', profileData);

      // Assicurati che l'ID utente e il numero di telefono siano inclusi nei dati del profilo
      if (this.user) {
        profileData.id = this.user.id;
        profileData.phoneNumber = this.user.phoneNumber;
        console.log('Aggiunta ID utente esistente ai dati del profilo:', this.user.id);
      }

      // Verifica se c'è un'immagine del profilo da caricare
      if (profileData.avatar && (profileData.avatar.startsWith('file://') || profileData.avatar.startsWith('content://'))) {
        try {
          // Importa il servizio cloud
          const cloudService = require('./cloudService').default;
          const mediaCloudService = require('./mediaCloudService').default;

          // Carica l'immagine sul server
          console.log('Caricamento immagine profilo sul server:', profileData.avatar);

          // Assicurati che cloudService sia inizializzato con il token
          cloudService.setToken(this.token);

          // Carica l'immagine sul server con metadati espliciti per il tipo di file
          const uploadResult = await cloudService.uploadFile(
            profileData.avatar,
            this.user.id,
            'profile',
            true, // Le immagini del profilo sono pubbliche
            {
              type: 'profile',
              userId: this.user.id,
              displayName: this.user.displayName || '',
              isProfilePhoto: true
            }
          );

          if (uploadResult.success) {
            console.log('Immagine profilo caricata con successo:', uploadResult.file);

            // Salva l'URL dell'avatar corrente prima di sovrascriverlo (per eliminazione)
            const oldAvatarUrl = this.user.avatar || this.user.photoURL;

            // Imposta il nuovo URL sia per avatar che per photoURL
            profileData.photoURL = uploadResult.file.url;
            profileData.avatar = uploadResult.file.url; // Usa lo stesso URL per entrambi
            profileData.oldAvatarUrl = oldAvatarUrl; // Salva l'URL vecchio in un campo separato

            console.log('Vecchio avatar (da eliminare):', oldAvatarUrl);
            console.log('Nuova foto profilo (avatar e photoURL):', uploadResult.file.url);

            // Log dettagliato dell'URL dell'immagine del profilo
            console.log('DEBUG - URL immagine profilo aggiornato:', {
              avatar: profileData.avatar,
              photoURL: profileData.photoURL,
              originalUrl: uploadResult.file.url
            });
          } else {
            console.error('Errore nel caricamento dell\'immagine profilo:', uploadResult.error);
          }
        } catch (uploadError) {
          console.error('Errore durante il caricamento dell\'immagine profilo:', uploadError);
          // Continua con l'aggiornamento del profilo anche se il caricamento dell'immagine fallisce
        }
      }

      // Aggiorna il profilo sul server
      try {
        const response = await fetch(`${AUTH_API_URL}/api/users/update`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${this.token}`,
          },
          body: JSON.stringify(profileData),
        });

        const data = await response.json();
        console.log('Risposta aggiornamento profilo dal server:', data);

        if (!response.ok) {
          console.warn('Errore dal server durante l\'aggiornamento del profilo:', data.error);
          // Continua con l'aggiornamento locale anche se il server fallisce
        } else if (data.user && data.user.id) {
          // Se il server ha restituito un nuovo ID utente, utilizzalo
          console.log('Server ha restituito un nuovo ID utente:', data.user.id);
          console.log('ID utente precedente:', this.user.id);

          // Aggiorna l'utente locale con i dati restituiti dal server, mantenendo i campi locali non presenti nella risposta
          this.user = {
            ...this.user,
            ...data.user
          };

          // Salva in AsyncStorage per la persistenza
          await AsyncStorage.setItem(USER_KEY, JSON.stringify(this.user));
          console.log('Dati utente aggiornati salvati in AsyncStorage con nuovo ID');

          // Notifica i listener
          this._notifyListeners();

          return {
            success: true,
            user: this.user
          };
        }
      } catch (serverError) {
        console.error('Errore nella comunicazione con il server:', serverError);
        // Continua con l'aggiornamento locale anche se il server fallisce
      }

      // Se non abbiamo ricevuto dati dal server o c'è stato un errore, aggiorna solo localmente
      this.user = {
        ...this.user,
        ...profileData
      };

      console.log('Profilo utente aggiornato:', this.user);

      // Salva in AsyncStorage per la persistenza
      await AsyncStorage.setItem(USER_KEY, JSON.stringify(this.user));
      console.log('Dati utente aggiornati salvati in AsyncStorage');

      // Notifica i listener
      this._notifyListeners();

      return {
        success: true,
        user: this.user
      };


    } catch (error) {
      console.error('Errore durante l\'aggiornamento del profilo:', error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Effettua il logout
   * @returns {Promise<boolean>} - true se il logout è avvenuto con successo
   */
  async logout() {
    try {
      // Rimuovi il token e l'utente da AsyncStorage
      await AsyncStorage.removeItem(TOKEN_KEY);
      await AsyncStorage.removeItem(USER_KEY);

      // Resetta lo stato
      this.token = null;
      this.user = null;
      this.isAuthenticated = false;

      // Notifica i listener
      this._notifyListeners();

      return true;
    } catch (error) {
      console.error('Errore durante il logout:', error);
      return false;
    }
  }

  /**
   * Verifica se l'utente è autenticato
   * @returns {boolean} - true se l'utente è autenticato
   */
  isUserAuthenticated() {
    return this.isAuthenticated;
  }

  /**
   * Ottiene l'utente corrente
   * @returns {Object|null} - Utente corrente o null se non autenticato
   */
  getCurrentUser() {
    return this.user;
  }

  /**
   * Ottiene il token JWT
   * @returns {string|null} - Token JWT o null se non autenticato
   */
  getToken() {
    let token = this.token;

    // Se non abbiamo un token in memoria, prova a recuperarlo da AsyncStorage
    if (!token) {
      try {
        // Nota: questa è una chiamata sincrona a una funzione asincrona
        // In un'app reale, dovresti usare async/await, ma per compatibilità
        // con il codice esistente, manteniamo questa implementazione
        AsyncStorage.getItem(TOKEN_KEY).then(storedToken => {
          if (storedToken) {
            this.token = storedToken;
            console.log('Token recuperato da AsyncStorage');
          }
        });
      } catch (error) {
        console.error('Errore nel recupero del token da AsyncStorage:', error);
      }
    }

    // Restituisci il token così com'è (TrendyChat non usa JWT)
    if (token) {
      console.log('Token TrendyChat recuperato:', token ? token.substring(0, 10) + '...' : 'Nessun token');
    }

    return token;
  }

  /**
   * Aggiunge un listener per i cambiamenti di autenticazione
   * @param {Function} listener - Funzione da chiamare quando cambia lo stato di autenticazione
   */
  addAuthChangeListener(listener) {
    this.listeners.push(listener);
  }

  /**
   * Rimuove un listener per i cambiamenti di autenticazione
   * @param {Function} listener - Funzione da rimuovere
   */
  removeAuthChangeListener(listener) {
    this.listeners = this.listeners.filter(l => l !== listener);
  }

  /**
   * Notifica tutti i listener
   * @private
   */
  _notifyListeners() {
    this.listeners.forEach(listener => {
      try {
        listener({
          isAuthenticated: this.isAuthenticated,
          user: this.user,
        });
      } catch (error) {
        console.error('Errore durante la notifica del listener:', error);
      }
    });
  }
}

// Esporta un'istanza singleton del servizio
export default new AuthService();
