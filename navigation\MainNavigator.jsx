import React from 'react';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { Ionicons } from '@expo/vector-icons';
import { View, Text, StyleSheet, TouchableOpacity, Platform } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { theme } from '../theme';

// Screens
import HomeScreen from '../screens/HomeScreen';
import ChatRoomScreen from '../screens/ChatRoomScreen';
import StatusScreen from '../screens/StatusScreen';
import CallScreen from '../screens/CallScreen';
import CallsScreen from '../screens/CallsScreen';
import VideoCallScreen from '../screens/VideoCallScreen';
import AudioCallScreen from '../screens/AudioCallScreen';
import CallScreenWebRTC from '../screens/CallScreenWebRTC';
import CallHistoryScreen from '../screens/CallHistoryScreen';
import AddCallParticipantsScreen from '../screens/AddCallParticipantsScreen';
import SettingsScreen from '../screens/SettingsScreen';
import ProfileScreen from '../screens/ProfileScreen';
import ThemeSettingsScreen from '../screens/ThemeSettingsScreen';
import BackupScreen from '../screens/BackupScreen';
import BackupSettingsScreen from '../screens/BackupSettingsScreen';
import QRCodeScreen from '../screens/QRCodeScreen';
import QRCodeScannerScreen from '../screens/QRCodeScannerScreen';
import ChatBackgroundScreen from '../screens/ChatBackgroundScreen';
import AccountSettings from '../screens/AccountSettings';
import ChatSettings from '../screens/ChatSettings';
import NotificationSettings from '../screens/NotificationSettings';
import StorageSettings from '../screens/StorageSettings';
import LanguageSettings from '../screens/LanguageSettings';
import GroupChatScreen from '../screens/GroupChatScreen';
import GroupInfoScreen from '../screens/GroupInfoScreen';
import EditGroupScreen from '../screens/EditGroupScreen';
import NewGroupScreen from '../screens/NewGroupScreen';
import MyGroupsScreen from '../screens/MyGroupsScreen';
import AddGroupMembersScreen from '../screens/AddGroupMembersScreen';
import ContactListScreen from '../screens/ContactListScreen';
import NewChatScreen from '../screens/NewChatScreen';
import StatusViewScreen from '../screens/StatusViewScreen';
import EarnScreen from '../screens/EarnScreen';
import VideoPlayerScreen from '../screens/VideoPlayerScreen';
import AddMembersToCommunityScreen from '../screens/AddMembersToCommunityScreen';
import LinkedDevicesScreen from '../screens/LinkedDevicesScreen';
import StarredMessagesScreen from '../screens/StarredMessagesScreen';
import ArchivedChatsScreen from '../screens/ArchivedChatsScreen';
import EditProfileScreen from '../screens/EditProfileScreen';
import PrivacySettingsScreen from '../screens/PrivacySettingsScreen';
import BlockedContactsScreen from '../screens/BlockedContactsScreen';
import ChatMediaGalleryScreen from '../screens/ChatMediaGalleryScreen';
import MediaViewerScreen from '../screens/MediaViewerScreen';
import ChatSearchScreen from '../screens/ChatSearchScreen';
import ChatNotificationScreen from '../screens/ChatNotificationScreen';
import EphemeralMessagesScreen from '../screens/EphemeralMessagesScreen';
import ChatMediaScreen from '../screens/ChatMediaScreen';
import ContactInfoScreen from '../screens/ContactInfoScreen';
import ChatListsScreen from '../screens/ChatListsScreen';
import ChatLinkScreen from '../screens/ChatLinkScreen';
import BroadcastScreen from '../screens/BroadcastScreen';
import CreateBroadcastScreen from '../screens/CreateBroadcastScreen';
import TrendyWalletScreen from '../screens/TrendyWalletScreen';
import TrendyStoreScreen from '../screens/TrendyStoreScreen';

// Live Screens
import { LiveHomeScreen } from '../screens/live';
import { LiveSetupScreen, LiveStudioScreen } from '../screens/live_new';
import TrendyLivePreview from '../screens/live_new/TrendyLivePreview.jsx';
import TrendyLiveStream from '../screens/live_new/TrendyLiveStream';

const Tab = createBottomTabNavigator();
const Stack = createNativeStackNavigator();

// Componente per l'header personalizzato
const CustomHeader = ({ title, hasBackButton, navigation, rightComponent }) => (
  <View style={styles.header}>
    {hasBackButton && (
      <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
        <Ionicons name="arrow-back" size={24} color={theme.colors.background} />
      </TouchableOpacity>
    )}
    <Text style={styles.headerTitle}>{title}</Text>
    {rightComponent && <View style={styles.rightComponent}>{rightComponent}</View>}
  </View>
);

// Stack per le chat
const ChatStack = () => (
  <Stack.Navigator
    screenOptions={{
      headerStyle: {
        backgroundColor: '#1E88E5',
      },
      headerTintColor: '#FFFFFF',
      headerTitleStyle: {
        fontWeight: 'bold',
      },
      headerShown: false
    }}
  >
    <Stack.Screen name="ChatList" component={HomeScreen} />
    <Stack.Screen name="ChatRoom" component={ChatRoomScreen} />
    <Stack.Screen name="NewChat" component={NewChatScreen} />
    <Stack.Screen name="GroupChat" component={GroupChatScreen} />
    <Stack.Screen name="GroupInfo" component={GroupInfoScreen} />
    <Stack.Screen name="EditGroup" component={EditGroupScreen} />
    <Stack.Screen name="NewGroup" component={NewGroupScreen} />
    <Stack.Screen name="MyGroups" component={MyGroupsScreen} />
    <Stack.Screen name="AddGroupMembers" component={AddGroupMembersScreen} />
    <Stack.Screen name="ContactList" component={ContactListScreen} />
    <Stack.Screen name="LinkedDevices" component={LinkedDevicesScreen} />
    <Stack.Screen name="StarredMessages" component={StarredMessagesScreen} />
    <Stack.Screen name="ArchivedChats" component={ArchivedChatsScreen} />
    <Stack.Screen name="ChatMediaGallery" component={ChatMediaGalleryScreen} options={{ headerShown: false }} />
    <Stack.Screen name="MediaViewer" component={MediaViewerScreen} options={{ headerShown: false }} />
    <Stack.Screen name="ChatBackground" component={ChatBackgroundScreen} options={{ headerShown: false }} />
    <Stack.Screen name="CallWebRTC" component={CallScreenWebRTC} options={{ headerShown: false }} />
    <Stack.Screen name="ChatSearch" component={ChatSearchScreen} options={{ headerShown: false }} />
    <Stack.Screen name="ChatNotification" component={ChatNotificationScreen} options={{ headerShown: false }} />
    <Stack.Screen name="EphemeralMessages" component={EphemeralMessagesScreen} options={{ headerShown: false }} />
    <Stack.Screen name="ChatMedia" component={ChatMediaScreen} options={{ headerShown: false }} />
    <Stack.Screen name="ContactInfo" component={ContactInfoScreen} options={{ headerShown: false }} />
    <Stack.Screen name="ChatLists" component={ChatListsScreen} options={{ headerShown: false }} />
    <Stack.Screen name="ChatLink" component={ChatLinkScreen} options={{ headerShown: false }} />
    <Stack.Screen name="BroadcastScreen" component={BroadcastScreen} options={{ headerShown: false }} />
    <Stack.Screen name="CreateBroadcastScreen" component={CreateBroadcastScreen} options={{ headerShown: false }} />
    <Stack.Screen name="AddCallParticipants" component={AddCallParticipantsScreen} options={{ headerShown: false }} />
  </Stack.Navigator>
);

// Stack per gli stati
const StatusStack = () => (
  <Stack.Navigator
    screenOptions={{
      headerStyle: {
        backgroundColor: '#1E88E5',
      },
      headerTintColor: '#FFFFFF',
      headerTitleStyle: {
        fontWeight: 'bold',
      },
      headerShown: false
    }}
  >
    <Stack.Screen name="StatusList" component={StatusScreen} options={{ title: 'Stati' }} />
    <Stack.Screen name="StatusView" component={StatusViewScreen} options={{ headerShown: false }} />
  </Stack.Navigator>
);

// Stack per le chiamate
const CallsStack = () => (
  <Stack.Navigator
    screenOptions={{
      headerStyle: {
        backgroundColor: '#1E88E5',
      },
      headerTintColor: '#FFFFFF',
      headerTitleStyle: {
        fontWeight: 'bold',
      },
      headerShown: false
    }}
  >
    <Stack.Screen name="CallsList" component={CallHistoryScreen} />
    <Stack.Screen name="Call" component={CallScreen} />
    <Stack.Screen name="VideoCall" component={VideoCallScreen} />
    <Stack.Screen name="AudioCall" component={AudioCallScreen} />
    <Stack.Screen name="CallWebRTC" component={CallScreenWebRTC} options={{ headerShown: false }} />
    <Stack.Screen name="AddCallParticipants" component={AddCallParticipantsScreen} />
    <Stack.Screen name="Contacts" component={ContactListScreen} />
  </Stack.Navigator>
);

// Stack per la sezione Earn (Guadagna)
const EarnStack = () => (
  <Stack.Navigator
    screenOptions={{
      headerStyle: {
        backgroundColor: '#1E88E5',
      },
      headerTintColor: '#FFFFFF',
      headerTitleStyle: {
        fontWeight: 'bold',
      },
      headerShown: false
    }}
  >
    <Stack.Screen name="EarnMain" component={EarnScreen} />
    <Stack.Screen name="VideoPlayer" component={VideoPlayerScreen} />
  </Stack.Navigator>
);

// Stack per le impostazioni
const SettingsStack = () => (
  <Stack.Navigator
    screenOptions={{
      headerStyle: {
        backgroundColor: '#1E88E5',
      },
      headerTintColor: '#FFFFFF',
      headerTitleStyle: {
        fontWeight: 'bold',
      },
      headerShown: false
    }}
  >
    <Stack.Screen name="SettingsList" component={SettingsScreen} />
    <Stack.Screen name="Profile" component={ProfileScreen} />
    <Stack.Screen name="EditProfile" component={EditProfileScreen} />
    <Stack.Screen name="AccountSettings" component={AccountSettings} />
    <Stack.Screen name="ChatSettings" component={ChatSettings} />
    <Stack.Screen name="NotificationSettings" component={NotificationSettings} />
    <Stack.Screen name="StorageSettings" component={StorageSettings} />
    <Stack.Screen name="LanguageSettings" component={LanguageSettings} />
    <Stack.Screen name="Privacy" component={PrivacySettingsScreen} />
    <Stack.Screen name="BlockedContacts" component={BlockedContactsScreen} />
    <Stack.Screen name="ThemeSettings" component={ThemeSettingsScreen} />
    <Stack.Screen name="Backup" component={BackupScreen} />
    <Stack.Screen name="BackupSettings" component={BackupSettingsScreen} />
    <Stack.Screen name="QRCode" component={QRCodeScreen} options={{ headerShown: false }} />
    <Stack.Screen name="QRCodeScanner" component={QRCodeScannerScreen} options={{ headerShown: false }} />
    <Stack.Screen name="TrendyWallet" component={TrendyWalletScreen} options={{ headerShown: false }} />
    <Stack.Screen name="TrendyStore" component={TrendyStoreScreen} options={{ headerShown: false }} />
  </Stack.Navigator>
);

// Stack per la sezione Live
const LiveStack = () => (
  <Stack.Navigator
    screenOptions={{
      headerStyle: {
        backgroundColor: '#1E88E5',
      },
      headerTintColor: '#FFFFFF',
      headerTitleStyle: {
        fontWeight: 'bold',
      },
      headerShown: false
    }}
  >
    <Stack.Screen name="LiveHome" component={LiveHomeScreen} />
    <Stack.Screen name="LivePreview" component={TrendyLivePreview} options={{ headerShown: false }} />
    <Stack.Screen name="LiveSetup" component={LiveSetupScreen} />
    <Stack.Screen name="LiveStudio" component={TrendyLiveStream} options={{ headerShown: false }} />
  </Stack.Navigator>
);

const MainNavigator = () => {
  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          let iconName;

          switch (route.name) {
            case 'Chat':
              iconName = focused ? 'chatbubbles' : 'chatbubbles-outline';
              break;
            case 'Aggiornamenti':
              iconName = focused ? 'radio' : 'radio-outline';
              break;
            case 'Live':
              // Per Live non usiamo un'icona standard ma un componente personalizzato
              return null;
            case 'Earn':
              iconName = focused ? 'wallet' : 'wallet-outline';
              break;
            case 'Chiamate':
              iconName = focused ? 'call' : 'call-outline';
              break;
            default:
              iconName = 'help';
          }

          return iconName ? <Ionicons name={iconName} size={size} color={color} /> : null;
        },
        tabBarActiveTintColor: '#1E88E5',
        tabBarInactiveTintColor: '#AAAAAA',
        tabBarStyle: {
          backgroundColor: '#121212',
          borderTopColor: '#2A2A2A',
          height: Platform.OS === 'ios' ? 85 : 65,
          paddingTop: 5,
          paddingBottom: Platform.OS === 'ios' ? 25 : 10,
          ...Platform.select({
            ios: {
              shadowColor: '#000',
              shadowOffset: { width: 0, height: -3 },
              shadowOpacity: 0.1,
              shadowRadius: 3,
            },
            android: {
              elevation: 8,
            },
          }),
        },
        tabBarLabelStyle: {
          fontSize: 12,
          fontWeight: '500',
          paddingBottom: Platform.OS === 'ios' ? 0 : 5,
        },
        tabBarIconStyle: {
          marginTop: Platform.OS === 'ios' ? 5 : 0,
        },
        headerShown: false,
        // Personalizzazione speciale per il tab Live
        ...(route.name === 'Live' && {
          tabBarButton: (props) => (
            <TouchableOpacity
              {...props}
              style={{
                top: -15,
                justifyContent: 'center',
                alignItems: 'center',
              }}
              onPress={() => {
                console.log('🎥 PULSANTE LIVE PREMUTO - Navigando verso LiveStack');
                console.log('📍 Destinazione: LiveHome (LiveHomeScreen)');
                console.log('🎯 Dovrebbe mostrare: Lista utenti in live con categorie');
                props.onPress();
              }}
            >
              <LinearGradient
                colors={['#1E88E5', '#D81B60']}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 1 }}
                style={{
                  width: 56,
                  height: 56,
                  borderRadius: 28,
                  justifyContent: 'center',
                  alignItems: 'center',
                  shadowColor: "#000",
                  shadowOffset: {
                    width: 0,
                    height: 2,
                  },
                  shadowOpacity: 0.25,
                  shadowRadius: 3.84,
                  elevation: 5,
                }}
              >
                <Ionicons name="videocam" size={26} color="#FFFFFF" />
              </LinearGradient>
              <Text style={{ color: '#FFFFFF', marginTop: 4, fontSize: 12, fontWeight: 'bold' }}>
                Live
              </Text>
            </TouchableOpacity>
          ),
        }),
      })}
    >
      <Tab.Screen name="Chat" component={ChatStack} />
      <Tab.Screen name="Aggiornamenti" component={StatusStack} />
      <Tab.Screen name="Live" component={LiveStack} />
      <Tab.Screen name="Earn" component={EarnStack} />
      <Tab.Screen name="Chiamate" component={CallsStack} />
      <Tab.Screen name="SettingsTab" component={SettingsStack} options={{ tabBarButton: () => null }} />
    </Tab.Navigator>
  );
};

const styles = StyleSheet.create({
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#1E88E5',
    height: 60,
    paddingHorizontal: 15,
  },
  backButton: {
    marginRight: 10,
  },
  headerTitle: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: 'bold',
    flex: 1,
  },
  rightComponent: {
    flexDirection: 'row',
  },
});

export default MainNavigator;