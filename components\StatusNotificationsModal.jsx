import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  TouchableWithoutFeedback,
  Dimensions,
  Platform,
  ScrollView,
  Switch
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import AsyncStorage from '@react-native-async-storage/async-storage';

const { width, height } = Dimensions.get('window');

const StatusNotificationsModal = ({ visible, onClose }) => {
  const [notificationSettings, setNotificationSettings] = useState({
    enableNotifications: true,
    newStoryNotifications: true,
    soundEnabled: true,
    vibrationEnabled: true,
    showPreview: true,
    quietHoursEnabled: false,
    quietHoursStart: '22:00',
    quietHoursEnd: '08:00',
    notificationSound: 'default',
    badgeCount: true
  });

  useEffect(() => {
    loadNotificationSettings();
  }, []);

  const loadNotificationSettings = async () => {
    try {
      const settings = await AsyncStorage.getItem('storyNotificationSettings');
      if (settings) {
        setNotificationSettings(JSON.parse(settings));
      }
    } catch (error) {
      console.error('Errore caricamento impostazioni notifiche:', error);
    }
  };

  const saveNotificationSettings = async (newSettings) => {
    try {
      await AsyncStorage.setItem('storyNotificationSettings', JSON.stringify(newSettings));
      setNotificationSettings(newSettings);
      console.log('✅ Impostazioni notifiche storie salvate:', newSettings);
    } catch (error) {
      console.error('❌ Errore salvataggio impostazioni notifiche:', error);
    }
  };

  const updateSetting = (key, value) => {
    const newSettings = { ...notificationSettings, [key]: value };
    saveNotificationSettings(newSettings);
  };

  const soundOptions = [
    { id: 'default', label: 'Suono predefinito', icon: 'musical-note-outline' },
    { id: 'story1', label: 'Suono storie 1', icon: 'volume-high-outline' },
    { id: 'story2', label: 'Suono storie 2', icon: 'volume-medium-outline' },
    { id: 'silent', label: 'Silenzioso', icon: 'volume-mute-outline' }
  ];

  if (!visible) return null;

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={onClose}
      statusBarTranslucent
    >
      <TouchableWithoutFeedback onPress={onClose}>
        <View style={styles.modalContainer}>
          <TouchableWithoutFeedback>
            <View style={styles.modalContent}>
              <LinearGradient
                colors={['#1E88E5', '#D81B60']}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 0 }}
                style={styles.header}
              >
                <Text style={styles.headerTitle}>Notifiche storie</Text>
                <TouchableOpacity style={styles.closeButton} onPress={onClose}>
                  <Ionicons name="close" size={24} color="#FFFFFF" />
                </TouchableOpacity>
              </LinearGradient>

              <ScrollView style={styles.scrollContainer}>
                {/* NOTIFICHE GENERALI */}
                <View style={styles.categoryContainer}>
                  <Text style={styles.categoryTitle}>Notifiche generali</Text>
                  <Text style={styles.categoryDescription}>
                    Gestisci le notifiche per le storie
                  </Text>

                  <View style={styles.switchItem}>
                    <View style={styles.switchIconContainer}>
                      <LinearGradient
                        colors={['#4CAF50', '#45A049']}
                        style={styles.optionIconGradient}
                      >
                        <Ionicons name="notifications-outline" size={18} color="#FFFFFF" />
                      </LinearGradient>
                    </View>
                    <View style={styles.switchTextContainer}>
                      <Text style={styles.optionLabel}>Abilita notifiche</Text>
                      <Text style={styles.optionDescription}>Ricevi notifiche per le storie</Text>
                    </View>
                    <Switch
                      value={notificationSettings.enableNotifications}
                      onValueChange={(value) => updateSetting('enableNotifications', value)}
                      trackColor={{ false: '#2A2A2A', true: '#1E88E5' }}
                      thumbColor={notificationSettings.enableNotifications ? '#D81B60' : '#AAAAAA'}
                    />
                  </View>

                  <View style={styles.switchItem}>
                    <View style={styles.switchIconContainer}>
                      <LinearGradient
                        colors={['#1E88E5', '#D81B60']}
                        style={styles.optionIconGradient}
                      >
                        <Ionicons name="add-circle-outline" size={18} color="#FFFFFF" />
                      </LinearGradient>
                    </View>
                    <View style={styles.switchTextContainer}>
                      <Text style={styles.optionLabel}>Nuove storie</Text>
                      <Text style={styles.optionDescription}>Notifica quando qualcuno pubblica una storia</Text>
                    </View>
                    <Switch
                      value={notificationSettings.newStoryNotifications}
                      onValueChange={(value) => updateSetting('newStoryNotifications', value)}
                      trackColor={{ false: '#2A2A2A', true: '#1E88E5' }}
                      thumbColor={notificationSettings.newStoryNotifications ? '#D81B60' : '#AAAAAA'}
                      disabled={!notificationSettings.enableNotifications}
                    />
                  </View>

                  <View style={styles.switchItem}>
                    <View style={styles.switchIconContainer}>
                      <LinearGradient
                        colors={['#FF9800', '#F57C00']}
                        style={styles.optionIconGradient}
                      >
                        <Ionicons name="eye-outline" size={18} color="#FFFFFF" />
                      </LinearGradient>
                    </View>
                    <View style={styles.switchTextContainer}>
                      <Text style={styles.optionLabel}>Mostra anteprima</Text>
                      <Text style={styles.optionDescription}>Mostra contenuto nella notifica</Text>
                    </View>
                    <Switch
                      value={notificationSettings.showPreview}
                      onValueChange={(value) => updateSetting('showPreview', value)}
                      trackColor={{ false: '#2A2A2A', true: '#1E88E5' }}
                      thumbColor={notificationSettings.showPreview ? '#D81B60' : '#AAAAAA'}
                      disabled={!notificationSettings.enableNotifications}
                    />
                  </View>

                  <View style={styles.switchItem}>
                    <View style={styles.switchIconContainer}>
                      <LinearGradient
                        colors={['#9C27B0', '#7B1FA2']}
                        style={styles.optionIconGradient}
                      >
                        <Ionicons name="radio-button-on-outline" size={18} color="#FFFFFF" />
                      </LinearGradient>
                    </View>
                    <View style={styles.switchTextContainer}>
                      <Text style={styles.optionLabel}>Badge contatore</Text>
                      <Text style={styles.optionDescription}>Mostra numero storie non viste sull'icona</Text>
                    </View>
                    <Switch
                      value={notificationSettings.badgeCount}
                      onValueChange={(value) => updateSetting('badgeCount', value)}
                      trackColor={{ false: '#2A2A2A', true: '#1E88E5' }}
                      thumbColor={notificationSettings.badgeCount ? '#D81B60' : '#AAAAAA'}
                      disabled={!notificationSettings.enableNotifications}
                    />
                  </View>
                </View>

                {/* SUONI E VIBRAZIONI */}
                <View style={styles.categoryContainer}>
                  <Text style={styles.categoryTitle}>Suoni e vibrazioni</Text>

                  <View style={styles.switchItem}>
                    <View style={styles.switchIconContainer}>
                      <LinearGradient
                        colors={['#2196F3', '#1976D2']}
                        style={styles.optionIconGradient}
                      >
                        <Ionicons name="volume-high-outline" size={18} color="#FFFFFF" />
                      </LinearGradient>
                    </View>
                    <View style={styles.switchTextContainer}>
                      <Text style={styles.optionLabel}>Suono</Text>
                      <Text style={styles.optionDescription}>Riproduci suono per le notifiche</Text>
                    </View>
                    <Switch
                      value={notificationSettings.soundEnabled}
                      onValueChange={(value) => updateSetting('soundEnabled', value)}
                      trackColor={{ false: '#2A2A2A', true: '#1E88E5' }}
                      thumbColor={notificationSettings.soundEnabled ? '#D81B60' : '#AAAAAA'}
                      disabled={!notificationSettings.enableNotifications}
                    />
                  </View>

                  <View style={styles.switchItem}>
                    <View style={styles.switchIconContainer}>
                      <LinearGradient
                        colors={['#607D8B', '#455A64']}
                        style={styles.optionIconGradient}
                      >
                        <Ionicons name="phone-portrait-outline" size={18} color="#FFFFFF" />
                      </LinearGradient>
                    </View>
                    <View style={styles.switchTextContainer}>
                      <Text style={styles.optionLabel}>Vibrazione</Text>
                      <Text style={styles.optionDescription}>Vibra per le notifiche</Text>
                    </View>
                    <Switch
                      value={notificationSettings.vibrationEnabled}
                      onValueChange={(value) => updateSetting('vibrationEnabled', value)}
                      trackColor={{ false: '#2A2A2A', true: '#1E88E5' }}
                      thumbColor={notificationSettings.vibrationEnabled ? '#D81B60' : '#AAAAAA'}
                      disabled={!notificationSettings.enableNotifications}
                    />
                  </View>

                  {/* SELEZIONE SUONO */}
                  {notificationSettings.soundEnabled && (
                    <View style={styles.subCategoryContainer}>
                      <Text style={styles.subCategoryTitle}>Suono notifica</Text>
                      {soundOptions.map((option) => (
                        <TouchableOpacity
                          key={option.id}
                          style={[
                            styles.optionItem,
                            notificationSettings.notificationSound === option.id && styles.selectedOption
                          ]}
                          onPress={() => updateSetting('notificationSound', option.id)}
                        >
                          <View style={styles.optionIconContainer}>
                            <LinearGradient
                              colors={['#1E88E5', '#D81B60']}
                              style={styles.smallIconGradient}
                            >
                              <Ionicons name={option.icon} size={16} color="#FFFFFF" />
                            </LinearGradient>
                          </View>
                          <View style={styles.optionTextContainer}>
                            <Text style={styles.smallOptionLabel}>{option.label}</Text>
                          </View>
                          <View style={styles.radioContainer}>
                            <LinearGradient
                              colors={notificationSettings.notificationSound === option.id 
                                ? ['#1E88E5', '#D81B60'] 
                                : ['#2A2A2A', '#2A2A2A']
                              }
                              style={styles.smallRadioButton}
                            >
                              {notificationSettings.notificationSound === option.id && (
                                <Ionicons name="checkmark" size={12} color="#FFFFFF" />
                              )}
                            </LinearGradient>
                          </View>
                        </TouchableOpacity>
                      ))}
                    </View>
                  )}
                </View>

                {/* ORE SILENZIOSE */}
                <View style={styles.categoryContainer}>
                  <Text style={styles.categoryTitle}>Ore silenziose</Text>

                  <View style={styles.switchItem}>
                    <View style={styles.switchIconContainer}>
                      <LinearGradient
                        colors={['#795548', '#5D4037']}
                        style={styles.optionIconGradient}
                      >
                        <Ionicons name="moon-outline" size={18} color="#FFFFFF" />
                      </LinearGradient>
                    </View>
                    <View style={styles.switchTextContainer}>
                      <Text style={styles.optionLabel}>Abilita ore silenziose</Text>
                      <Text style={styles.optionDescription}>Non disturbare in certi orari</Text>
                    </View>
                    <Switch
                      value={notificationSettings.quietHoursEnabled}
                      onValueChange={(value) => updateSetting('quietHoursEnabled', value)}
                      trackColor={{ false: '#2A2A2A', true: '#1E88E5' }}
                      thumbColor={notificationSettings.quietHoursEnabled ? '#D81B60' : '#AAAAAA'}
                      disabled={!notificationSettings.enableNotifications}
                    />
                  </View>

                  {notificationSettings.quietHoursEnabled && (
                    <View style={styles.timeContainer}>
                      <TouchableOpacity
                        style={styles.timeItem}
                        onPress={() => alert('Selezione orario in arrivo')}
                      >
                        <Text style={styles.timeLabel}>Inizio</Text>
                        <Text style={styles.timeValue}>{notificationSettings.quietHoursStart}</Text>
                      </TouchableOpacity>
                      <TouchableOpacity
                        style={styles.timeItem}
                        onPress={() => alert('Selezione orario in arrivo')}
                      >
                        <Text style={styles.timeLabel}>Fine</Text>
                        <Text style={styles.timeValue}>{notificationSettings.quietHoursEnd}</Text>
                      </TouchableOpacity>
                    </View>
                  )}
                </View>
              </ScrollView>
            </View>
          </TouchableWithoutFeedback>
        </View>
      </TouchableWithoutFeedback>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
  },
  modalContent: {
    width: width * 0.9,
    maxHeight: height * 0.9,
    backgroundColor: '#1E1E1E',
    borderRadius: 20,
    overflow: 'hidden',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 16,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  closeButton: {
    padding: 8,
  },
  scrollContainer: {
    maxHeight: height * 0.8,
  },
  categoryContainer: {
    marginBottom: 24,
    paddingHorizontal: 16,
  },
  categoryTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 4,
  },
  categoryDescription: {
    fontSize: 14,
    color: '#AAAAAA',
    marginBottom: 16,
    lineHeight: 20,
  },
  switchItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#2A2A2A',
  },
  switchIconContainer: {
    marginRight: 16,
  },
  optionIconGradient: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  switchTextContainer: {
    flex: 1,
  },
  optionLabel: {
    fontSize: 16,
    color: '#FFFFFF',
    marginBottom: 2,
  },
  optionDescription: {
    fontSize: 12,
    color: '#AAAAAA',
  },
  subCategoryContainer: {
    marginTop: 16,
    paddingLeft: 16,
    borderLeftWidth: 2,
    borderLeftColor: '#1E88E5',
  },
  subCategoryTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#AAAAAA',
    marginBottom: 12,
  },
  optionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
  },
  selectedOption: {
    backgroundColor: 'rgba(30, 136, 229, 0.1)',
    borderRadius: 8,
    marginHorizontal: -8,
    paddingHorizontal: 8,
  },
  optionIconContainer: {
    marginRight: 12,
  },
  smallIconGradient: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  optionTextContainer: {
    flex: 1,
  },
  smallOptionLabel: {
    fontSize: 14,
    color: '#FFFFFF',
  },
  radioContainer: {
    marginLeft: 8,
  },
  smallRadioButton: {
    width: 20,
    height: 20,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
  },
  timeContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginTop: 16,
  },
  timeItem: {
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#2A2A2A',
    borderRadius: 12,
    minWidth: 100,
  },
  timeLabel: {
    fontSize: 12,
    color: '#AAAAAA',
    marginBottom: 4,
  },
  timeValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
});

export default StatusNotificationsModal;
