import AsyncStorage from '@react-native-async-storage/async-storage';
import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  Image,
  ActivityIndicator,
  SafeAreaView,
  StatusBar,
  Platform,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { useNavigation } from '@react-navigation/native';
import { useTheme } from '../hooks/useTheme';
import useCommunityStore from '../store/communityStore';
import useAuthStore from '../store/authStore';
import LottieView from 'lottie-react-native';

const CommunityScreen = () => {
  const navigation = useNavigation();
  const { theme } = useTheme();
  const [communities, setCommunities] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    loadCommunities();
  }, []);

  const loadCommunities = async () => {
    try {
      setLoading(true);
      const { user } = useAuthStore.getState();

      if (!user) {
        setCommunities([]);
        setLoading(false);
        setRefreshing(false);
        return;
      }

      // Usa lo store per caricare le community
      const communityStore = useCommunityStore.getState();
      await communityStore.loadCommunities();
      const communitiesData = communityStore.communities;

      // Aggiungi il conteggio dei gruppi a ogni community
      const communitiesWithGroups = communitiesData.map(community => {
        return {
          ...community,
          groupsCount: community.groups ? community.groups.length : 0,
        };
      });

      setCommunities(communitiesWithGroups);
    } catch (error) {
      console.error('Errore nel caricamento delle community:', error);
      Alert.alert('Errore', 'Impossibile caricare le community');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const handleRefresh = () => {
    setRefreshing(true);
    loadCommunities();
  };

  const handleCreateCommunity = () => {
    navigation.navigate('NewCommunity');
  };

  const handleCommunityPress = (community) => {
    navigation.navigate('CommunityDetails', { communityId: community.id });
  };

  const renderCommunityItem = ({ item }) => (
    <TouchableOpacity
      style={[styles.communityItem, { backgroundColor: '#252525' }]}
      onPress={() => handleCommunityPress(item)}
    >
      <View style={styles.communityImageContainer}>
        {item.image ? (
          <Image source={{ uri: item.image }} style={styles.communityImage} />
        ) : (
          <View style={[styles.communityImagePlaceholder, { backgroundColor: theme.colors.primary }]}>
            <Text style={styles.communityImagePlaceholderText}>
              {item.name.substring(0, 2).toUpperCase()}
            </Text>
          </View>
        )}
      </View>

      <View style={styles.communityInfo}>
        <Text style={[styles.communityName, { color: '#FFFFFF' }]}>
          {item.name}
        </Text>
        <Text style={[styles.communityDescription, { color: '#AAAAAA' }]}>
          {item.groupsCount} {item.groupsCount === 1 ? 'gruppo' : 'gruppi'}
        </Text>
      </View>

      <Ionicons name="chevron-forward" size={20} color="#AAAAAA" />
    </TouchableOpacity>
  );

  const renderEmptyComponent = () => (
    <View style={styles.emptyContainer}>
      <LottieView
        source={require('../assets/animations/empty-community.json')}
        style={styles.emptyAnimation}
        autoPlay
        loop
      />
      <Text style={[styles.emptyText, { color: '#FFFFFF' }]}>
        Non sei ancora in nessuna community
      </Text>
      <Text style={[styles.emptySubtext, { color: '#AAAAAA' }]}>
        Le community ti permettono di organizzare i tuoi gruppi correlati
      </Text>
      <TouchableOpacity
        style={[styles.createButton, { backgroundColor: '#1E88E5' }]}
        onPress={handleCreateCommunity}
      >
        <Text style={styles.createButtonText}>Crea una community</Text>
      </TouchableOpacity>
    </View>
  );

  return (
    <View style={[styles.container, { backgroundColor: '#1E1E1E' }]}>
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent={true} />

      <LinearGradient
        colors={['#1E88E5', '#D81B60']}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 0 }}
        style={styles.header}
      >
        <Text style={styles.headerTitle}>Community</Text>

        <TouchableOpacity
          style={styles.headerButton}
          onPress={handleCreateCommunity}
        >
          <Ionicons name="add" size={24} color="#FFFFFF" />
        </TouchableOpacity>
      </LinearGradient>

      {loading && !refreshing ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#1E88E5" />
        </View>
      ) : (
        <FlatList
          data={communities}
          renderItem={renderCommunityItem}
          keyExtractor={item => item.id}
          contentContainerStyle={styles.listContent}
          ListEmptyComponent={renderEmptyComponent}
          refreshing={refreshing}
          onRefresh={handleRefresh}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingTop: Platform.OS === 'ios' ? 50 : 40,
    paddingBottom: 16,
    height: Platform.OS === 'ios' ? 110 : 100,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  headerButton: {
    padding: 8,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  listContent: {
    padding: 16,
    flexGrow: 1,
  },
  communityItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 12,
    marginBottom: 12,
  },
  communityImageContainer: {
    marginRight: 12,
  },
  communityImage: {
    width: 50,
    height: 50,
    borderRadius: 25,
  },
  communityImagePlaceholder: {
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
  },
  communityImagePlaceholderText: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: 'bold',
  },
  communityInfo: {
    flex: 1,
  },
  communityName: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  communityDescription: {
    fontSize: 14,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyAnimation: {
    width: 200,
    height: 200,
    marginBottom: 20,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 8,
  },
  emptySubtext: {
    fontSize: 14,
    textAlign: 'center',
    marginBottom: 24,
  },
  createButton: {
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 24,
  },
  createButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '500',
  },
});

export default CommunityScreen;
