import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  TextInput,
  Image,
  ActivityIndicator,
  Alert,
  SafeAreaView,
  StatusBar,
  Platform,
} from 'react-native';
import { useRoute, useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import useContactStore from '../store/contactStore';
import useGroupStore from '../store/groupStore';
import contactSearchService from '../services/contactSearchService';

const AddGroupMembersScreen = () => {
  const route = useRoute();
  const navigation = useNavigation();
  const { groupId } = route.params;
  const { contacts, loading: contactsLoading } = useContactStore();
  const { currentGroup, addMembers, loading: groupLoading } = useGroupStore();
  const [selectedContacts, setSelectedContacts] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [isAdding, setIsAdding] = useState(false);

  // 🔍 STATI PER RICERCA UTENTI (IDENTICI ALLA HOMESCREEN)
  const [userSearchResults, setUserSearchResults] = useState([]);
  const [isSearchingUsers, setIsSearchingUsers] = useState(false);
  const [showUserResults, setShowUserResults] = useState(false);

  // Filtra i contatti in base alla query di ricerca e rimuovi quelli già nel gruppo
  const filteredContacts = contacts.filter(
    contact =>
      contact.isRegistered &&
      !currentGroup?.members?.some(member => member.id === contact.id) &&
      contact.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // 🔍 RICERCA UTENTI PER NUMERO DI TELEFONO (IDENTICA ALLA HOMESCREEN)
  useEffect(() => {
    const searchUsers = async () => {
      // Verifica se la query è un numero di telefono
      if (searchQuery.trim() && contactSearchService.isPhoneNumber(searchQuery)) {
        setIsSearchingUsers(true);
        setShowUserResults(true);

        try {
          const results = await contactSearchService.searchUsers(searchQuery);
          setUserSearchResults(results);
        } catch (error) {
          console.error('Errore nella ricerca degli utenti:', error);
          setUserSearchResults([]);
        } finally {
          setIsSearchingUsers(false);
        }
      } else {
        setShowUserResults(false);
        setUserSearchResults([]);
      }
    };

    // Esegui la ricerca con un piccolo ritardo per evitare troppe chiamate API
    const timeoutId = setTimeout(searchUsers, 500);

    return () => clearTimeout(timeoutId);
  }, [searchQuery]);

  const handleSelectContact = (contact) => {
    if (selectedContacts.some(c => c.id === contact.id)) {
      setSelectedContacts(selectedContacts.filter(c => c.id !== contact.id));
    } else {
      setSelectedContacts([...selectedContacts, contact]);
    }
  };

  const handleAddMembers = async () => {
    if (selectedContacts.length === 0) {
      Alert.alert('Errore', 'Seleziona almeno un contatto');
      return;
    }

    try {
      setIsAdding(true);
      const memberIds = selectedContacts.map(contact => contact.id);
      await addMembers(groupId, memberIds);

      Alert.alert(
        'Membri aggiunti',
        `${selectedContacts.length} nuovi membri aggiunti al gruppo`,
        [
          {
            text: 'OK',
            onPress: () => navigation.goBack()
          }
        ]
      );
    } catch (error) {
      console.error("Errore nell'aggiunta dei membri:", error);
      Alert.alert('Errore', 'Impossibile aggiungere i membri al gruppo');
    } finally {
      setIsAdding(false);
    }
  };

  // 🔍 GESTISCE LA SELEZIONE DI UN UTENTE TROVATO (IDENTICA ALLA HOMESCREEN)
  const handleUserPress = (user) => {
    console.log('👤 AddGroupMembers: Selezionando utente:', user.displayName);

    // Verifica se l'utente è già nel gruppo
    const isAlreadyInGroup = currentGroup?.members?.some(member =>
      member === user.id || member === user._id
    );

    if (isAlreadyInGroup) {
      Alert.alert('Info', 'Questo utente è già nel gruppo.');
      return;
    }

    // Verifica se l'utente è già selezionato
    const isAlreadySelected = selectedContacts.some(contact =>
      contact.id === user.id || contact.id === user._id
    );

    if (isAlreadySelected) {
      Alert.alert('Info', 'Questo utente è già selezionato.');
      return;
    }

    // Aggiungi l'utente ai contatti selezionati
    const contactToAdd = {
      id: user.id || user._id,
      name: user.displayName,
      photoURL: user.photoURL,
      phoneNumber: user.phoneNumber,
      isRegistered: true,
      online: user.online || false
    };

    setSelectedContacts(prev => [...prev, contactToAdd]);

    // Pulisci la ricerca
    setSearchQuery('');
    setShowUserResults(false);
    setUserSearchResults([]);

    Alert.alert('Successo', `${user.displayName} aggiunto alla selezione.`);
  };

  // 🔍 PULISCE LA RICERCA (IDENTICA ALLA HOMESCREEN)
  const clearSearch = () => {
    setSearchQuery('');
    setShowUserResults(false);
    setUserSearchResults([]);
  };

  const renderContact = ({ item }) => {
    const isSelected = selectedContacts.some(c => c.id === item.id);

    return (
      <TouchableOpacity
        style={[styles.contactItem, isSelected && styles.selectedItem]}
        onPress={() => handleSelectContact(item)}
      >
        <View style={styles.checkboxContainer}>
          {isSelected ? (
            <View style={styles.checkbox}>
              <Ionicons name="checkmark" size={16} color="#FFFFFF" />
            </View>
          ) : (
            <View style={styles.emptyCheckbox} />
          )}
        </View>

        <Image
          source={{ uri: item.photoURL || 'https://via.placeholder.com/50' }}
          style={styles.contactImage}
        />

        <View style={styles.contactInfo}>
          <Text style={styles.contactName}>{item.name}</Text>
          <Text style={styles.contactStatus}>
            {item.online ? 'Online' : 'Ultimo accesso recente'}
          </Text>
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#1E88E5" />

      <LinearGradient
        colors={['#1E88E5', '#D81B60']}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 0 }}
        style={styles.header}
      >
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color="#FFFFFF" />
        </TouchableOpacity>

        <Text style={styles.headerTitle}>Aggiungi partecipanti</Text>

        {selectedContacts.length > 0 && (
          <TouchableOpacity
            style={styles.addButton}
            onPress={handleAddMembers}
            disabled={isAdding}
          >
            {isAdding ? (
              <ActivityIndicator size="small" color="#FFFFFF" />
            ) : (
              <Ionicons name="checkmark" size={24} color="#FFFFFF" />
            )}
          </TouchableOpacity>
        )}
      </LinearGradient>

      {selectedContacts.length > 0 && (
        <View style={styles.selectedContactsContainer}>
          <Text style={styles.selectedCountText}>
            {selectedContacts.length} contatti selezionati
          </Text>
        </View>
      )}

      <View style={styles.searchContainer}>
        <Ionicons name="search" size={20} color="#AAAAAA" />
        <TextInput
          style={styles.searchInput}
          placeholder="Cerca contatti"
          placeholderTextColor="#AAAAAA"
          value={searchQuery}
          onChangeText={setSearchQuery}
        />
        {searchQuery.length > 0 && (
          <TouchableOpacity onPress={clearSearch}>
            <Ionicons name="close-circle" size={20} color="#AAAAAA" />
          </TouchableOpacity>
        )}
      </View>

      {/* 🔍 RISULTATI RICERCA UTENTI (IDENTICI ALLA HOMESCREEN) */}
      {showUserResults && searchQuery.trim() && (
        <View style={styles.userSearchResultsContainer}>
          {isSearchingUsers ? (
            <View style={styles.userSearchLoadingContainer}>
              <ActivityIndicator size="small" color="#1E88E5" />
              <Text style={styles.userSearchLoadingText}>Ricerca utenti...</Text>
            </View>
          ) : userSearchResults.length > 0 ? (
            <>
              <Text style={styles.userSearchResultsTitle}>Utenti trovati</Text>
              {userSearchResults.map(user => (
                <TouchableOpacity
                  key={user.id || user._id}
                  style={styles.userSearchResultItem}
                  onPress={() => handleUserPress(user)}
                >
                  {user.photoURL ? (
                    <Image source={{ uri: user.photoURL }} style={styles.userSearchAvatar} />
                  ) : (
                    <LinearGradient
                      colors={['#1E88E5', '#D81B60']}
                      style={[styles.userSearchAvatar, styles.defaultAvatar]}
                    >
                      <Text style={styles.avatarText}>
                        {(user.displayName || 'U').charAt(0).toUpperCase()}
                      </Text>
                    </LinearGradient>
                  )}
                  <View style={styles.userSearchInfo}>
                    <Text style={styles.userSearchName}>{user.displayName}</Text>
                    <Text style={styles.userSearchPhone}>{user.phoneNumber}</Text>
                  </View>
                  <TouchableOpacity
                    style={styles.userSearchAddButton}
                    onPress={() => handleUserPress(user)}
                  >
                    <Ionicons name="person-add" size={20} color="#1E88E5" />
                  </TouchableOpacity>
                </TouchableOpacity>
              ))}
            </>
          ) : contactSearchService.isPhoneNumber(searchQuery) ? (
            <View style={styles.userSearchEmptyContainer}>
              <Ionicons name="person-outline" size={40} color="#AAAAAA" />
              <Text style={styles.userSearchEmptyText}>
                Nessun utente trovato con il numero {searchQuery}
              </Text>
              <Text style={styles.userSearchEmptySubtext}>
                Invita i tuoi amici a unirsi a TrendyChat!
              </Text>
            </View>
          ) : null}
        </View>
      )}

      {contactsLoading || groupLoading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#1E88E5" />
        </View>
      ) : (
        <FlatList
          data={filteredContacts}
          renderItem={renderContact}
          keyExtractor={item => item.id}
          contentContainerStyle={styles.contactsList}
          ListEmptyComponent={
            <View style={styles.emptyContainer}>
              <Ionicons name="people" size={64} color="#AAAAAA" />
              <Text style={styles.emptyText}>
                Nessun contatto disponibile
              </Text>
              <Text style={styles.emptySubtext}>
                Tutti i tuoi contatti sono già nel gruppo o non hai contatti registrati
              </Text>
            </View>
          }
        />
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#121212',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingTop: Platform.OS === 'ios' ? 50 : 30,
    paddingBottom: 10,
    height: Platform.OS === 'ios' ? 110 : 100,
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFFFFF',
    flex: 1,
    marginLeft: 8,
  },
  addButton: {
    padding: 8,
  },
  selectedContactsContainer: {
    padding: 12,
    backgroundColor: '#1E1E1E',
    borderBottomWidth: 1,
    borderBottomColor: '#2A2A2A',
  },
  selectedCountText: {
    fontSize: 14,
    color: '#1E88E5',
    fontWeight: 'bold',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#1E1E1E',
    borderBottomWidth: 1,
    borderBottomColor: '#2A2A2A',
    marginHorizontal: 0,
  },
  searchInput: {
    flex: 1,
    marginLeft: 12,
    marginRight: 8,
    color: '#FFFFFF',
    fontSize: 16,
    height: 40,
    paddingVertical: 8,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  contactsList: {
    paddingVertical: 8,
  },
  contactItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    backgroundColor: '#1E1E1E',
  },
  selectedItem: {
    backgroundColor: '#2A2A2A',
  },
  checkboxContainer: {
    width: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  checkbox: {
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: '#1E88E5',
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyCheckbox: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 1,
    borderColor: '#AAAAAA',
  },
  contactImage: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: 12,
  },
  contactInfo: {
    flex: 1,
  },
  contactName: {
    fontSize: 16,
    color: '#FFFFFF',
  },
  contactStatus: {
    fontSize: 14,
    color: '#AAAAAA',
    marginTop: 2,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    marginTop: 50,
  },
  emptyText: {
    fontSize: 16,
    color: '#FFFFFF',
    marginTop: 16,
    textAlign: 'center',
  },
  emptySubtext: {
    fontSize: 14,
    color: '#AAAAAA',
    marginTop: 8,
    textAlign: 'center',
  },
  // 🔍 STILI PER RICERCA UTENTI (IDENTICI ALLA HOMESCREEN)
  userSearchResultsContainer: {
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    marginHorizontal: 16,
    marginTop: 8,
    marginBottom: 8,
    padding: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 1.41,
  },
  userSearchLoadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
  },
  userSearchLoadingText: {
    marginLeft: 8,
    fontSize: 14,
    color: '#666666',
  },
  userSearchResultsTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333333',
    marginBottom: 12,
  },
  userSearchResultItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  userSearchAvatar: {
    width: 48,
    height: 48,
    borderRadius: 24,
  },
  defaultAvatar: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatarText: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: 'bold',
  },
  userSearchInfo: {
    flex: 1,
    marginLeft: 12,
  },
  userSearchName: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333333',
  },
  userSearchPhone: {
    fontSize: 14,
    color: '#666666',
    marginTop: 2,
  },
  userSearchAddButton: {
    padding: 8,
  },
  userSearchEmptyContainer: {
    alignItems: 'center',
    padding: 16,
  },
  userSearchEmptyText: {
    fontSize: 14,
    color: '#666666',
    textAlign: 'center',
    marginTop: 8,
  },
  userSearchEmptySubtext: {
    fontSize: 12,
    color: '#999999',
    textAlign: 'center',
    marginTop: 4,
  },
});

export default AddGroupMembersScreen;
