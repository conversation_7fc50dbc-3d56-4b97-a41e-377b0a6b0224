import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  KeyboardAvoidingView,
  Platform,
  SafeAreaView,
  StatusBar,
  Dimensions,
} from 'react-native';
import { useRoute, useNavigation } from '@react-navigation/native';
import { LinearGradient } from 'expo-linear-gradient';
import { Audio } from 'expo-av';
import * as ImagePicker from 'expo-image-picker';
import * as DocumentPicker from 'expo-document-picker';
import * as FileSystem from 'expo-file-system';
import * as VideoThumbnails from 'expo-video-thumbnails';

// Importiamo gli stessi componenti della chat utenti
import ChatHeader from '../components/ChatHeader';
import ChatBubble from '../components/ChatBubble';
import InputBox from '../components/InputBox';
import AttachmentMenu from '../components/AttachmentMenu';
import TypingIndicator from '../components/chat/TypingIndicator';
import RecordingManager from '../utils/RecordingManager';

// Importiamo gli stessi store della chat utenti
import useGroupStore from '../store/groupStore';
import useAuthStore from '../store/authStore';
import useWebRTCStore from '../store/webrtcStore';
import { io } from 'socket.io-client';

const { width, height } = Dimensions.get('window');

const GroupChatScreen = () => {
  const route = useRoute();
  const navigation = useNavigation();
  const { groupId, name, avatar } = route.params;
  const { user } = useAuthStore();

  // Usiamo gli stessi hook della chat utenti
  const {
    currentGroup,
    loading,
    error,
    loadMessages,
    sendMessage,
    sendImage,
    sendVideo,
    sendDocument,
    sendAudioMessage,
    deleteMessageForEveryone,
    deleteMessageForMe
  } = useGroupStore();

  // WebRTC per chiamate di gruppo (stesso sistema della chat utenti)
  // 🚀 Rimosso useWebRTCStore - ora usa architettura ibrida

  // Stati locali (stessi della chat utenti)
  const [isTyping, setIsTyping] = useState(false);
  const [loadingMedia, setLoadingMedia] = useState(false);
  const [replyTo, setReplyTo] = useState(null);
  const [showAttachmentMenu, setShowAttachmentMenu] = useState(false);
  const flatListRef = useRef(null);

  // 📡 WEBSOCKET STATI GRUPPO
  const [socket, setSocket] = useState(null);
  const [groupTypingUsers, setGroupTypingUsers] = useState([]); // Array di utenti che stanno scrivendo
  const [groupRecordingUsers, setGroupRecordingUsers] = useState([]); // Array di utenti che stanno registrando
  const [groupOnlineUsers, setGroupOnlineUsers] = useState([]); // Array di utenti online nel gruppo

  // Carica i messaggi del gruppo all'avvio
  useEffect(() => {
    console.log('🔄 GroupChatScreen: Caricamento messaggi per gruppo:', groupId);
    loadMessages(groupId);
    // 📡 CONNETTI WEBSOCKET PER STATI GRUPPO
    connectGroupWebSocket();
  }, [groupId]);

  // 📡 CONNESSIONE WEBSOCKET PER STATI GRUPPO
  const connectGroupWebSocket = () => {
    if (socket) {
      socket.disconnect();
    }

    const newSocket = io('http://192.168.1.66:3001');

    newSocket.on('connect', () => {

      // Autentica per gruppo
      newSocket.emit('join_group', {
        groupId,
        userId: user?.id || user?.uid,
        userName: user?.displayName || 'Utente'
      });

      // Imposta te stesso come online
      setGroupOnlineUsers(prev => {
        const myUserId = user?.id || user?.uid;
        if (!prev.find(u => u.userId === myUserId)) {
          return [...prev, { userId: myUserId, userName: user?.displayName || 'Tu' }];
        }
        return prev;
      });
    });

    // Ricevi lista utenti online gruppo
    newSocket.on('group_online_users', (data) => {
      if (data.groupId === groupId) {
        setGroupOnlineUsers(data.users || []);
      }
    });

    // Ricevi utente che entra nel gruppo
    newSocket.on('group_user_joined', (data) => {
      if (data.groupId === groupId && data.userId !== (user?.id || user?.uid)) {
        setGroupOnlineUsers(prev => {
          if (!prev.find(u => u.userId === data.userId)) {
            return [...prev, { userId: data.userId, userName: data.userName }];
          }
          return prev;
        });
      }
    });

    // Ricevi utente che esce dal gruppo
    newSocket.on('group_user_left', (data) => {
      if (data.groupId === groupId) {
        setGroupOnlineUsers(prev => prev.filter(u => u.userId !== data.userId));
      }
    });

    // Ricevi stati typing gruppo
    newSocket.on('group_user_typing', (data) => {
      console.log('📨 GRUPPO - Ricevuto group_user_typing:', data);
      console.log('🔍 GRUPPO - DEBUG group_user_typing:', {
        dataGroupId: data.groupId,
        groupIdCorrente: groupId,
        dataUserId: data.userId,
        mioUserId: user?.id || user?.uid,
        èPerQuestoGruppo: data.groupId === groupId,
        èAltroUtente: data.userId !== (user?.id || user?.uid)
      });

      if (data.groupId === groupId && data.userId !== (user?.id || user?.uid)) {
        if (data.isTyping) {
          // Aggiungi utente alla lista typing
          setGroupTypingUsers(prev => {
            if (!prev.find(u => u.userId === data.userId)) {
              console.log('✅ GRUPPO - Utente aggiunto a typing:', data.userId, data.userName);
              return [...prev, { userId: data.userId, userName: data.userName }];
            }
            return prev;
          });
          // Rimuovi dopo 5 secondi
          setTimeout(() => {
            console.log('⏰ GRUPPO - Timeout: rimuovo typing per', data.userId);
            setGroupTypingUsers(prev => prev.filter(u => u.userId !== data.userId));
          }, 5000);
        } else {
          // Rimuovi utente dalla lista typing
          console.log('🛑 GRUPPO - Utente rimosso da typing:', data.userId);
          setGroupTypingUsers(prev => prev.filter(u => u.userId !== data.userId));
        }
      }
    });

    // Ricevi stati recording gruppo
    newSocket.on('group_user_recording', (data) => {
      console.log('🎤 Ricevuto recording gruppo:', data);
      if (data.groupId === groupId && data.userId !== (user?.id || user?.uid)) {
        if (data.isRecording) {
          // Aggiungi utente alla lista recording
          setGroupRecordingUsers(prev => {
            if (!prev.find(u => u.userId === data.userId)) {
              return [...prev, { userId: data.userId, userName: data.userName }];
            }
            return prev;
          });
        } else {
          // Rimuovi utente dalla lista recording
          setGroupRecordingUsers(prev => prev.filter(u => u.userId !== data.userId));
        }
      }
    });

    newSocket.on('disconnect', () => {
      console.log('🔴 WebSocket gruppo disconnesso');
    });

    setSocket(newSocket);
  };

  // 🧹 CLEANUP WEBSOCKET GRUPPO
  useEffect(() => {
    return () => {
      console.log('🚪 USCENDO DAL GRUPPO:', groupId);
      console.log('👤 Utente che esce:', user?.displayName || 'Sconosciuto');
      console.log('⏰ Timestamp uscita:', new Date().toLocaleTimeString());

      if (socket) {
        console.log('🧹 Disconnettendo WebSocket gruppo...');
        socket.emit('leave_group', {
          groupId,
          userId: user?.id || user?.uid,
          userName: user?.displayName || 'Utente'
        });
        socket.disconnect();
        console.log('📡 Evento leave_group inviato');
      }
    };
  }, [socket, groupId, user]);





  // Handler per inviare messaggio di testo (IDENTICO ALLA CHAT UTENTI)
  const handleSend = async (text) => {
    if (!text.trim()) return;

    // ✅ Sistema gruppo production-ready

    try {
      await sendMessage(groupId, text.trim());

      // Con FlatList invertito, scrollToIndex(0) porta all'ultimo messaggio (CON CONTROLLO)
      if (currentGroup?.messages && currentGroup.messages.length > 0) {
        flatListRef.current?.scrollToIndex({ index: 0, animated: true });
      }
    } catch (error) {
      console.error('❌ Errore invio messaggio gruppo:', error);
      Alert.alert('Errore', 'Impossibile inviare il messaggio');
    }
  };

  // Handler per allegati (stesso della chat utenti)
  const handleAttachment = () => {
    setShowAttachmentMenu(true);
  };

  const handleSelectMedia = async (media) => {
    try {
      setLoadingMedia(true);

      switch (media.type) {
        case 'image':
          await sendImage(groupId, media.uri);
          break;
        case 'video':
          // Genera thumbnail per video da AttachmentMenu
          try {
            console.log('🎬 Generando thumbnail per video da menu:', media.uri);
            const { uri: thumbnailUri } = await VideoThumbnails.getThumbnailAsync(media.uri, {
              time: 1000,
              quality: 0.8,
            });
            console.log('✅ Thumbnail generata da menu:', thumbnailUri);
            await sendVideo(groupId, media.uri, thumbnailUri);
          } catch (thumbnailError) {
            console.error('❌ Errore generazione thumbnail da menu:', thumbnailError);
            await sendVideo(groupId, media.uri, null);
          }
          break;
        case 'document':
          await sendDocument(groupId, media.uri);
          break;
        case 'contact_picker':
          // Naviga alla selezione contatti per gruppo
          navigation.navigate('ContactPicker', {
            onSelectContact: (contact) => {
              sendMessage(groupId, `👤 Contatto: ${contact.name}\n📞 ${contact.phone}`);
            }
          });
          break;
        case 'location':
          // Invia messaggio con posizione al gruppo
          await sendMessage(groupId, `📍 Posizione condivisa\nLat: ${media.latitude}\nLng: ${media.longitude}`);
          break;
        default:
          console.log('Tipo media non supportato:', media.type);
      }
    } catch (error) {
      console.error('Errore nell\'invio del media al gruppo:', error);
      Alert.alert('Errore', 'Impossibile inviare il media');
    } finally {
      setLoadingMedia(false);
    }
  };

  // Handler per immagini (stesso della chat utenti)
  const handleImagePick = async () => {
    try {
      setLoadingMedia(true);
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: 'images',
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        console.log('📷 Inviando immagine al gruppo');
        await sendImage(groupId, result.assets[0].uri);
      }
    } catch (error) {
      console.error('❌ Errore invio immagine gruppo:', error);
      Alert.alert('Errore', 'Impossibile inviare l\'immagine');
    } finally {
      setLoadingMedia(false);
    }
  };

  // Handler per video (IDENTICO ALLA CHAT UTENTI CON THUMBNAIL)
  const handleVideoPick = async () => {
    try {
      setLoadingMedia(true);
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: 'videos',
        allowsEditing: true,
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        const asset = result.assets[0];

        // Genera una vera thumbnail dal video (IDENTICO AL CHATSTORE)
        console.log('🎬 Generando thumbnail per video gruppo:', asset.uri);
        try {
          const { uri: thumbnailUri } = await VideoThumbnails.getThumbnailAsync(asset.uri, {
            time: 1000, // Prendi il frame a 1 secondo
            quality: 0.8,
          });
          console.log('✅ Thumbnail generata per gruppo:', thumbnailUri);
          await sendVideo(groupId, asset.uri, thumbnailUri);
        } catch (thumbnailError) {
          console.error('❌ Errore generazione thumbnail gruppo:', thumbnailError);
          // Fallback: invia senza thumbnail
          await sendVideo(groupId, asset.uri, null);
        }
      }
    } catch (error) {
      console.error('❌ Errore invio video gruppo:', error);
      Alert.alert('Errore', 'Impossibile inviare il video');
    } finally {
      setLoadingMedia(false);
    }
  };

  // Handler per documenti (IDENTICO ALLA CHAT UTENTI)
  const handleDocumentPick = async () => {
    try {
      setLoadingMedia(true);
      console.log('📄 Aprendo DocumentPicker...');

      const result = await DocumentPicker.getDocumentAsync({
        type: '*/*',
        copyToCacheDirectory: true,
      });

      console.log('📄 Risultato DocumentPicker:', result);

      if (!result.canceled && result.assets && result.assets.length > 0) {
        const asset = result.assets[0];
        console.log('📄 Documento selezionato:', asset);

        const fileInfo = await FileSystem.getInfoAsync(asset.uri);
        console.log('📄 Info file:', fileInfo);

        const userId = user.id || user.uid;
        console.log('📤 Inviando documento per gruppo:', groupId);

        await sendDocument(
          groupId,
          asset.uri,
          asset.name,
          fileInfo.size || 0,
          asset.mimeType || 'application/octet-stream'
        );

        console.log('✅ Documento inviato con successo');
      } else {
        console.log('📄 Selezione documento annullata o fallita');
      }
    } catch (error) {
      console.error('❌ Errore invio documento gruppo:', error);
      Alert.alert('Errore', 'Impossibile inviare il documento');
    } finally {
      setLoadingMedia(false);
    }
  };



  // Handler per messaggi vocali (IDENTICO ALLA CHAT UTENTI)
  const startRecording = async (uri) => {
    try {
      console.log('🎤 GroupChatScreen: Ricevuto URI audio:', uri ? 'presente' : 'assente');

      if (!uri) {
        console.log('❌ GroupChatScreen: URI audio mancante');
        return;
      }

      const info = await FileSystem.getInfoAsync(uri);
      if (!info.exists) {
        console.log('❌ GroupChatScreen: File audio non esiste');
        return;
      }

      // Se la registrazione è troppo breve (meno di 1 secondo), non inviarla
      if (info.size < 1000) {
        Alert.alert('Registrazione troppo breve', 'La registrazione audio è troppo breve');
        return;
      }

      const userId = user.id || user.uid;
      console.log('📤 GroupChatScreen: Invio messaggio audio per gruppo:', userId);
      await sendAudioMessage(groupId, userId, uri);

      // Con FlatList invertito, scrollToIndex(0) porta all'ultimo messaggio (CON CONTROLLO)
      if (currentGroup?.messages && currentGroup.messages.length > 0) {
        flatListRef.current?.scrollToIndex({ index: 0, animated: true });
      }
    } catch (error) {
      console.error('Errore nell\'invio del messaggio audio:', error);
      Alert.alert('Errore', 'Impossibile inviare il messaggio audio');
    }
  };

  // 🚀 ARCHITETTURA IBRIDA: Render + SuperMicron per GRUPPI
  const callRouter = require('../services/callRouter').default;

  const handleCall = async () => {
    try {
      // 📞 NAVIGA ALLA SCHERMATA DI CHIAMATA PRIMA
      navigation.navigate('CallWebRTC', {
        chatId: groupId,
        name: currentGroup?.name || 'Gruppo',
        avatar: currentGroup?.photoURL,
        isVideoCall: false,
        isGroupCall: true
      });

      // Per gruppi, passiamo tutti i membri tranne l'utente corrente
      const otherMembers = currentGroup?.members?.filter(member => member !== user?.id) || [];

      // 🚀 AVVIA ARCHITETTURA IBRIDA
      await callRouter.startCall(otherMembers, {
        isVideoEnabled: false,
        isAudioEnabled: true,
        chatId: groupId,
        targetName: currentGroup?.name || 'Gruppo',
        isGroupCall: true
      });
    } catch (error) {
      console.error('❌ IBRIDA GRUPPO: Errore chiamata audio:', error);
      Alert.alert('Errore', 'Impossibile avviare la chiamata di gruppo');
    }
  };

  const handleVideoCall = async () => {
    try {
      console.log('📹 IBRIDA GRUPPO: Avviando videochiamata di gruppo con architettura ibrida');
      console.log('📹 IBRIDA GRUPPO: GroupId:', groupId);
      console.log('📹 IBRIDA GRUPPO: Membri gruppo:', currentGroup?.members?.length || 0);

      // 🎥 NAVIGA ALLA SCHERMATA DI CHIAMATA PRIMA
      console.log('🎥 IBRIDA GRUPPO: Navigando alla schermata di videochiamata di gruppo...');
      navigation.navigate('CallWebRTC', {
        chatId: groupId,
        name: currentGroup?.name || 'Gruppo',
        avatar: currentGroup?.photoURL,
        isVideoCall: true,
        isGroupCall: true
      });

      // Per gruppi, passiamo tutti i membri tranne l'utente corrente
      const otherMembers = currentGroup?.members?.filter(member => member !== user?.id) || [];

      // 🚀 AVVIA ARCHITETTURA IBRIDA
      await callRouter.startCall(otherMembers, {
        isVideoEnabled: true,
        isAudioEnabled: true,
        chatId: groupId,
        targetName: currentGroup?.name || 'Gruppo',
        isGroupCall: true
      });

      console.log('✅ IBRIDA GRUPPO: Videochiamata di gruppo avviata con successo');
    } catch (error) {
      console.error('❌ IBRIDA GRUPPO: Errore videochiamata:', error);
      Alert.alert('Errore', 'Impossibile avviare la videochiamata di gruppo');
    }
  };

  // Handler per info gruppo
  const handleInfo = () => {
    console.log('ℹ️ Aprendo info gruppo:', groupId);
    navigation.navigate('GroupInfo', {
      groupId,
      name: currentGroup?.name,
      avatar: currentGroup?.photoURL
    });
  };

  // Handler per risposta a messaggio
  const handleReply = (message) => {
    setReplyTo(message);
  };

  const handleCancelReply = () => {
    setReplyTo(null);
  };

  const handleImagePress = (imageUrl) => {
    console.log('🖼️ Navigando al MediaViewer per immagine gruppo:', imageUrl);
    navigation.navigate('MediaViewer', {
      mediaUri: imageUrl,
      mediaType: 'image'
    });
  };

  const handleDeleteMessage = (message) => {
    const isOwnMessage = message.senderId === (user.id || user.uid);
    const messageAge = Date.now() - new Date(message.createdAt).getTime();
    const canDeleteForEveryone = isOwnMessage && messageAge < 7 * 60 * 1000; // 7 minuti

    const options = [
      { text: 'Cancella solo per te', onPress: () => handleDeleteForMe(message.id) },
    ];

    if (canDeleteForEveryone) {
      options.unshift({ text: 'Cancella per tutti', onPress: () => handleDeleteForEveryone(message.id), style: 'destructive' });
    }

    options.push({ text: 'Annulla', style: 'cancel' });

    Alert.alert(
      'Cancella messaggio',
      canDeleteForEveryone
        ? 'Vuoi cancellare questo messaggio per tutti o solo per te?'
        : 'Vuoi cancellare questo messaggio solo per te?',
      options
    );
  };

  const handleDeleteForEveryone = async (messageId) => {
    try {
      console.log('🗑️ GroupChat: Cancellando messaggio per tutti:', messageId);
      await deleteMessageForEveryone(groupId, messageId);
      Alert.alert('Successo', 'Messaggio cancellato per tutti');
    } catch (error) {
      console.error('❌ Errore cancellazione per tutti:', error);
      Alert.alert('Errore', 'Impossibile cancellare il messaggio per tutti');
    }
  };

  const handleDeleteForMe = async (messageId) => {
    try {
      console.log('👤 GroupChat: Cancellando messaggio solo per me:', messageId);
      await deleteMessageForMe(groupId, messageId);
      // Non mostrare alert per "cancella solo per me" (come WhatsApp)
    } catch (error) {
      console.error('❌ Errore cancellazione per me:', error);
      Alert.alert('Errore', 'Impossibile cancellare il messaggio');
    }
  };

  // Handler per reazioni ai messaggi
  const handleReaction = (messageId, reactionType) => {
    console.log('😊 Reazione al messaggio gruppo:', messageId, reactionType);
    // Da implementare: sistema reazioni per gruppi
  };

  // Handler per typing status
  const setTypingStatus = (isTyping) => {
    setIsTyping(isTyping);
    // Da implementare: notifica typing per gruppi
  };

  // 📱 CALCOLA SUBTITLE GRUPPO CON STATI WEBSOCKET
  const getGroupSubtitle = () => {
    if (groupRecordingUsers.length > 0) {
      if (groupRecordingUsers.length === 1) {
        return `${groupRecordingUsers[0].userName} sta registrando un audio...`;
      }
      return `${groupRecordingUsers.length} persone stanno registrando...`;
    }

    if (groupTypingUsers.length > 0) {
      if (groupTypingUsers.length === 1) {
        return `${groupTypingUsers[0].userName} sta scrivendo...`;
      } else if (groupTypingUsers.length === 2) {
        return `${groupTypingUsers[0].userName} e ${groupTypingUsers[1].userName} stanno scrivendo...`;
      } else {
        return `${groupTypingUsers[0].userName} e altri ${groupTypingUsers.length - 1} stanno scrivendo...`;
      }
    }

    if (isTyping) return 'Qualcuno sta scrivendo...'; // Fallback sistema vecchio

    // Mostra utenti online invece del numero totale
    const totalMembers = currentGroup?.members?.length || 0;
    const onlineCount = groupOnlineUsers.length;

    if (onlineCount === 0) {
      return totalMembers === 1 ? 'Solo tu' : `${totalMembers} partecipanti`;
    } else if (onlineCount === 1 && groupOnlineUsers[0]?.userId === (user?.id || user?.uid)) {
      return totalMembers === 1 ? 'Solo tu' : `${totalMembers} partecipanti`;
    } else {
      return `${onlineCount} online, ${totalMembers} partecipanti`;
    }
  };

  const currentGroupSubtitle = getGroupSubtitle();

  // ✅ Sistema gruppo production-ready

  // Render del componente (stessa struttura di ChatRoomScreen)
  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#1E88E5" />

      {/* Header del gruppo (stesso stile di ChatHeader) */}
      <ChatHeader
        title={currentGroup?.name || name || 'Gruppo'}
        subtitle={currentGroupSubtitle}
        avatar={currentGroup?.photoURL || avatar}
        isOnline={groupTypingUsers.length > 0 || groupRecordingUsers.length > 0}
        onCallPress={handleCall}
        onVideoCallPress={handleVideoCall}
        onInfoPress={handleInfo}
        chatId={groupId}
      />

      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'padding'}
        style={styles.keyboardAvoidingView}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 90 : 80}
        enabled
      >
        <LinearGradient
          colors={['#0A1929', '#132F4C']}
          style={styles.chatBackground}
        >
          {loading ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color="#1E88E5" />
            </View>
          ) : (
            <FlatList
              ref={flatListRef}
              data={currentGroup?.messages || []}
              keyExtractor={(item) => item.id}
              inverted={true}
              renderItem={({ item }) => {
                return (
                  <ChatBubble
                    message={item}
                    isOwn={item.senderId === (user.id || user.uid)}
                    onReplyPress={handleReply}
                    onReactionPress={(reactionType) => handleReaction(item.id, reactionType)}
                    onImagePress={handleImagePress}
                    onLongPress={() => {
                      // Logica WhatsApp: menu diverso per messaggi propri vs altrui
                      const isOwnMessage = item.senderId === (user.id || user.uid);

                      const options = [];

                      // MESSAGGI ALTRUI: Rispondi + Elimina (solo per me) + Inoltra
                      if (!isOwnMessage) {
                        options.push({ text: 'Rispondi', onPress: () => handleReply(item) });
                        options.push({ text: 'Inoltra', onPress: () => console.log('Inoltra messaggio gruppo:', item.id) });
                        options.push({ text: 'Elimina', onPress: () => handleDeleteForMe(item.id), style: 'destructive' });
                      }
                      // MESSAGGI PROPRI: Elimina (per tutti/per me) + Inoltra
                      else {
                        options.push({ text: 'Inoltra', onPress: () => console.log('Inoltra messaggio gruppo:', item.id) });
                        options.push({ text: 'Elimina', onPress: () => handleDeleteMessage(item), style: 'destructive' });
                      }

                      options.push({ text: 'Annulla', style: 'cancel' });

                      Alert.alert(
                        'Opzioni messaggio',
                        '',
                        options
                      );
                    }}
                  />
                );
              }}
              contentContainerStyle={styles.messageList}
              showsVerticalScrollIndicator={false}
            />
          )}
        </LinearGradient>

        {/* Input del gruppo (STESSO SISTEMA WHATSAPP DELLA CHAT UTENTI) */}
        <InputBox
          onSend={handleSend}
          onAttachment={handleAttachment}
          onVoiceMessage={startRecording}
          placeholder="Messaggio al gruppo"
          replyTo={replyTo}
          onCancelReply={handleCancelReply}
          chatId={groupId}
          userId={user?.id || user?.uid}
          onTypingStatusChange={(chatId, userId, isTyping) => {
            // 📡 INVIA EVENTO TYPING GRUPPO VIA WEBSOCKET
            console.log('📝 GRUPPO - Typing status changed:', { groupId, userId, isTyping });
            if (socket) {
              socket.emit('group_typing', {
                groupId,
                userId,
                userName: user?.displayName || 'Utente',
                isTyping
              });
              console.log('📡 GRUPPO - Evento group_typing inviato via WebSocket');
            } else {
              console.log('❌ GRUPPO - Socket NON connesso');
            }
            setTypingStatus(isTyping);
          }}
        />

        {loadingMedia && (
          <View style={styles.loadingOverlay}>
            <ActivityIndicator size="large" color="#FFFFFF" />
          </View>
        )}

        <AttachmentMenu
          visible={showAttachmentMenu}
          onClose={() => setShowAttachmentMenu(false)}
          onSelectMedia={handleSelectMedia}
        />
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

// Stili identici a ChatRoomScreen
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#0A1929',
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  chatBackground: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  messageList: {
    padding: 10,
    // Con inverted={true}, i messaggi più recenti appaiono in basso
    paddingTop: 10,
    paddingBottom: 80, // Spazio extra per l'input in basso
  },
  loadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default GroupChatScreen;