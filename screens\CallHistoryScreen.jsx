import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Image,
  Alert,
  SafeAreaView,
  StatusBar,
  ActivityIndicator,
  Platform,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { format } from 'date-fns';
import { it } from 'date-fns/locale';
import useCallStore from '../store/callStore';
import useAuthStore from '../store/authStore';
import useContactStore from '../store/contactStore';
import { theme } from '../theme';
import { deleteCallFromHistory, clearAllCallHistory } from '../utils/callHistoryUtils';

const CallHistoryScreen = () => {
  const navigation = useNavigation();
  const { user } = useAuthStore();
  const { contacts, loadContacts } = useContactStore();

  // 📞 CRONOLOGIA: Stati per cronologia dal server HP
  const [callHistory, setCallHistory] = useState([]);
  const [loading, setLoading] = useState(false);
  const [filter, setFilter] = useState('all'); // 'all', 'missed', 'incoming', 'outgoing'

  // 📞 CARICA CRONOLOGIA DAL SERVER HP
  const loadCallHistoryFromHP = async (filterType = 'all') => {
    try {
      setLoading(true);
      console.log(`📞 CRONOLOGIA: Caricando dal server HP (filtro: ${filterType})`);

      if (!user) {
        console.log('❌ CRONOLOGIA: Utente non autenticato');
        return;
      }

      const serverUrl = 'http://192.168.1.66:3001';
      let endpoint = `${serverUrl}/api/calls/history/${user.id}`;

      if (filterType !== 'all') {
        endpoint += `?filter=${filterType}`;
      }

      const response = await fetch(endpoint, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${user.token || ''}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success && data.calls) {
          setCallHistory(data.calls);
          console.log(`✅ CRONOLOGIA: Caricate ${data.calls.length} chiamate dal server HP`);
        } else {
          setCallHistory([]);
          console.log('📞 Nessuna chiamata trovata nella cronologia HP');
        }
      } else {
        console.error('❌ CRONOLOGIA: Errore caricamento dal server HP:', response.status);
        setCallHistory([]);
      }

    } catch (error) {
      console.error('❌ CRONOLOGIA: Errore caricamento cronologia:', error);
      setCallHistory([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadCallHistoryFromHP(filter);
    loadContacts();
  }, [filter]);

  const getContactInfo = (call) => {
    // 🔍 Verifica se è una chiamata con se stessi
    const isSelfCall = call.participants && call.participants.every(p => p === user?.id);

    if (isSelfCall) {
      return {
        name: user?.displayName || user?.name || 'Tu',
        photoURL: user?.photoURL || user?.avatar || null
      };
    }

    // 🏠 Usa i dati del contatto dal server HP se disponibili
    if (call.contactName && call.contactName !== 'Sconosciuto') {
      return {
        name: call.contactName,
        photoURL: call.contactAvatar || null
      };
    }

    // 📱 Fallback ai contatti locali
    if (call.participants && call.participants.length > 0) {
      const otherParticipant = call.participants.find(p => p !== user?.id);
      if (otherParticipant) {
        const contact = contacts.find(c => c.id === otherParticipant);
        if (contact) {
          return { name: contact.name || contact.username, photoURL: contact.profileImage };
        }
        return { name: 'Utente sconosciuto', photoURL: null };
      }
    }

    return { name: 'Utente sconosciuto', photoURL: null };
  };

  const getCallIcon = (call) => {
    const isIncoming = call.direction === 'incoming';
    const isMissed = call.status === 'missed' || call.status === 'rejected';
    const isVideo = call.type === 'video';

    if (isVideo) {
      if (isIncoming) {
        return isMissed ? 'videocam-off' : 'videocam';
      } else {
        return 'videocam';
      }
    } else {
      if (isIncoming) {
        return isMissed ? 'call-missed' : 'call-received';
      } else {
        return 'call-made';
      }
    }
  };

  const getCallIconColor = (call) => {
    const isIncoming = call.direction === 'incoming';
    const isMissed = call.status === 'missed' || call.status === 'rejected';

    if (isMissed) {
      return '#F44336'; // Rosso per chiamate perse
    } else if (isIncoming) {
      return '#4CAF50'; // Verde per chiamate ricevute
    } else {
      return '#2196F3'; // Blu per chiamate effettuate
    }
  };

  const formatCallDate = (call) => {
    // 🏠 Usa startTime dal server HP o fallback a createdAt
    const timestamp = call.startTime || call.createdAt;
    if (!timestamp) return '';

    const date = new Date(timestamp);
    const now = new Date();
    const isToday = date.toDateString() === now.toDateString();

    if (isToday) {
      return format(date, 'HH:mm', { locale: it });
    }

    const yesterday = new Date(now);
    yesterday.setDate(yesterday.getDate() - 1);
    const isYesterday = date.toDateString() === yesterday.toDateString();

    if (isYesterday) {
      return `Ieri, ${format(date, 'HH:mm', { locale: it })}`;
    }

    return format(date, 'dd MMM, HH:mm', { locale: it });
  };

  const formatCallDuration = (seconds) => {
    if (!seconds) return '';

    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;

    if (mins === 0) {
      return `${secs} sec`;
    }

    return `${mins} min ${secs} sec`;
  };

  const handleFilterChange = (newFilter) => {
    console.log(`📞 CRONOLOGIA: Cambiando filtro da ${filter} a ${newFilter}`);
    setFilter(newFilter);
  };

  const handleClearAllHistory = async () => {
    Alert.alert(
      'Cancella cronologia',
      'Sei sicuro di voler cancellare tutta la cronologia delle chiamate? Questa azione non può essere annullata.',
      [
        { text: 'Annulla', style: 'cancel' },
        {
          text: 'Cancella tutto',
          style: 'destructive',
          onPress: async () => {
            if (!user) return;

            const result = await clearAllCallHistory(user.id);
            if (result.success) {
              Alert.alert(
                'Cronologia cancellata',
                `${result.deletedCount} chiamate eliminate con successo`
              );
              // Ricarica la cronologia (dovrebbe essere vuota)
              loadCallHistoryFromHP(filter);
            } else {
              Alert.alert('Errore', 'Impossibile cancellare la cronologia');
            }
          }
        },
      ]
    );
  };

  const handleCallPress = (call) => {
    // 🏠 Ottieni l'altro partecipante dalla chiamata
    const otherParticipant = call.participants?.find(p => p !== user?.id);
    const contact = getContactInfo(call);

    if (!otherParticipant) {
      Alert.alert('Errore', 'Impossibile identificare il contatto per questa chiamata');
      return;
    }

    Alert.alert(
      'Chiama',
      `Vuoi chiamare ${contact.name}?`,
      [
        { text: 'Annulla', style: 'cancel' },
        {
          text: 'Chiamata audio',
          onPress: () => {
            navigation.navigate('AudioCall', {
              contactId: otherParticipant,
              contactName: contact.name,
              contactPhoto: contact.photoURL,
            });
          }
        },
        {
          text: 'Videochiamata',
          onPress: () => {
            navigation.navigate('VideoCall', {
              contactId: otherParticipant,
              contactName: contact.name,
              contactPhoto: contact.photoURL,
            });
          }
        },
      ]
    );
  };

  const handleDeleteCall = async (callId) => {
    Alert.alert(
      'Elimina chiamata',
      'Sei sicuro di voler eliminare questa chiamata dalla cronologia?',
      [
        { text: 'Annulla', style: 'cancel' },
        {
          text: 'Elimina',
          style: 'destructive',
          onPress: async () => {
            const success = await deleteCallFromHistory(callId);
            if (success) {
              // Ricarica la cronologia
              loadCallHistoryFromHP(filter);
            } else {
              Alert.alert('Errore', 'Impossibile eliminare la chiamata');
            }
          }
        },
      ]
    );
  };

  const filteredCalls = callHistory ? callHistory.filter(call => {
    if (filter === 'all') return true;

    const isIncoming = call.receiverId === user?.uid;
    const isMissed = call.status === 'rejected' || call.status === 'unanswered';

    if (filter === 'missed') return isMissed;
    if (filter === 'incoming') return isIncoming && !isMissed;
    if (filter === 'outgoing') return !isIncoming;

    return true;
  }) : [];

  const renderCallItem = ({ item }) => {
    const isIncoming = item.direction === 'incoming';
    const isMissed = item.status === 'missed' || item.status === 'rejected';
    const otherParticipant = item.participants?.find(p => p !== user?.id);
    const contact = getContactInfo(item);

    return (
      <TouchableOpacity
        style={styles.callItem}
        onPress={() => handleCallPress(item)}
        onLongPress={() => handleDeleteCall(item.id)}
      >
        <View style={styles.callIconContainer}>
          <Ionicons
            name={getCallIcon(item)}
            size={24}
            color={getCallIconColor(item)}
          />
        </View>

        <Image
          source={{ uri: contact.photoURL || 'https://via.placeholder.com/150' }}
          style={styles.contactPhoto}
        />

        <View style={styles.callInfo}>
          <Text style={styles.contactName}>{contact.name}</Text>

          <View style={styles.callDetails}>
            <Ionicons
              name={isIncoming ? 'arrow-down' : 'arrow-up'}
              size={14}
              color={isMissed ? '#F44336' : '#757575'}
              style={styles.callDirectionIcon}
            />

            <Text style={[
              styles.callStatus,
              isMissed && styles.missedCallStatus
            ]}>
              {item.type === 'video' ? 'Videochiamata' : 'Chiamata'}
              {isMissed ? ' persa' : isIncoming ? ' ricevuta' : ' effettuata'}
              {item.duration > 0 && ` • ${formatCallDuration(item.duration)}`}
            </Text>
          </View>
        </View>

        <View style={styles.callTimeContainer}>
          <Text style={styles.callTime}>{formatCallDate(item)}</Text>

          <TouchableOpacity
            style={styles.callButton}
            onPress={() => {
              if (!otherParticipant) return;

              navigation.navigate(item.type === 'video' ? 'VideoCall' : 'AudioCall', {
                contactId: otherParticipant,
                contactName: contact.name,
                contactPhoto: contact.photoURL,
              });
            }}
          >
            <Ionicons
              name={item.type === 'video' ? 'videocam' : 'call'}
              size={20}
              color={theme.colors.primary}
            />
          </TouchableOpacity>
        </View>
      </TouchableOpacity>
    );
  };

  const renderEmptyList = () => (
    <View style={styles.emptyContainer}>
      <Ionicons name="call-outline" size={64} color="#CCCCCC" />
      <Text style={styles.emptyText}>Nessuna chiamata recente</Text>
    </View>
  );

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent={true} />

      <LinearGradient
        colors={['#1E88E5', '#D81B60']}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 0 }}
        style={styles.header}
      >
        <View style={styles.headerContent}>
          <Text style={styles.headerTitle}>Chiamate</Text>
          <TouchableOpacity
            style={styles.clearButton}
            onPress={handleClearAllHistory}
          >
            <Ionicons name="trash-outline" size={22} color="#FFFFFF" />
          </TouchableOpacity>
        </View>
      </LinearGradient>

      <View style={styles.filterContainer}>
        <TouchableOpacity
          style={[styles.filterButton, filter === 'all' && styles.activeFilterButton]}
          onPress={() => handleFilterChange('all')}
        >
          <Text style={[
            styles.filterText,
            filter === 'all' && styles.activeFilterText
          ]}>
            Tutte
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.filterButton, filter === 'missed' && styles.activeFilterButton]}
          onPress={() => handleFilterChange('missed')}
        >
          <Text style={[
            styles.filterText,
            filter === 'missed' && styles.activeFilterText
          ]}>
            Perse
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.filterButton, filter === 'incoming' && styles.activeFilterButton]}
          onPress={() => handleFilterChange('incoming')}
        >
          <Text style={[
            styles.filterText,
            filter === 'incoming' && styles.activeFilterText
          ]}>
            Ricevute
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.filterButton, filter === 'outgoing' && styles.activeFilterButton]}
          onPress={() => handleFilterChange('outgoing')}
        >
          <Text style={[
            styles.filterText,
            filter === 'outgoing' && styles.activeFilterText
          ]}>
            Effettuate
          </Text>
        </TouchableOpacity>
      </View>

      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={theme.colors.primary} />
        </View>
      ) : (
        <FlatList
          data={filteredCalls}
          renderItem={renderCallItem}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.listContent}
          ListEmptyComponent={renderEmptyList}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#1E1E1E',
  },
  header: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    paddingTop: Platform.OS === 'ios' ? 50 : 40,
    paddingBottom: 16,
    borderBottomWidth: 0,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  clearButton: {
    padding: 8,
  },
  filterContainer: {
    flexDirection: 'row',
    paddingHorizontal: 8,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#2A2A2A',
    backgroundColor: '#1E1E1E',
  },
  filterButton: {
    flex: 1,
    paddingVertical: 8,
    alignItems: 'center',
  },
  activeFilterButton: {
    borderBottomWidth: 2,
    borderBottomColor: '#1E88E5',
  },
  filterText: {
    fontSize: 14,
    color: '#AAAAAA',
  },
  activeFilterText: {
    color: '#1E88E5',
    fontWeight: 'bold',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  listContent: {
    flexGrow: 1,
  },
  callItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#2A2A2A',
    backgroundColor: '#1E1E1E',
  },
  callIconContainer: {
    marginRight: 12,
  },
  contactPhoto: {
    width: 48,
    height: 48,
    borderRadius: 24,
    marginRight: 16,
    backgroundColor: '#EEEEEE',
  },
  callInfo: {
    flex: 1,
  },
  contactName: {
    fontSize: 16,
    fontWeight: '500',
    color: '#FFFFFF',
    marginBottom: 4,
  },
  callDetails: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  callDirectionIcon: {
    marginRight: 4,
  },
  callStatus: {
    fontSize: 14,
    color: '#AAAAAA',
  },
  missedCallStatus: {
    color: '#F44336',
  },
  callTimeContainer: {
    alignItems: 'flex-end',
  },
  callTime: {
    fontSize: 12,
    color: '#AAAAAA',
    marginBottom: 8,
  },
  callButton: {
    padding: 8,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
    backgroundColor: '#1E1E1E',
  },
  emptyText: {
    fontSize: 16,
    color: '#AAAAAA',
    textAlign: 'center',
    marginTop: 16,
  },
});

export default CallHistoryScreen;
