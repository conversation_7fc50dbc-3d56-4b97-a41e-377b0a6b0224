import axios from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { API_URL } from '../config/api';

// Configurazione base per axios
const api = axios.create({
  baseURL: API_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
});

// Log per debug
console.log('API URL:', API_URL);

// Interceptor per aggiungere il token di autenticazione
api.interceptors.request.use(
  async (config) => {
    try {
      const token = await AsyncStorage.getItem('userToken');
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
    } catch (error) {
      console.error('❌ TrendyChat: Errore lettura token:', error);
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Servizi API
export const apiService = {
  // Autenticazione
  auth: {
    login: async (phoneNumber) => {
      return api.post('/api/otp/generate', { userId: phoneNumber });
    },
    verifyOTP: async (phoneNumber, otp) => {
      console.log('[DEBUG] apiService.verifyOTP chiamato con phoneNumber=', phoneNumber, 'otp=', otp);
      console.log('[DEBUG] Tipo di phoneNumber:', typeof phoneNumber, 'Tipo di otp:', typeof otp);
      console.log('[DEBUG] URL endpoint: /api/otp/verify');
      try {
        const response = await api.post('/api/otp/verify', { userId: phoneNumber, otp });
        console.log('[DEBUG] apiService.verifyOTP risposta:', response.data);
        return response;
      } catch (error) {
        console.log('[DEBUG] apiService.verifyOTP errore:', error.message);
        throw error;
      }
    },
    refreshToken: async () => {
      return api.post('/auth/refresh');
    },
    logout: async () => {
      return api.post('/auth/logout');
    }
  },

  // Utenti
  users: {
    getProfile: async () => {
      return api.get('/users/profile');
    },
    updateProfile: async (data) => {
      return api.put('/users/update', data);
    },
    searchUsers: async (query) => {
      return api.get('/users/search', { params: { query } });
    },
    findByPhone: async (phoneNumbers) => {
      return api.post('/users/find-by-phone', { phoneNumbers });
    },
    getByPhone: async (phoneNumber) => {
      // Usa l'endpoint /users e filtra per telefono lato client
      console.log('🔍 TrendyChat: Cercando utente per telefono:', phoneNumber);
      const response = await api.get('/users');
      console.log('📡 TrendyChat: Risposta server /users:', response.data);

      const users = response.data.users || [];
      const user = users.find(u => u.phoneNumber === phoneNumber);

      console.log('👤 TrendyChat: Utente trovato:', user);

      return {
        data: {
          success: !!user,
          user: user || null,
          message: user ? 'Utente trovato' : 'Utente non trovato'
        }
      };
    }
  },

  // Chat
  chat: {
    getChats: async () => {
      return api.get('/chat/list');
    },
    getMessages: async (chatId) => {
      return api.get(`/chat/messages/${chatId}`);
    },
    sendMessage: async (chatId, message) => {
      return api.post(`/chat/send/${chatId}`, { message });
    },
    uploadMedia: async (chatId, file) => {
      const formData = new FormData();
      formData.append('file', file);
      return api.post(`/chat/media/${chatId}`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
    }
  },

  // Live Streaming
  live: {
    startStream: async (data) => {
      return api.post('/live/start', data);
    },
    endStream: async (streamId) => {
      return api.post(`/live/end/${streamId}`);
    },
    getStreams: async () => {
      return api.get('/live/list');
    }
  },

  // Chiamate
  calls: {
    startCall: async (data) => {
      return api.post('/calls/start', data);
    },
    endCall: async (callId) => {
      return api.post(`/calls/end/${callId}`);
    },
    getCallHistory: async () => {
      return api.get('/calls/history');
    }
  },

  // Storage
  storage: {
    uploadFile: async (file, type) => {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('type', type);
      return api.post('/cloud/upload', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
    },
    getFile: async (fileId) => {
      return api.get(`/storage/file/${fileId}`);
    },
    deleteFile: async (fileId) => {
      return api.delete(`/storage/file/${fileId}`);
    }
  },

  // Backup
  backup: {
    createBackup: async () => {
      return api.post('/backup/create');
    },
    restoreBackup: async (backupId) => {
      return api.post(`/backup/restore/${backupId}`);
    },
    getBackups: async () => {
      return api.get('/backup/list');
    }
  },

  // Community
  communities: {
    getCommunities: async () => {
      return api.get('/communities');
    },
    getCommunity: async (communityId) => {
      return api.get(`/communities/${communityId}`);
    },
    createCommunity: async (data) => {
      return api.post('/communities', data);
    },
    updateCommunity: async (communityId, data) => {
      return api.put(`/communities/${communityId}`, data);
    },
    deleteCommunity: async (communityId) => {
      return api.delete(`/communities/${communityId}`);
    },
    addMember: async (communityId, userId) => {
      return api.post(`/communities/${communityId}/members`, { userId });
    },
    removeMember: async (communityId, userId) => {
      return api.delete(`/communities/${communityId}/members/${userId}`);
    },
    addAdmin: async (communityId, userId) => {
      return api.post(`/communities/${communityId}/admins`, { userId });
    },
    removeAdmin: async (communityId, userId) => {
      return api.delete(`/communities/${communityId}/admins/${userId}`);
    },
    addGroup: async (communityId, groupId) => {
      return api.post(`/communities/${communityId}/groups`, { groupId });
    },
    removeGroup: async (communityId, groupId) => {
      return api.delete(`/communities/${communityId}/groups/${groupId}`);
    }
  },

  // Gruppi
  groups: {
    getGroups: async () => {
      return api.get('/groups');
    },
    getGroup: async (groupId) => {
      return api.get(`/groups/${groupId}`);
    },
    createGroup: async (data) => {
      return api.post('/groups', data);
    },
    updateGroup: async (groupId, data) => {
      return api.put(`/groups/${groupId}`, data);
    },
    deleteGroup: async (groupId) => {
      return api.delete(`/groups/${groupId}`);
    },
    addMember: async (groupId, userId) => {
      return api.post(`/groups/${groupId}/members`, { userId });
    },
    removeMember: async (groupId, userId) => {
      return api.delete(`/groups/${groupId}/members/${userId}`);
    },
    addAdmin: async (groupId, userId) => {
      return api.post(`/groups/${groupId}/admins`, { userId });
    },
    removeAdmin: async (groupId, userId) => {
      return api.delete(`/groups/${groupId}/admins/${userId}`);
    },
    sendMessage: async (groupId, data) => {
      return api.post(`/groups/${groupId}/messages`, data);
    },
    getMessages: async (groupId, limit = 50, skip = 0) => {
      return api.get(`/groups/${groupId}/messages`, { params: { limit, skip } });
    }
  },

  // Storie
  stories: {
    getStories: async () => {
      return api.get('/stories');
    },
    getStory: async (storyId) => {
      return api.get(`/stories/${storyId}`);
    },
    createStory: async (data) => {
      return api.post('/stories', data);
    },
    deleteStory: async (storyId) => {
      return api.delete(`/stories/${storyId}`);
    },
    getUserStories: async (userId) => {
      return api.get(`/stories/user/${userId}`);
    }
  }
};

export default apiService;