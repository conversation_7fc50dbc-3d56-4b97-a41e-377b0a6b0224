import { API_URL } from '../config/api';

// Importa WebRTC da react-native-webrtc
import {
  RTCPeerConnection,
  RTCSessionDescription,
  RTCIceCandidate,
  mediaDevices
} from 'react-native-webrtc';

// Importa servizi per architettura ibrida con failover
import signalingFailoverService from './signalingFailoverService';
const webrtcConfig = require('../webrtc.config');

class WebRTCService {
  constructor() {
    this.socket = null;
    this.peerConnection = null;
    this.localStream = null;
    this.remoteStream = null;
    this.callId = null;
    this.isConnected = false;
    this.connectionStats = {
      startTime: null,
      iceConnectionState: 'new',
      connectionState: 'new'
    };
  }

  // 🚀 Inizializza la connessione WebRTC con Failover Automatico
  async initialize() {
    try {
      console.log('🚀 Inizializzando WebRTC Service con failover...');

      // Inizializza il servizio di failover per signaling
      await signalingFailoverService.initialize();

      // Ottieni il socket dal servizio failover
      this.socket = signalingFailoverService.getSocket();

      if (!this.socket) {
        throw new Error('Impossibile ottenere socket dal servizio failover');
      }

      // Log del server attivo
      const serverInfo = signalingFailoverService.getCurrentServerInfo();
      console.log(`✅ Connesso a signaling ${serverInfo.server.type}: ${serverInfo.server.url}`);

      // Configurazione WebRTC con server ibridi
      const configuration = {
        iceServers: webrtcConfig.iceServers,
        iceCandidatePoolSize: 10
      };

      this.peerConnection = new RTCPeerConnection(configuration);

      // Setup eventi connessione
      this._setupConnectionEvents();

      // Gestione ICE candidates
      this.peerConnection.onicecandidate = (event) => {
        if (event.candidate && this.socket) {
          this.socket.emit('ice-candidate', {
            callId: this.callId,
            candidate: event.candidate
          });
        }
      };

      // Gestione stream remoto
      this.peerConnection.ontrack = (event) => {
        this.remoteStream = event.streams[0];
        console.log('📺 Stream remoto ricevuto');
      };

      // Setup eventi socket
      this._setupSocketEvents();

      // Setup eventi failover
      this._setupFailoverEvents();

      this.isConnected = true;
      console.log('✅ WebRTC Service inizializzato con failover');
      return true;

    } catch (error) {
      console.error('❌ Errore inizializzazione WebRTC:', error);
      throw error;
    }
  }

  // 🔗 Setup eventi connessione
  _setupConnectionEvents() {
    this.peerConnection.oniceconnectionstatechange = () => {
      this.connectionStats.iceConnectionState = this.peerConnection.iceConnectionState;
      console.log('🔗 ICE Connection State:', this.peerConnection.iceConnectionState);
    };

    this.peerConnection.onconnectionstatechange = () => {
      this.connectionStats.connectionState = this.peerConnection.connectionState;
      console.log('🔗 Connection State:', this.peerConnection.connectionState);
    };
  }

  // 📡 Setup eventi socket
  _setupSocketEvents() {
    if (!this.socket) return;

    // Eventi socket per signaling P2P
    this.socket.on('offer', async (data) => {
      await this.handleOffer(data);
    });

    this.socket.on('answer', async (data) => {
      await this.handleAnswer(data);
    });

    this.socket.on('ice-candidate', async (data) => {
      await this.handleIceCandidate(data);
    });

    this.socket.on('call-ended', () => {
      console.log('📞 Chiamata terminata dal peer');
      this.endCall();
    });

    this.socket.on('disconnect', () => {
      console.log('🔌 Disconnesso dal signaling server');
      this.isConnected = false;
    });
  }

  // 🔄 Setup eventi failover
  _setupFailoverEvents() {
    // Gestione cambio server
    signalingFailoverService.on('serverSwitch', (data) => {
      console.log(`🔄 Cambio server signaling: ${data.server.type}`);

      // Aggiorna il socket di riferimento
      this.socket = signalingFailoverService.getSocket();

      // Riconfigurazione eventi socket
      this._setupSocketEvents();
    });

    // Gestione disconnessione
    signalingFailoverService.on('disconnect', (data) => {
      console.log('🔌 Signaling disconnesso, tentativo failover...');
      this.isConnected = false;
    });

    // Gestione riconnessione
    signalingFailoverService.on('connect', (data) => {
      console.log(`✅ Riconnesso a signaling ${data.server.type}`);
      this.socket = signalingFailoverService.getSocket();
      this.isConnected = true;
      this._setupSocketEvents();
    });

    // Gestione errori
    signalingFailoverService.on('error', (data) => {
      console.error('❌ Errore signaling failover:', data.error);
    });
  }

  // 📞 Inizia una chiamata P2P
  async startCall(userId, options = {}) {
    try {
      console.log('📞 Avviando chiamata P2P con:', userId);

      this.connectionStats.startTime = Date.now();

      // Ottieni stream locale con configurazione ibrida
      this.localStream = await mediaDevices.getUserMedia({
        video: options.isVideoEnabled !== false ? webrtcConfig.videoConstraints : false,
        audio: options.isAudioEnabled !== false ? webrtcConfig.audioConstraints : false
      });

      // Aggiungi stream locale alla connessione
      this.localStream.getTracks().forEach(track => {
        this.peerConnection.addTrack(track, this.localStream);
      });

      // Crea offerta
      const offer = await this.peerConnection.createOffer();
      await this.peerConnection.setLocalDescription(offer);

      // Genera call ID unico
      this.callId = `call_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

      // Invia offerta al server Render
      this.socket.emit('start-call', {
        callId: this.callId,
        targetUserId: userId,
        offer,
        callType: 'p2p'
      });

      console.log('✅ Chiamata P2P avviata:', this.callId);
      return this.callId;

    } catch (error) {
      console.error('❌ Errore avvio chiamata P2P:', error);
      throw error;
    }
  }

  // 📞 Rispondi a una chiamata P2P
  async answerCall(callId, options = {}) {
    try {
      console.log('📞 Rispondendo alla chiamata P2P:', callId);

      this.callId = callId;
      this.connectionStats.startTime = Date.now();

      // Ottieni stream locale
      this.localStream = await mediaDevices.getUserMedia({
        video: options.isVideoEnabled !== false ? webrtcConfig.videoConstraints : false,
        audio: options.isAudioEnabled !== false ? webrtcConfig.audioConstraints : false
      });

      // Aggiungi stream locale alla connessione
      this.localStream.getTracks().forEach(track => {
        this.peerConnection.addTrack(track, this.localStream);
      });

      // Notifica il server che stiamo rispondendo
      this.socket.emit('answer-call', {
        callId: this.callId,
        accepted: true
      });

      console.log('✅ Risposta alla chiamata P2P inviata');
      return true;

    } catch (error) {
      console.error('❌ Errore risposta chiamata P2P:', error);
      throw error;
    }
  }

  // Gestisci offerta ricevuta
  async handleOffer(data) {
    try {
      this.callId = data.callId;
      await this.peerConnection.setRemoteDescription(data.offer);

      // Crea risposta
      const answer = await this.peerConnection.createAnswer();
      await this.peerConnection.setLocalDescription(answer);

      // Invia risposta
      this.socket.emit('answer', {
        callId: this.callId,
        answer
      });
    } catch (error) {
      console.error('Errore gestione offerta:', error);
    }
  }

  // Gestisci risposta ricevuta
  async handleAnswer(data) {
    try {
      await this.peerConnection.setRemoteDescription(data.answer);
    } catch (error) {
      console.error('Errore gestione risposta:', error);
    }
  }

  // Gestisci ICE candidate ricevuto
  async handleIceCandidate(data) {
    try {
      await this.peerConnection.addIceCandidate(data.candidate);
    } catch (error) {
      console.error('Errore gestione ICE candidate:', error);
    }
  }

  // 🔚 Termina la chiamata P2P
  async endCall() {
    try {
      console.log('🔚 Terminando chiamata P2P...');

      // Ferma tutti i track locali
      if (this.localStream) {
        this.localStream.getTracks().forEach(track => {
          track.stop();
          console.log(`🛑 Track ${track.kind} fermato`);
        });
      }

      // Chiudi la connessione peer
      if (this.peerConnection) {
        this.peerConnection.close();
      }

      // Notifica il server
      if (this.socket && this.callId) {
        this.socket.emit('end-call', {
          callId: this.callId,
          reason: 'user_ended'
        });
      }

      // Reset stato
      this._resetCallState();

      console.log('✅ Chiamata P2P terminata');
      return true;

    } catch (error) {
      console.error('❌ Errore terminazione chiamata P2P:', error);
      return false;
    }
  }

  // 🎤 Attiva/disattiva audio
  async toggleAudio(enabled) {
    try {
      if (!this.localStream) {
        return false;
      }

      const audioTrack = this.localStream.getAudioTracks()[0];
      if (audioTrack) {
        audioTrack.enabled = enabled;
        console.log(`🎤 Audio ${enabled ? 'attivato' : 'disattivato'}`);
        return true;
      }

      return false;
    } catch (error) {
      console.error('❌ Errore toggle audio:', error);
      return false;
    }
  }

  // 📹 Attiva/disattiva video
  async toggleVideo(enabled) {
    try {
      if (!this.localStream) {
        return false;
      }

      const videoTrack = this.localStream.getVideoTracks()[0];
      if (videoTrack) {
        videoTrack.enabled = enabled;
        console.log(`📹 Video ${enabled ? 'attivato' : 'disattivato'}`);
        return true;
      }

      return false;
    } catch (error) {
      console.error('❌ Errore toggle video:', error);
      return false;
    }
  }

  // 📊 Ottieni statistiche connessione
  getConnectionStats() {
    return {
      ...this.connectionStats,
      callId: this.callId,
      isConnected: this.isConnected,
      hasLocalStream: !!this.localStream,
      hasRemoteStream: !!this.remoteStream,
      duration: this.connectionStats.startTime ? Date.now() - this.connectionStats.startTime : 0
    };
  }

  // 🔄 Reset stato chiamata
  _resetCallState() {
    this.localStream = null;
    this.remoteStream = null;
    this.peerConnection = null;
    this.callId = null;
    this.connectionStats = {
      startTime: null,
      iceConnectionState: 'new',
      connectionState: 'new'
    };
  }

  // 🔌 Disconnetti dal servizio
  disconnect() {
    try {
      console.log('🔌 Disconnessione WebRTC Service...');

      if (this.callId) {
        this.endCall();
      }

      // Disconnetti il servizio failover
      signalingFailoverService.disconnect();
      this.socket = null;

      this.isConnected = false;
      console.log('✅ WebRTC Service disconnesso');

    } catch (error) {
      console.error('❌ Errore disconnessione WebRTC:', error);
    }
  }

  // 📊 Ottieni informazioni server signaling attivo
  getSignalingServerInfo() {
    return signalingFailoverService.getCurrentServerInfo();
  }

  // Ottieni stream locale (backward compatibility)
  getLocalStream() {
    return this.localStream;
  }

  // Ottieni stream remoto (backward compatibility)
  getRemoteStream() {
    return this.remoteStream;
  }
}

export default new WebRTCService();