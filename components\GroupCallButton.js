// 🏠 Group Call Button per TrendyChat - Architettura Ibrida
// Gestisce l'avvio di chiamate di gruppo tramite SFU SuperMicron

import React, { useState } from 'react';
import {
  TouchableOpacity,
  StyleSheet,
  View,
  Modal,
  Text,
  Animated,
  Alert,
  FlatList
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { useNavigation } from '@react-navigation/native';
import callRouter from '../services/callRouter';

const GroupCallButton = ({ groupId, groupName, participants = [] }) => {
  const [showOptions, setShowOptions] = useState(false);
  const [showParticipants, setShowParticipants] = useState(false);
  const [selectedParticipants, setSelectedParticipants] = useState([]);
  const navigation = useNavigation();
  const scaleAnim = new Animated.Value(0);

  const handleCallPress = () => {
    setShowOptions(true);
    Animated.spring(scaleAnim, {
      toValue: 1,
      useNativeDriver: true,
    }).start();
  };

  const handleClose = () => {
    Animated.spring(scaleAnim, {
      toValue: 0,
      useNativeDriver: true,
    }).start(() => {
      setShowOptions(false);
      setShowParticipants(false);
      setSelectedParticipants([]);
    });
  };

  const handleCallType = async (type) => {
    try {
      console.log(`🏠 Avviando chiamata di gruppo ${type} per ${groupName || groupId}`);
      
      // Se è un gruppo, usa tutti i partecipanti
      let callParticipants = [];
      
      if (groupId) {
        // Chiamata di gruppo: usa tutti i partecipanti del gruppo
        callParticipants = participants.map(p => p.id || p.userId || p);
      } else {
        // Chiamata multi-utente: usa partecipanti selezionati
        callParticipants = selectedParticipants;
      }
      
      if (callParticipants.length === 0) {
        Alert.alert(
          'Nessun Partecipante',
          'Seleziona almeno un partecipante per la chiamata.',
          [{ text: 'OK' }]
        );
        return;
      }
      
      // Opzioni chiamata di gruppo
      const options = {
        isVideoEnabled: type === 'video',
        isAudioEnabled: true,
        forceSFU: callParticipants.length >= 2 // Forza SFU per gruppi
      };
      
      // Avvia chiamata tramite Call Router
      const callData = await callRouter.startCall(callParticipants, options);
      
      console.log('✅ Chiamata di gruppo avviata:', callData);
      
      // Naviga alla schermata di chiamata di gruppo
      navigation.navigate('GroupCall', {
        callId: callData.id,
        callType: callData.type,
        roomId: callData.id,
        isIncoming: false,
        groupId: groupId,
        groupName: groupName,
        participants: callParticipants,
        isVideoCall: type === 'video',
        server: callData.server,
        maxParticipants: callData.roomData?.maxParticipants || 50
      });
      
      handleClose();
      
    } catch (error) {
      console.error('❌ Errore avvio chiamata di gruppo:', error);
      
      Alert.alert(
        'Errore Chiamata',
        'Impossibile avviare la chiamata di gruppo. Riprova più tardi.',
        [{ text: 'OK' }]
      );
      
      handleClose();
    }
  };

  const toggleParticipant = (participantId) => {
    setSelectedParticipants(prev => {
      if (prev.includes(participantId)) {
        return prev.filter(id => id !== participantId);
      } else {
        return [...prev, participantId];
      }
    });
  };

  const renderParticipant = ({ item }) => {
    const participantId = item.id || item.userId || item;
    const participantName = item.name || item.displayName || item.username || participantId;
    const isSelected = selectedParticipants.includes(participantId);
    
    return (
      <TouchableOpacity
        style={[styles.participantItem, isSelected && styles.participantSelected]}
        onPress={() => toggleParticipant(participantId)}
      >
        <View style={styles.participantInfo}>
          <View style={styles.avatar}>
            <Text style={styles.avatarText}>
              {participantName.charAt(0).toUpperCase()}
            </Text>
          </View>
          <Text style={styles.participantName}>{participantName}</Text>
        </View>
        {isSelected && (
          <Icon name="check-circle" size={20} color="#4CAF50" />
        )}
      </TouchableOpacity>
    );
  };

  return (
    <>
      <TouchableOpacity
        style={[styles.button, { backgroundColor: groupId ? '#FF9800' : '#9C27B0' }]}
        onPress={handleCallPress}
      >
        <Icon name={groupId ? "account-group" : "phone-plus"} size={24} color="#fff" />
      </TouchableOpacity>

      <Modal
        visible={showOptions}
        transparent
        animationType="none"
        onRequestClose={handleClose}
      >
        <TouchableOpacity
          style={styles.modalOverlay}
          activeOpacity={1}
          onPress={handleClose}
        >
          <Animated.View
            style={[
              styles.optionsContainer,
              {
                transform: [{ scale: scaleAnim }]
              }
            ]}
          >
            {!groupId && !showParticipants && (
              <>
                <Text style={styles.modalTitle}>Seleziona Partecipanti</Text>
                <TouchableOpacity
                  style={styles.option}
                  onPress={() => setShowParticipants(true)}
                >
                  <Icon name="account-multiple-plus" size={24} color="#9C27B0" />
                  <Text style={styles.optionText}>Scegli partecipanti</Text>
                </TouchableOpacity>
              </>
            )}

            {(groupId || showParticipants) && (
              <>
                {!groupId && (
                  <>
                    <Text style={styles.modalTitle}>
                      Partecipanti ({selectedParticipants.length})
                    </Text>
                    <FlatList
                      data={participants}
                      renderItem={renderParticipant}
                      keyExtractor={(item) => item.id || item.userId || item}
                      style={styles.participantsList}
                      maxHeight={200}
                    />
                  </>
                )}

                <Text style={styles.modalTitle}>
                  {groupId ? `Chiamata ${groupName || 'Gruppo'}` : 'Tipo Chiamata'}
                </Text>

                <TouchableOpacity
                  style={styles.option}
                  onPress={() => handleCallType('audio')}
                  disabled={!groupId && selectedParticipants.length === 0}
                >
                  <Icon name="phone" size={24} color="#4CAF50" />
                  <Text style={[
                    styles.optionText,
                    (!groupId && selectedParticipants.length === 0) && styles.optionDisabled
                  ]}>
                    Chiamata vocale di gruppo
                  </Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={styles.option}
                  onPress={() => handleCallType('video')}
                  disabled={!groupId && selectedParticipants.length === 0}
                >
                  <Icon name="video" size={24} color="#2196F3" />
                  <Text style={[
                    styles.optionText,
                    (!groupId && selectedParticipants.length === 0) && styles.optionDisabled
                  ]}>
                    Videochiamata di gruppo
                  </Text>
                </TouchableOpacity>

                {!groupId && (
                  <TouchableOpacity
                    style={styles.option}
                    onPress={() => setShowParticipants(false)}
                  >
                    <Icon name="arrow-left" size={24} color="#666" />
                    <Text style={styles.optionText}>Indietro</Text>
                  </TouchableOpacity>
                )}
              </>
            )}
          </Animated.View>
        </TouchableOpacity>
      </Modal>
    </>
  );
};

const styles = StyleSheet.create({
  button: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 5,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  optionsContainer: {
    backgroundColor: '#fff',
    borderRadius: 15,
    padding: 20,
    width: '90%',
    maxWidth: 400,
    maxHeight: '80%',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 15,
    textAlign: 'center',
    color: '#333',
  },
  option: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  optionText: {
    fontSize: 16,
    marginLeft: 15,
    color: '#333',
  },
  optionDisabled: {
    color: '#ccc',
  },
  participantsList: {
    maxHeight: 200,
    marginBottom: 15,
  },
  participantItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  participantSelected: {
    backgroundColor: '#E8F5E8',
  },
  participantInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  avatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#9C27B0',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  avatarText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: 'bold',
  },
  participantName: {
    fontSize: 16,
    color: '#333',
  },
});

export default GroupCallButton;
