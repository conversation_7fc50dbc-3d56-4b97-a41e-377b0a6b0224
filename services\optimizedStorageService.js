import AsyncStorage from '@react-native-async-storage/async-storage';
import * as FileSystem from 'expo-file-system';
import { SERVER_IP, STORAGE_KEYS, PERFORMANCE_CONFIG } from '../config/constants';

// Costanti per la gestione della cache
const CACHE_EXPIRY = 24 * 60 * 60 * 1000; // 24 ore in millisecondi
const STORAGE_INFO_CACHE_KEY = '@trendychat:storage_info';
const LAST_CLEANUP_KEY = '@trendychat:last_cache_cleanup';

/**
 * Ottieni i dettagli dello spazio di archiviazione con gestione ottimizzata della cache
 * @returns {Promise<Object>} Dettagli dello spazio di archiviazione
 */
export const getStorageDetails = async () => {
  try {
    // Verifica se abbiamo dati in cache validi
    const cachedDataJson = await AsyncStorage.getItem(STORAGE_INFO_CACHE_KEY);
    const now = Date.now();
    
    if (cachedDataJson) {
      try {
        const cachedData = JSON.parse(cachedDataJson);
        // Usa la cache se è ancora valida (meno di 1 ora)
        if (cachedData.timestamp && (now - cachedData.timestamp < 60 * 60 * 1000)) {
          console.log('Usando dati di archiviazione dalla cache');
          return cachedData.data;
        }
      } catch (e) {
        console.warn('Errore nel parsing dei dati in cache:', e);
      }
    }

    // Prova prima con la chiave utilizzata in authService.js
    let token = await AsyncStorage.getItem('@trendychat:token');

    // Se non trova il token, prova con la chiave standard
    if (!token) {
      token = await AsyncStorage.getItem(STORAGE_KEYS.AUTH_TOKEN);
    }

    console.log('Token trovato:', token ? 'Sì' : 'No');

    if (!token) {
      throw new Error('Token di autenticazione non trovato');
    }

    // Ottieni l'utente corrente
    const userJson = await AsyncStorage.getItem('@trendychat:user');
    let userId = '';

    if (userJson) {
      try {
        const user = JSON.parse(userJson);
        userId = user.id || user._id;
      } catch (e) {
        console.error('Errore nel parsing dei dati utente:', e);
      }
    }

    if (!userId) {
      throw new Error('ID utente non trovato');
    }

    // Tenta di recuperare i dati reali dal server
    try {
      console.log('Recupero dati di archiviazione dal server per l\'utente:', userId);

      // Costruisci l'URL dell'API
      const apiUrl = `http://${SERVER_IP}:3001/api/users/storage`;
      console.log('URL API storage:', apiUrl);

      // Effettua la richiesta al server con timeout
      console.log('Invio richiesta al server con token:', token);
      
      // Usa AbortController per implementare un timeout
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 secondi di timeout
      
      const response = await fetch(apiUrl, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        signal: controller.signal
      });
      
      clearTimeout(timeoutId);

      // Se la richiesta ha successo, restituisci i dati dal server
      if (response.ok) {
        const data = await response.json();
        console.log('Dati di archiviazione ricevuti dal server:', data);

        const formattedData = {
          quota: {
            used: data.quota?.used || 0,
            limit: data.quota?.limit || 1073741824, // 1GB in bytes
            available: data.quota?.available || 1073741824,
            percentUsed: data.quota?.percentUsed || 0
          },
          details: {
            total: data.details?.total || 0,
            photos: data.details?.photos || 0,
            videos: data.details?.videos || 0,
            audio: data.details?.audio || 0,
            documents: data.details?.documents || 0,
            other: data.details?.other || 0,
            backup: data.details?.backup || 0,
            stories: data.details?.stories || 0
          }
        };

        // Salva i dati in cache
        await AsyncStorage.setItem(STORAGE_INFO_CACHE_KEY, JSON.stringify({
          timestamp: now,
          data: formattedData
        }));

        return formattedData;
      } else {
        console.warn('Errore nella risposta del server:', response.status);
        // Prova a leggere il messaggio di errore
        try {
          const errorData = await response.text();
          console.warn('Dettagli errore:', errorData);
        } catch (e) {
          console.warn('Impossibile leggere i dettagli dell\'errore');
        }
        // Se il server restituisce un errore, calcola i dati localmente
        return await calculateLocalStorageUsage(userId);
      }
    } catch (error) {
      console.warn('Errore nel recupero dei dati dal server:', error);
      console.warn('Dettagli errore:', error.message);
      // In caso di errore di connessione, calcola i dati localmente
      return await calculateLocalStorageUsage(userId);
    }
  } catch (error) {
    console.error('Errore nel recupero dei dettagli di archiviazione:', error);

    // In caso di errore, restituisci dati minimi
    return {
      quota: {
        used: 0,
        limit: 1073741824, // 1GB in bytes
        available: 1073741824,
        percentUsed: 0
      },
      details: {
        total: 0,
        photos: 0,
        videos: 0,
        audio: 0,
        documents: 0,
        other: 0,
        backup: 0,
        stories: 0
      }
    };
  }
};

// Funzione per calcolare l'utilizzo dello storage localmente in modo ottimizzato
const calculateLocalStorageUsage = async (userId) => {
  try {
    console.log('Calcolo locale dell\'utilizzo dello storage');

    // Ottieni tutte le chiavi dallo storage in modo efficiente
    const keys = await AsyncStorage.getAllKeys();

    // Filtra le chiavi relative ai media
    const mediaKeys = keys.filter(key =>
      key.startsWith('media_info_') ||
      key.startsWith('media_cache_')
    );

    let totalSize = 0;
    let photoSize = 0;
    let videoSize = 0;
    let audioSize = 0;
    let documentSize = 0;
    let storiesSize = 0;
    let otherSize = 0;

    // Ottimizzazione: recupera i dati in batch per ridurre le chiamate a AsyncStorage
    const batchSize = 10;
    const batches = [];
    
    for (let i = 0; i < mediaKeys.length; i += batchSize) {
      batches.push(mediaKeys.slice(i, i + batchSize));
    }

    for (const batch of batches) {
      const mediaInfos = await AsyncStorage.multiGet(batch);
      
      for (const [key, value] of mediaInfos) {
        if (!value) continue;
        
        try {
          const mediaInfo = JSON.parse(value);
          
          // Verifica che il media appartenga all'utente corrente
          if (mediaInfo.userId === userId) {
            const size = mediaInfo.size || 0;
            totalSize += size;

            // Classifica il file in base al tipo
            if (mediaInfo.type === 'image') {
              photoSize += size;
            } else if (mediaInfo.type === 'video') {
              videoSize += size;
            } else if (mediaInfo.type === 'audio') {
              audioSize += size;
            } else if (mediaInfo.type === 'document') {
              documentSize += size;
            } else if (mediaInfo.type === 'story') {
              storiesSize += size;
            } else {
              otherSize += size;
            }
          }
        } catch (e) {
          console.warn('Errore nel parsing delle informazioni del media:', e);
        }
      }
    }

    // Calcola anche lo spazio utilizzato nella directory cache di FileSystem
    try {
      if (FileSystem.cacheDirectory) {
        const cacheDir = FileSystem.cacheDirectory;
        const cacheInfo = await FileSystem.getInfoAsync(cacheDir, { size: true });
        
        if (cacheInfo.exists && cacheInfo.size) {
          // Aggiungiamo una parte della cache al totale (50%)
          const cacheSize = Math.floor(cacheInfo.size * 0.5);
          totalSize += cacheSize;
          otherSize += cacheSize;
        }
      }
    } catch (e) {
      console.warn('Errore nel calcolo della dimensione della cache:', e);
    }

    // Limite di 1GB per utente
    const storageLimit = 1073741824; // 1GB in bytes

    const result = {
      quota: {
        used: totalSize,
        limit: storageLimit,
        available: Math.max(0, storageLimit - totalSize),
        percentUsed: Math.min(100, (totalSize / storageLimit) * 100)
      },
      details: {
        total: totalSize,
        photos: photoSize,
        videos: videoSize,
        audio: audioSize,
        documents: documentSize,
        stories: storiesSize,
        other: otherSize
      }
    };

    // Salva i dati in cache
    await AsyncStorage.setItem(STORAGE_INFO_CACHE_KEY, JSON.stringify({
      timestamp: Date.now(),
      data: result
    }));

    console.log('Dati di archiviazione ricevuti:', result);
    return result;
  } catch (error) {
    console.error('Errore nel calcolo locale dell\'utilizzo dello storage:', error);
    
    // In caso di errore, restituisci dati minimi
    return {
      quota: {
        used: 0,
        limit: 1073741824, // 1GB in bytes
        available: 1073741824,
        percentUsed: 0
      },
      details: {
        total: 0,
        photos: 0,
        videos: 0,
        audio: 0,
        documents: 0,
        stories: 0,
        other: 0
      }
    };
  }
};
