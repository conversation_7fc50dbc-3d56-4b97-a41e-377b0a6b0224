import React from 'react';
import {
  View,
  Image,
  StyleSheet,
  Dimensions,
  ImageBackground,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useTheme } from '../../hooks/useTheme';

const { width, height } = Dimensions.get('window');

const ChatBackground = ({
  type = 'default',
  customImage = null,
  customColor = null,
  customGradient = null,
  customPattern = null,
  children,
}) => {
  const { theme, isDark } = useTheme();

  const renderBackground = () => {
    switch (type) {
      case 'image':
        if (customImage) {
          return (
            <ImageBackground
              source={{ uri: customImage }}
              style={styles.background}
              resizeMode="cover"
            >
              <View style={[styles.overlay, { backgroundColor: 'rgba(0, 0, 0, 0.2)' }]}>
                {children}
              </View>
            </ImageBackground>
          );
        }
        return renderDefaultBackground();

      case 'color':
        if (customColor) {
          return (
            <View style={[styles.background, { backgroundColor: customColor }]}>
              {children}
            </View>
          );
        }
        return renderDefaultBackground();

      case 'gradient':
        const gradientColors = customGradient || (isDark ? ['#1A237E', '#4A148C'] : ['#E3F2FD', '#BBDEFB']);
        return (
          <LinearGradient
            colors={gradientColors}
            style={styles.background}
          >
            {children}
          </LinearGradient>
        );

      case 'pattern':
        const patternColors = customPattern
          ? (typeof customPattern === 'number'
              ? (customPattern === 0 ? ['#E5DDD5', '#D4C9C0']
                : customPattern === 1 ? ['#DCF8C6', '#C5E1B0']
                : customPattern === 2 ? ['#D5F5E3', '#ABEBC6']
                : ['#D6EAF8', '#AED6F1'])
              : ['#E5DDD5', '#D4C9C0'])
          : (isDark ? ['#1A237E', '#4A148C'] : ['#E5DDD5', '#D4C9C0']);

        return (
          <LinearGradient
            colors={patternColors}
            style={styles.background}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
          >
            <View style={styles.patternOverlay}>
              {/* Pattern di punti o linee */}
              {Array.from({ length: 50 }).map((_, i) => (
                <View key={i} style={styles.patternRow}>
                  {Array.from({ length: 30 }).map((_, j) => (
                    <View key={j} style={styles.patternDot} />
                  ))}
                </View>
              ))}
            </View>
            <View style={[styles.overlay, { backgroundColor: isDark ? 'rgba(0, 0, 0, 0.2)' : 'rgba(255, 255, 255, 0.2)' }]}>
              {children}
            </View>
          </LinearGradient>
        );

      default:
        return renderDefaultBackground();
    }
  };

  const renderDefaultBackground = () => {
    // Sfondo default TrendyChat con gradiente blu-rosa
    return (
      <LinearGradient
        colors={['#1E88E5', '#D81B60']}
        style={styles.background}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        <View style={[styles.overlay, { backgroundColor: 'rgba(0, 0, 0, 0.1)' }]}>
          {children}
        </View>
      </LinearGradient>
    );
  };

  return renderBackground();
};

const styles = StyleSheet.create({
  background: {
    flex: 1,
    width,
    height,
  },
  overlay: {
    flex: 1,
  },
  patternOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    flexDirection: 'column',
  },
  patternRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    flex: 1,
  },
  patternDot: {
    width: 2,
    height: 2,
    borderRadius: 1,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
  },
});

export default ChatBackground;
