import { create } from 'zustand';
import AsyncStorage from '@react-native-async-storage/async-storage';
import axios from 'axios';
import { AppState } from 'react-native';

const API_URL = 'http://192.168.1.66:3001';

const usePresenceStore = create((set, get) => ({
  // Stato
  isOnline: false,
  lastSeen: null,
  heartbeatInterval: null,
  presenceCache: new Map(), // Cache per le presenze degli utenti

  // Azioni
  setOnline: async () => {
    try {
      // Ottieni il token di autenticazione (ID utente per il server HP)
      let token = null;

      // SEMPRE usa l'ID utente da authStore (priorità assoluta)
      try {
        const useAuthStore = require('./authStore').default;
        const authState = useAuthStore.getState();

        if (authState && authState.user && authState.user.id) {
          token = authState.user.id; // Usa l'ID utente corretto (user_1748339520304)
          console.log('🔍 setOnline - ID utente da authStore:', token);
        }
      } catch (storeError) {
        console.error('❌ Errore accesso authStore:', storeError);
      }

      // FALLBACK: Se non trova l'ID utente, prova con AsyncStorage (ma dovrebbe essere raro)
      if (!token) {
        const asyncToken = await AsyncStorage.getItem('@trendychat:token') ||
                          await AsyncStorage.getItem('authToken');

        if (asyncToken && asyncToken.startsWith('jwt_token_')) {
          token = asyncToken.replace('jwt_token_', '');
        } else {
          token = asyncToken;
        }
        console.log('⚠️ setOnline - FALLBACK ID da AsyncStorage:', token);
        console.log('⚠️ ATTENZIONE: Dovrebbe usare authStore, non AsyncStorage!');
      }

      if (!token) {
        console.warn('Token non trovato per presenza online');
        return;
      }

      console.log('🟢 Impostando utente online:', token.substring(0, 10) + '...');

      // Usa l'endpoint di aggiornamento profilo esistente per impostare online
      console.log('📡 Inviando richiesta setOnline al server:', {
        id: token,
        online: true,
        lastSeen: new Date().toISOString()
      });

      const response = await axios.put(`${API_URL}/api/users/update`, {
        id: token, // Passa l'ID utente
        online: true,
        lastSeen: new Date().toISOString()
      }, {
        headers: { 'Authorization': `Bearer ${token}` }
      });

      console.log('✅ Risposta server setOnline:', response.data);
      set({ isOnline: true });
      get().startHeartbeat();
    } catch (error) {
      console.error('Errore nell\'impostare online:', error);
    }
  },

  setOffline: async () => {
    try {
      // Ottieni il token di autenticazione (ID utente per il server HP)
      let token = null;

      // SEMPRE usa l'ID utente da authStore (priorità assoluta)
      try {
        const useAuthStore = require('./authStore').default;
        const authState = useAuthStore.getState();

        if (authState && authState.user && authState.user.id) {
          token = authState.user.id;
          console.log('🔍 setOffline - ID utente da authStore:', token);
        }
      } catch (storeError) {
        console.error('❌ Errore accesso authStore (setOffline):', storeError);
      }

      // FALLBACK: Se non trova l'ID utente, prova con AsyncStorage
      if (!token) {
        const asyncToken = await AsyncStorage.getItem('@trendychat:token') ||
                          await AsyncStorage.getItem('authToken');

        if (asyncToken && asyncToken.startsWith('jwt_token_')) {
          token = asyncToken.replace('jwt_token_', '');
        } else {
          token = asyncToken;
        }
        console.log('⚠️ setOffline - FALLBACK ID da AsyncStorage:', token);
      }

      if (!token) {
        console.warn('Token non trovato per presenza offline');
        return;
      }

      console.log('🔴 Impostando utente offline:', token.substring(0, 10) + '...');

      // Usa l'endpoint di aggiornamento profilo esistente per impostare offline
      await axios.put(`${API_URL}/api/users/update`, {
        id: token, // Passa l'ID utente
        online: false,
        lastSeen: new Date().toISOString()
      }, {
        headers: { 'Authorization': `Bearer ${token}` }
      });

      set({ isOnline: false, lastSeen: new Date() });
      get().stopHeartbeat();
    } catch (error) {
      console.error('Errore nell\'impostare offline:', error);
    }
  },

  startHeartbeat: () => {
    const { heartbeatInterval } = get();
    if (heartbeatInterval) return; // Già avviato

    console.log('💓 Avviando heartbeat ogni 30 secondi');

    const interval = setInterval(async () => {
      try {
        // Ottieni il token di autenticazione (ID utente per il server HP)
        let token = null;

        // SEMPRE usa l'ID utente da authStore (priorità assoluta)
        try {
          const useAuthStore = require('./authStore').default;
          const authState = useAuthStore.getState();

          if (authState && authState.user && authState.user.id) {
            token = authState.user.id;
            // Log heartbeat rimosso per pulizia console
          }
        } catch (storeError) {
          console.error('❌ Errore accesso authStore (heartbeat):', storeError);
        }

        // FALLBACK: Se non trova l'ID utente, prova con AsyncStorage
        if (!token) {
          const asyncToken = await AsyncStorage.getItem('@trendychat:token') ||
                            await AsyncStorage.getItem('authToken');

          if (asyncToken && asyncToken.startsWith('jwt_token_')) {
            token = asyncToken.replace('jwt_token_', '');
          } else {
            token = asyncToken;
          }
        }

        if (!token) return;

        // Usa l'endpoint di aggiornamento profilo esistente per heartbeat
        await axios.put(`${API_URL}/api/users/update`, {
          id: token, // Passa l'ID utente
          online: true,
          lastSeen: new Date().toISOString()
        }, {
          headers: { 'Authorization': `Bearer ${token}` }
        });
      } catch (error) {
        console.error('Errore nel heartbeat:', error);
      }
    }, 30000); // Ogni 30 secondi

    set({ heartbeatInterval: interval });
  },

  stopHeartbeat: () => {
    const { heartbeatInterval } = get();
    if (heartbeatInterval) {
      console.log('💔 Fermando heartbeat');
      clearInterval(heartbeatInterval);
      set({ heartbeatInterval: null });
    }
  },

  getUserPresence: async (userId) => {
    try {
      // Controlla cache prima
      const { presenceCache } = get();
      const cached = presenceCache.get(userId);
      if (cached && (Date.now() - cached.timestamp) < 30000) { // Cache per 30 secondi
        return cached.data;
      }

      // Ottieni il token di autenticazione (ID utente per il server HP)
      let token = null;

      // Prima prova a ottenere l'ID utente da authStore
      try {
        const useAuthStore = require('./authStore').default;
        const authState = useAuthStore.getState();

        if (authState && authState.user && authState.user.id) {
          token = authState.user.id;
        }
      } catch (storeError) {
        // Fallback silenzioso
      }

      // Se non trova l'ID utente, prova con AsyncStorage
      if (!token) {
        token = await AsyncStorage.getItem('@trendychat:token') ||
                await AsyncStorage.getItem('authToken');

        if (token && token.startsWith('jwt_token_')) {
          token = token.replace('jwt_token_', '');
        }
      }

      if (!token) return null;

      // Usa l'endpoint /api/users esistente per ottenere tutti gli utenti
      const response = await axios.get(`${API_URL}/api/users`, {
        headers: { 'Authorization': `Bearer ${token}` }
      });

      // Trova l'utente specifico
      const users = response.data.users || [];
      const user = users.find(u => u.id === userId);

      console.log('🔍 getUserPresence - Utente trovato nel database:', {
        userId,
        found: !!user,
        online: user?.online,
        lastSeen: user?.lastSeen,
        updatedAt: user?.updatedAt
      });

      // Debug: Mostra tutti i campi dell'utente per capire cosa c'è nel database
      console.log('🔍 getUserPresence - Tutti i campi utente dal database:', user);

      if (!user) {
        console.log('❌ getUserPresence - Utente non trovato:', userId);
        return null;
      }

      // Formatta la risposta come presenza
      const presenceData = {
        success: true,
        userId: user.id,
        online: user.online || false,
        displayName: user.displayName || user.name || 'Utente',
        lastSeen: user.lastSeen || user.updatedAt,
        lastSeenFormatted: get().formatLastSeen(user.lastSeen || user.updatedAt, user.online || false)
      };

      console.log('📊 getUserPresence - Dati presenza formattati:', presenceData);

      // Aggiorna cache
      presenceCache.set(userId, {
        data: presenceData,
        timestamp: Date.now()
      });

      return presenceData;
    } catch (error) {
      console.error('Errore nel recupero presenza utente:', error);
      return null;
    }
  },

  getBulkPresence: async (userIds) => {
    try {
      // Ottieni il token di autenticazione (ID utente per il server HP)
      let token = null;

      // Prima prova a ottenere l'ID utente da authStore
      try {
        const useAuthStore = require('./authStore').default;
        const authState = useAuthStore.getState();

        if (authState && authState.user && authState.user.id) {
          token = authState.user.id;
        }
      } catch (storeError) {
        // Fallback silenzioso
      }

      // Se non trova l'ID utente, prova con AsyncStorage
      if (!token) {
        token = await AsyncStorage.getItem('@trendychat:token') ||
                await AsyncStorage.getItem('authToken');

        if (token && token.startsWith('jwt_token_')) {
          token = token.replace('jwt_token_', '');
        }
      }

      if (!token) return [];

      // Usa l'endpoint /api/users esistente per ottenere tutti gli utenti
      const response = await axios.get(`${API_URL}/api/users`, {
        headers: { 'Authorization': `Bearer ${token}` }
      });

      // Filtra solo gli utenti richiesti
      const users = response.data.users || [];
      const filteredUsers = users.filter(user => userIds.includes(user.id));

      // Formatta come presenza
      const presenceData = filteredUsers.map(user => ({
        userId: user.id,
        online: user.online || false,
        displayName: user.displayName || user.name || 'Utente',
        lastSeen: user.lastSeen || user.updatedAt,
        lastSeenFormatted: formatLastSeen(user.lastSeen || user.updatedAt, user.online || false)
      }));

      // Aggiorna cache per tutti gli utenti
      const { presenceCache } = get();
      presenceData.forEach(presence => {
        presenceCache.set(presence.userId, {
          data: presence,
          timestamp: Date.now()
        });
      });

      return presenceData;
    } catch (error) {
      console.error('Errore nel recupero presenza bulk:', error);
      return [];
    }
  },

  // Formatta l'ultimo accesso come WhatsApp
  formatLastSeen: (lastSeen, isOnline) => {
    if (isOnline) {
      return 'online';
    }

    if (!lastSeen) {
      return 'ultimo accesso sconosciuto';
    }

    const now = new Date();
    const lastSeenDate = new Date(lastSeen);
    const diffInMinutes = Math.floor((now - lastSeenDate) / (1000 * 60));
    const diffInHours = Math.floor(diffInMinutes / 60);
    const diffInDays = Math.floor(diffInHours / 24);

    if (diffInMinutes < 1) {
      return 'ultimo accesso ora';
    } else if (diffInMinutes < 60) {
      return `ultimo accesso ${diffInMinutes} minuti fa`;
    } else if (diffInHours < 24) {
      if (diffInHours === 1) {
        return 'ultimo accesso 1 ora fa';
      }
      return `ultimo accesso ${diffInHours} ore fa`;
    } else if (diffInDays === 1) {
      return 'ultimo accesso ieri';
    } else if (diffInDays < 7) {
      return `ultimo accesso ${diffInDays} giorni fa`;
    } else {
      return 'ultimo accesso tempo fa';
    }
  },

  // Pulisce la cache
  clearCache: () => {
    set({ presenceCache: new Map() });
  },

  // Inizializzazione
  initialize: () => {
    console.log('🚀 Inizializzando sistema presenza');

    // Listener per lo stato dell'app
    const handleAppStateChange = (nextAppState) => {
      console.log('📱 Stato app cambiato:', nextAppState);

      if (nextAppState === 'active') {
        get().setOnline();
      } else if (nextAppState === 'background' || nextAppState === 'inactive') {
        get().setOffline();
      }
    };

    AppState.addEventListener('change', handleAppStateChange);

    // Imposta online all'avvio
    get().setOnline();

    return () => {
      console.log('🛑 Cleanup sistema presenza');
      AppState.removeEventListener('change', handleAppStateChange);
      get().stopHeartbeat();
    };
  }
}));

export default usePresenceStore;
