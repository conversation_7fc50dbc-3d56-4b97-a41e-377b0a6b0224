import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  ScrollView,
  Alert,
  Platform
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation, useRoute } from '@react-navigation/native';
import { LinearGradient } from 'expo-linear-gradient';
import useChatStore from '../store/useChatStore';

const EphemeralMessagesScreen = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const { chatId, name } = route.params;
  const { updateChatSettings } = useChatStore();

  // Opzioni per la durata dei messaggi effimeri
  const durationOptions = [
    { id: 'off', label: 'Disattivati', description: 'I messaggi non scompariranno' },
    { id: '24h', label: '24 ore', description: 'I messaggi scompariranno dopo 24 ore' },
    { id: '7d', label: '7 giorni', description: 'I messaggi scompariranno dopo 7 giorni' },
    { id: '90d', label: '90 giorni', description: 'I messaggi scompariranno dopo 90 giorni' },
  ];

  const [selectedDuration, setSelectedDuration] = useState('off');

  const handleSave = async () => {
    try {
      // Prepara le impostazioni da salvare
      const settings = {
        ephemeralMessages: {
          enabled: selectedDuration !== 'off',
          duration: selectedDuration
        }
      };

      // Aggiorna le impostazioni della chat
      await updateChatSettings(chatId, settings);

      // 🔧 AVVIA/FERMA CONTROLLO PERIODICO MESSAGGI EFFIMERI (LATO APP)
      if (selectedDuration !== 'off') {
        // Avvia il controllo periodico quando i messaggi effimeri vengono abilitati
        useChatStore.getState().startEphemeralMessageCleanup();
        console.log('⏰ Controllo messaggi effimeri avviato per durata:', selectedDuration);
      } else {
        // Ferma il controllo quando vengono disabilitati
        useChatStore.getState().stopEphemeralMessageCleanup();
        console.log('⏹️ Controllo messaggi effimeri fermato');
      }

      // Mostra un messaggio di conferma
      Alert.alert(
        'Impostazioni salvate',
        selectedDuration !== 'off'
          ? `I messaggi effimeri sono stati attivati. I nuovi messaggi scompariranno automaticamente.`
          : 'I messaggi effimeri sono stati disattivati.',
        [{ text: 'OK', onPress: () => navigation.goBack() }]
      );
    } catch (error) {
      console.error('Errore nel salvataggio delle impostazioni:', error);
      Alert.alert('Errore', 'Impossibile salvare le impostazioni dei messaggi effimeri');
    }
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent={true} />

      <LinearGradient
        colors={['#1E88E5', '#D81B60']}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 0 }}
        style={styles.header}
      >
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color="#FFFFFF" />
        </TouchableOpacity>

        <Text style={styles.headerTitle}>Messaggi effimeri</Text>

        <TouchableOpacity
          style={styles.saveButton}
          onPress={handleSave}
        >
          <Text style={styles.saveButtonText}>Salva</Text>
        </TouchableOpacity>
      </LinearGradient>

      <ScrollView style={styles.content}>
        <View style={styles.infoSection}>
          <Ionicons name="time-outline" size={24} color="#1E88E5" style={styles.infoIcon} />
          <Text style={styles.infoText}>
            I messaggi effimeri scompariranno automaticamente dalla chat dopo il periodo di tempo selezionato.
            Questa impostazione si applica solo ai nuovi messaggi inviati dopo l'attivazione.
          </Text>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Durata dei messaggi</Text>

          {durationOptions.map((option) => (
            <TouchableOpacity
              key={option.id}
              style={styles.durationOption}
              onPress={() => setSelectedDuration(option.id)}
            >
              <View style={styles.durationTextContainer}>
                <Text style={styles.durationLabel}>{option.label}</Text>
                <Text style={styles.durationDescription}>{option.description}</Text>
              </View>

              <View style={styles.radioContainer}>
                <View style={styles.radioOuter}>
                  {selectedDuration === option.id && (
                    <LinearGradient
                      colors={['#1E88E5', '#D81B60']}
                      style={styles.radioInner}
                      start={{ x: 0, y: 0 }}
                      end={{ x: 1, y: 0 }}
                    />
                  )}
                </View>
              </View>
            </TouchableOpacity>
          ))}
        </View>

        <View style={styles.noteSection}>
          <Text style={styles.noteText}>
            Nota: Entrambi i partecipanti alla chat possono fare screenshot o salvare i messaggi prima che scompaiano.
          </Text>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#121212',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingTop: Platform.OS === 'ios' ? 50 : 30,
    paddingBottom: 10,
    paddingHorizontal: 10,
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    color: '#FFFFFF',
    fontSize: 20,
    fontWeight: 'bold',
  },
  saveButton: {
    padding: 8,
  },
  saveButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
  content: {
    flex: 1,
  },
  infoSection: {
    flexDirection: 'row',
    backgroundColor: '#1E1E1E',
    marginTop: 20,
    marginHorizontal: 10,
    borderRadius: 10,
    padding: 15,
  },
  infoIcon: {
    marginRight: 10,
  },
  infoText: {
    color: '#FFFFFF',
    fontSize: 14,
    flex: 1,
    lineHeight: 20,
  },
  section: {
    backgroundColor: '#1E1E1E',
    marginTop: 20,
    marginHorizontal: 10,
    borderRadius: 10,
    padding: 15,
  },
  sectionTitle: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 15,
  },
  durationOption: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 255, 255, 0.1)',
  },
  durationTextContainer: {
    flex: 1,
  },
  durationLabel: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '500',
  },
  durationDescription: {
    color: 'rgba(255, 255, 255, 0.7)',
    fontSize: 14,
    marginTop: 4,
  },
  radioContainer: {
    marginLeft: 10,
  },
  radioOuter: {
    width: 22,
    height: 22,
    borderRadius: 11,
    borderWidth: 2,
    borderColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
  },
  radioInner: {
    width: 14,
    height: 14,
    borderRadius: 7,
  },
  noteSection: {
    marginTop: 20,
    marginHorizontal: 10,
    marginBottom: 30,
    padding: 15,
  },
  noteText: {
    color: 'rgba(255, 255, 255, 0.7)',
    fontSize: 14,
    fontStyle: 'italic',
    textAlign: 'center',
  },
});

export default EphemeralMessagesScreen;
