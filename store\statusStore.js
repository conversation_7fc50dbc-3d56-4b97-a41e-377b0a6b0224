import { create } from 'zustand';
import axios from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';
import useAuthStore from './authStore';
import { API_URL } from '../config/api';
import mediaCompressionService from '../services/mediaCompressionService';
import cloudService from '../services/cloudService';

const useStatusStore = create((set, get) => ({
  statuses: [],
  myStatus: null,
  loading: false,
  error: null,

  // Carica gli stati dei contatti (usa endpoint storie)
  loadStatuses: async () => {
    const { user } = useAuthStore.getState();
    if (!user) return;

    try {
      set({ loading: true, error: null });

      // Ottieni il token di autenticazione
      const token = await AsyncStorage.getItem('@trendychat:token');

      // Richiesta al server HP ProLiant usando endpoint storie
      const response = await axios.get(`${API_URL}/stories`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      // Verifica che la risposta sia valida
      if (response.data && response.data.stories) {
        const stories = response.data.stories || [];

        // Converte le storie in formato stati
        const formattedStatuses = stories.map(story => ({
          id: story.id || story._id,
          userId: story.user?.id || story.user?._id || story.user,
          userName: story.user?.name || story.user?.displayName || 'Utente',
          userPhotoURL: story.user?.avatar || story.user?.photoURL,
          type: story.mediaType || 'image',
          mediaUrl: story.mediaUrl,
          text: story.content || '',
          createdAt: new Date(story.createdAt),
          expiresAt: new Date(story.expiresAt || new Date(Date.now() + 24 * 60 * 60 * 1000)),
          viewers: story.viewers || [],
          replies: []
        }));

        // Trova la mia storia più recente
        const myStory = formattedStatuses.find(status =>
          status.userId === (user.id || user.uid)
        );

        // Filtra le storie per escludere la mia (che va solo in myStatus)
        const otherStatuses = formattedStatuses.filter(status =>
          status.userId !== (user.id || user.uid)
        );

        set({
          statuses: otherStatuses,
          myStatus: myStory || null
        });
      } else {
        set({ statuses: [] });
      }
    } catch (error) {
      console.error('Errore nel caricamento degli stati:', error);
      set({ error: error.message || 'Impossibile caricare gli stati' });
    } finally {
      set({ loading: false });
    }
  },

  // Crea un nuovo stato (usa endpoint storie)
  createStatus: async (mediaUri, type = 'image', textData = null) => {
    const { user } = useAuthStore.getState();
    if (!user) return;

    try {
      set({ loading: true, error: null });

      let mediaUrl = null;

      if (type === 'text' && textData) {
        // Per gli stati testuali, creiamo un'immagine con il testo
        // Per ora usiamo il testo come contenuto
        mediaUrl = 'data:text/plain;base64,' + btoa(textData.text);
      } else if (mediaUri) {
        // Per gli stati con media, prima comprimiamo e poi carichiamo
        try {
          console.log('🔄 Avvio compressione media per storia...');

          // Comprimi il media per le storie
          const compressedMedia = await mediaCompressionService.compressMedia(
            mediaUri,
            type,
            'stories'
          );

          console.log(`✅ Media compresso: ${compressedMedia.compressionRatio}% di riduzione`);
          console.log(`📊 Da ${(compressedMedia.originalSize / 1024 / 1024).toFixed(2)}MB a ${(compressedMedia.compressedSize / 1024 / 1024).toFixed(2)}MB`);

          // Carica il media compresso sul server
          const cloudService = require('../services/cloudService').default;
          const uploadResult = await cloudService.uploadFile(compressedMedia.uri, user.id || user.uid);

          // Estrai l'URL dalla risposta del cloud service
          if (uploadResult && uploadResult.url) {
            mediaUrl = uploadResult.url;
          } else if (uploadResult && uploadResult.file && uploadResult.file.url) {
            mediaUrl = uploadResult.file.url;
          } else {
            console.error('Risposta cloud service non valida:', uploadResult);
            throw new Error('URL del media non trovato nella risposta');
          }

          console.log('🚀 URL media compresso caricato:', mediaUrl);
        } catch (uploadError) {
          console.error('❌ Errore nel caricamento del media:', uploadError);
          throw new Error('Impossibile caricare il file');
        }
      }

      if (!mediaUrl) {
        throw new Error('URL del media obbligatorio');
      }

      // Prepara i dati per l'endpoint storie
      const storyData = {
        content: textData?.text || '',
        mediaUrl: mediaUrl,
        mediaType: type === 'text' ? 'image' : type
      };

      // Ottieni il token di autenticazione
      const token = await AsyncStorage.getItem('@trendychat:token');

      // Invia i dati al server usando endpoint storie
      const response = await axios.post(`${API_URL}/stories`, storyData, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      // Verifica che la risposta sia valida
      if (response.data && response.data.success) {
        const newStory = response.data.story;

        // Converte la storia in formato stato per l'UI
        const myNewStatus = {
          id: newStory.id,
          userId: user.id || user.uid,
          userName: user.displayName,
          userPhotoURL: user.avatar || user.photoURL,
          type: type,
          mediaUrl: newStory.mediaUrl,
          text: newStory.content || '',
          createdAt: new Date(newStory.createdAt),
          expiresAt: new Date(newStory.expiresAt),
          viewers: newStory.viewers || [],
          replies: []
        };

        set(state => ({
          myStatus: myNewStatus
        }));

        return newStory.id;
      } else {
        throw new Error(response.data?.message || 'Errore nella creazione dello stato');
      }
    } catch (error) {
      console.error('Errore nella creazione dello stato:', error);
      set({ error: error.message || 'Impossibile creare lo stato' });
      throw error;
    } finally {
      set({ loading: false });
    }
  },

  // Visualizza uno stato
  viewStatus: async (statusId) => {
    const { user } = useAuthStore.getState();
    if (!user) return;

    try {
      // Invia la richiesta al server
      const response = await axios.post(`${API_URL}/status/view`, {
        statusId,
        userId: user.uid
      });

      // Verifica che la risposta sia valida
      if (!response.data || !response.data.success) {
        throw new Error(response.data?.message || 'Errore nella visualizzazione dello stato');
      }
    } catch (error) {
      console.error('Errore nella visualizzazione dello stato:', error);
      set({ error: error.message || 'Impossibile visualizzare lo stato' });
    }
  },

  // Rispondi a uno stato
  replyToStatus: async (statusId, replyText) => {
    const { user } = useAuthStore.getState();
    if (!user) return;

    try {
      set({ loading: true, error: null });

      const newReply = {
        userId: user.uid,
        userName: user.displayName,
        userPhotoURL: user.avatar || user.photoURL,
        text: replyText,
        timestamp: new Date().toISOString(),
      };

      // Invia la richiesta al server
      const response = await axios.post(`${API_URL}/status/reply`, {
        statusId,
        reply: newReply
      });

      // Verifica che la risposta sia valida
      if (response.data && response.data.success) {
        // Aggiorna lo stato locale
        set(state => ({
          statuses: state.statuses.map(status => {
            if (status.id === statusId) {
              return {
                ...status,
                replies: [...(status.replies || []), {
                  ...newReply,
                  timestamp: new Date(),
                }],
              };
            }
            return status;
          }),
        }));
      } else {
        throw new Error(response.data?.message || 'Errore nella risposta allo stato');
      }
    } catch (error) {
      console.error('Errore nella risposta allo stato:', error);
      set({ error: error.message || 'Impossibile rispondere allo stato' });
      throw error;
    } finally {
      set({ loading: false });
    }
  },

  // Ottieni le risposte a uno stato
  getStatusReplies: async (statusId) => {
    try {
      // Invia la richiesta al server
      const response = await axios.get(`${API_URL}/status/replies`, {
        params: { statusId }
      });

      // Verifica che la risposta sia valida
      if (response.data && response.data.success) {
        return response.data.replies || [];
      } else {
        throw new Error(response.data?.message || 'Errore nel recupero delle risposte');
      }
    } catch (error) {
      console.error('Errore nel recupero delle risposte:', error);
      set({ error: error.message || 'Impossibile recuperare le risposte' });
      return [];
    }
  },

  // Ottieni i visualizzatori di uno stato
  getStatusViewers: async (statusId) => {
    try {
      // Invia la richiesta al server
      const response = await axios.get(`${API_URL}/status/viewers`, {
        params: { statusId }
      });

      // Verifica che la risposta sia valida
      if (response.data && response.data.success) {
        return response.data.viewers || [];
      } else {
        throw new Error(response.data?.message || 'Errore nel recupero dei visualizzatori');
      }
    } catch (error) {
      console.error('Errore nel recupero dei visualizzatori:', error);
      set({ error: error.message || 'Impossibile recuperare i visualizzatori' });
      return [];
    }
  },

  // Elimina uno stato
  deleteStatus: async (statusId) => {
    try {
      set({ loading: true, error: null });

      // Invia la richiesta al server
      const response = await axios.delete(`${API_URL}/status/delete`, {
        data: { statusId }
      });

      // Verifica che la risposta sia valida
      if (response.data && response.data.success) {
        // Aggiorna lo stato locale
        set(state => ({
          myStatus: state.myStatus?.id === statusId ? null : state.myStatus,
          statuses: state.statuses.filter(status => status.id !== statusId)
        }));
      } else {
        throw new Error(response.data?.message || 'Errore nell\'eliminazione dello stato');
      }
    } catch (error) {
      console.error('Errore nell\'eliminazione dello stato:', error);
      set({ error: error.message || 'Impossibile eliminare lo stato' });
      throw error;
    } finally {
      set({ loading: false });
    }
  },

  // Imposta errore
  setError: (error) => set({ error }),

  // Pulisci errore
  clearError: () => set({ error: null }),
}));

export default useStatusStore;