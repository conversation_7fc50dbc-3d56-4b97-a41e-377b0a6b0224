import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Modal,
  Animated,
  Dimensions,
  Alert
} from 'react-native';
import { Ionicons, MaterialCommunityIcons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import * as ImagePicker from 'expo-image-picker';
import * as DocumentPicker from 'expo-document-picker';
// import * as Location from 'expo-location'; // Disabilitato - richiede nuovo APK
import { theme } from '../theme';

const { width, height } = Dimensions.get('window');

const AttachmentMenu = ({ visible, onClose, onSelectMedia }) => {
  const slideAnim = React.useRef(new Animated.Value(height)).current;

  React.useEffect(() => {
    if (visible) {
      Animated.spring(slideAnim, {
        toValue: 0,
        useNativeDriver: true,
        tension: 100,
        friction: 8,
      }).start();
    } else {
      Animated.spring(slideAnim, {
        toValue: height,
        useNativeDriver: true,
        tension: 100,
        friction: 8,
      }).start();
    }
  }, [visible]);

  const handleSelectImage = async () => {
    try {
      const permissionResult = await ImagePicker.requestMediaLibraryPermissionsAsync();

      if (!permissionResult.granted) {
        Alert.alert('Permesso negato', 'È necessario concedere il permesso per accedere alla galleria');
        return;
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: 'images',
        allowsEditing: true,
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        onSelectMedia({
          type: 'image',
          uri: result.assets[0].uri,
          name: 'image.jpg'
        });
        onClose();
      }
    } catch (error) {
      console.error('Errore nella selezione dell\'immagine:', error);
      Alert.alert('Errore', 'Impossibile selezionare l\'immagine');
    }
  };

  const handleSelectVideo = async () => {
    try {
      const permissionResult = await ImagePicker.requestMediaLibraryPermissionsAsync();

      if (!permissionResult.granted) {
        Alert.alert('Permesso negato', 'È necessario concedere il permesso per accedere alla galleria');
        return;
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: 'videos',
        allowsEditing: true,
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        onSelectMedia({
          type: 'video',
          uri: result.assets[0].uri,
          name: 'video.mp4'
        });
        onClose();
      }
    } catch (error) {
      console.error('Errore nella selezione del video:', error);
      Alert.alert('Errore', 'Impossibile selezionare il video');
    }
  };

  const handleSelectDocument = async () => {
    try {
      const result = await DocumentPicker.getDocumentAsync({
        type: '*/*',
        copyToCacheDirectory: true,
      });

      if (!result.canceled && result.assets[0]) {
        onSelectMedia({
          type: 'document',
          uri: result.assets[0].uri,
          name: result.assets[0].name,
          size: result.assets[0].size
        });
        onClose();
      }
    } catch (error) {
      console.error('Errore nella selezione del documento:', error);
      Alert.alert('Errore', 'Impossibile selezionare il documento');
    }
  };

  const handleSelectContact = async () => {
    try {
      // Per ora naviga alla lista contatti per selezionare
      onSelectMedia({
        type: 'contact_picker',
        action: 'open_contacts'
      });
      onClose();
    } catch (error) {
      console.error('Errore nella selezione del contatto:', error);
      Alert.alert('Errore', 'Impossibile aprire i contatti');
    }
  };

  const handleSelectLocation = async () => {
    Alert.alert(
      'Posizione GPS',
      'Funzionalità GPS non disponibile.\nRichiede nuovo development build con expo-location.',
      [
        { text: 'OK' }
      ]
    );
    onClose();
  };

  const menuItems = [
    {
      id: 'image',
      title: 'Foto',
      icon: 'image',
      iconType: 'Ionicons',
      color: '#E91E63',
      onPress: handleSelectImage
    },
    {
      id: 'video',
      title: 'Video',
      icon: 'videocam',
      iconType: 'Ionicons',
      color: '#FF5722',
      onPress: handleSelectVideo
    },
    {
      id: 'document',
      title: 'Documento',
      icon: 'document-text',
      iconType: 'Ionicons',
      color: '#2196F3',
      onPress: handleSelectDocument
    },
    {
      id: 'contact',
      title: 'Contatto',
      icon: 'person',
      iconType: 'Ionicons',
      color: '#4CAF50',
      onPress: handleSelectContact
    },
    {
      id: 'location',
      title: 'Posizione',
      icon: 'location',
      iconType: 'Ionicons',
      color: '#FF9800',
      onPress: handleSelectLocation
    }
  ];

  const renderMenuItem = (item) => (
    <TouchableOpacity
      key={item.id}
      style={styles.menuItem}
      onPress={item.onPress}
      activeOpacity={0.7}
    >
      <View style={[styles.iconContainer, { backgroundColor: item.color }]}>
        <Ionicons name={item.icon} size={24} color="#FFFFFF" />
      </View>
      <Text style={styles.menuItemText}>{item.title}</Text>
    </TouchableOpacity>
  );

  return (
    <Modal
      visible={visible}
      transparent
      animationType="none"
      onRequestClose={onClose}
    >
      <TouchableOpacity
        style={styles.overlay}
        activeOpacity={1}
        onPress={onClose}
      >
        <Animated.View
          style={[
            styles.menuContainer,
            {
              transform: [{ translateY: slideAnim }]
            }
          ]}
        >
          <LinearGradient
            colors={['#1A1A1A', '#2D2D2D']}
            style={styles.menuContent}
          >
            <View style={styles.handle} />

            <Text style={styles.menuTitle}>Invia Media</Text>

            <View style={styles.menuGrid}>
              {menuItems.map(renderMenuItem)}
            </View>
          </LinearGradient>
        </Animated.View>
      </TouchableOpacity>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  menuContainer: {
    width: '100%',
  },
  menuContent: {
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingTop: 8,
    paddingBottom: 34,
    paddingHorizontal: 20,
  },
  handle: {
    width: 40,
    height: 4,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    borderRadius: 2,
    alignSelf: 'center',
    marginBottom: 20,
  },
  menuTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#FFFFFF',
    textAlign: 'center',
    marginBottom: 24,
  },
  menuGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-around',
  },
  menuItem: {
    alignItems: 'center',
    marginBottom: 20,
    width: '30%',
  },
  iconContainer: {
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  menuItemText: {
    fontSize: 12,
    color: '#FFFFFF',
    textAlign: 'center',
    fontWeight: '500',
  },
});

export default AttachmentMenu;
