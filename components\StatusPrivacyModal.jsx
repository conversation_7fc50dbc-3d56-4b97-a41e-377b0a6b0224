import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  TouchableWithoutFeedback,
  Dimensions,
  Platform,
  ScrollView,
  Switch
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import AsyncStorage from '@react-native-async-storage/async-storage';

const { width, height } = Dimensions.get('window');

const StatusPrivacyModal = ({ visible, onClose }) => {
  const [privacySettings, setPrivacySettings] = useState({
    whoCanSee: 'everyone', // everyone, contacts, selected, nobody
    selectedContacts: [],
    excludedContacts: []
  });

  useEffect(() => {
    loadPrivacySettings();
  }, []);

  const loadPrivacySettings = async () => {
    try {
      const settings = await AsyncStorage.getItem('storyPrivacySettings');
      if (settings) {
        setPrivacySettings(JSON.parse(settings));
      }
    } catch (error) {
      console.error('Errore caricamento impostazioni privacy:', error);
    }
  };

  const savePrivacySettings = async (newSettings) => {
    try {
      await AsyncStorage.setItem('storyPrivacySettings', JSON.stringify(newSettings));
      setPrivacySettings(newSettings);
      console.log('✅ Impostazioni privacy storie salvate:', newSettings);
    } catch (error) {
      console.error('❌ Errore salvataggio impostazioni privacy:', error);
    }
  };

  const privacyOptions = [
    {
      id: 'everyone',
      icon: 'globe-outline',
      label: 'Tutti',
      description: 'Chiunque può vedere le tue storie',
      colors: ['#4CAF50', '#45A049']
    },
    {
      id: 'contacts',
      icon: 'people-outline',
      label: 'Solo contatti',
      description: 'Solo i tuoi contatti possono vedere le storie',
      colors: ['#1E88E5', '#D81B60']
    },
    {
      id: 'selected',
      icon: 'checkmark-circle-outline',
      label: 'Contatti selezionati',
      description: 'Solo contatti che scegli tu',
      colors: ['#FF9800', '#F57C00']
    },
    {
      id: 'nobody',
      icon: 'eye-off-outline',
      label: 'Nessuno',
      description: 'Le tue storie sono private',
      colors: ['#F44336', '#D32F2F']
    }
  ];

  if (!visible) return null;

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={onClose}
      statusBarTranslucent
    >
      <TouchableWithoutFeedback onPress={onClose}>
        <View style={styles.modalContainer}>
          <TouchableWithoutFeedback>
            <View style={styles.modalContent}>
              <LinearGradient
                colors={['#1E88E5', '#D81B60']}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 0 }}
                style={styles.header}
              >
                <Text style={styles.headerTitle}>Privacy storie</Text>
                <TouchableOpacity style={styles.closeButton} onPress={onClose}>
                  <Ionicons name="close" size={24} color="#FFFFFF" />
                </TouchableOpacity>
              </LinearGradient>

              <ScrollView style={styles.scrollContainer}>
                <View style={styles.categoryContainer}>
                  <Text style={styles.categoryTitle}>Chi può vedere le tue storie</Text>
                  <Text style={styles.categoryDescription}>
                    Scegli chi può visualizzare le storie che pubblichi
                  </Text>

                  {privacyOptions.map((option) => (
                    <TouchableOpacity
                      key={option.id}
                      style={[
                        styles.optionItem,
                        privacySettings.whoCanSee === option.id && styles.selectedOption
                      ]}
                      onPress={() => savePrivacySettings({
                        ...privacySettings,
                        whoCanSee: option.id
                      })}
                    >
                      <View style={styles.optionIconContainer}>
                        <LinearGradient
                          colors={option.colors}
                          start={{ x: 0, y: 0 }}
                          end={{ x: 1, y: 0 }}
                          style={styles.optionIconGradient}
                        >
                          <Ionicons name={option.icon} size={18} color="#FFFFFF" />
                        </LinearGradient>
                      </View>
                      <View style={styles.optionTextContainer}>
                        <Text style={styles.optionLabel}>{option.label}</Text>
                        <Text style={styles.optionDescription}>{option.description}</Text>
                      </View>
                      <View style={styles.radioContainer}>
                        <LinearGradient
                          colors={privacySettings.whoCanSee === option.id 
                            ? ['#1E88E5', '#D81B60'] 
                            : ['#2A2A2A', '#2A2A2A']
                          }
                          style={styles.radioButton}
                        >
                          {privacySettings.whoCanSee === option.id && (
                            <Ionicons name="checkmark" size={16} color="#FFFFFF" />
                          )}
                        </LinearGradient>
                      </View>
                    </TouchableOpacity>
                  ))}
                </View>

                {privacySettings.whoCanSee === 'selected' && (
                  <View style={styles.categoryContainer}>
                    <Text style={styles.categoryTitle}>Gestisci contatti</Text>
                    <TouchableOpacity
                      style={styles.optionItem}
                      onPress={() => {
                        onClose();
                        // TODO: Navigare alla schermata selezione contatti
                        alert('Funzionalità selezione contatti in arrivo');
                      }}
                    >
                      <View style={styles.optionIconContainer}>
                        <LinearGradient
                          colors={['#1E88E5', '#D81B60']}
                          style={styles.optionIconGradient}
                        >
                          <Ionicons name="person-add-outline" size={18} color="#FFFFFF" />
                        </LinearGradient>
                      </View>
                      <View style={styles.optionTextContainer}>
                        <Text style={styles.optionLabel}>Seleziona contatti</Text>
                        <Text style={styles.optionDescription}>
                          {privacySettings.selectedContacts.length} contatti selezionati
                        </Text>
                      </View>
                      <Ionicons name="chevron-forward" size={20} color="#AAAAAA" />
                    </TouchableOpacity>
                  </View>
                )}

                <View style={styles.categoryContainer}>
                  <Text style={styles.categoryTitle}>Opzioni avanzate</Text>
                  
                  <TouchableOpacity
                    style={styles.optionItem}
                    onPress={() => {
                      onClose();
                      alert('Funzionalità lista esclusi in arrivo');
                    }}
                  >
                    <View style={styles.optionIconContainer}>
                      <LinearGradient
                        colors={['#F44336', '#D32F2F']}
                        style={styles.optionIconGradient}
                      >
                        <Ionicons name="person-remove-outline" size={18} color="#FFFFFF" />
                      </LinearGradient>
                    </View>
                    <View style={styles.optionTextContainer}>
                      <Text style={styles.optionLabel}>Contatti esclusi</Text>
                      <Text style={styles.optionDescription}>
                        {privacySettings.excludedContacts.length} contatti esclusi
                      </Text>
                    </View>
                    <Ionicons name="chevron-forward" size={20} color="#AAAAAA" />
                  </TouchableOpacity>
                </View>
              </ScrollView>
            </View>
          </TouchableWithoutFeedback>
        </View>
      </TouchableWithoutFeedback>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
  },
  modalContent: {
    width: width * 0.9,
    maxHeight: height * 0.85,
    backgroundColor: '#1E1E1E',
    borderRadius: 20,
    overflow: 'hidden',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 16,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  closeButton: {
    padding: 8,
  },
  scrollContainer: {
    maxHeight: height * 0.7,
  },
  categoryContainer: {
    marginBottom: 24,
    paddingHorizontal: 16,
  },
  categoryTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 4,
  },
  categoryDescription: {
    fontSize: 14,
    color: '#AAAAAA',
    marginBottom: 16,
    lineHeight: 20,
  },
  optionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#2A2A2A',
  },
  selectedOption: {
    backgroundColor: 'rgba(30, 136, 229, 0.1)',
    borderRadius: 12,
    marginHorizontal: -8,
    paddingHorizontal: 8,
  },
  optionIconContainer: {
    marginRight: 16,
  },
  optionIconGradient: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  optionTextContainer: {
    flex: 1,
  },
  optionLabel: {
    fontSize: 16,
    color: '#FFFFFF',
    marginBottom: 2,
  },
  optionDescription: {
    fontSize: 12,
    color: '#AAAAAA',
  },
  radioContainer: {
    marginLeft: 12,
  },
  radioButton: {
    width: 24,
    height: 24,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default StatusPrivacyModal;
