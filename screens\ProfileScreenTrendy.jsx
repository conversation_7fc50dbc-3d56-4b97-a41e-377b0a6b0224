import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  ScrollView,
  StatusBar,
  Platform,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import useAuthStore from '../store/authStore';
import useMediaStore from '../store/mediaStore';
import cloudService from '../services/cloudService';

const ProfileScreenTrendy = () => {
  const navigation = useNavigation();
  const { user, loading, error, updateUserProfile, logout } = useAuthStore();
  const { pickImage, loading: mediaLoading } = useMediaStore();
  const [isLoading, setIsLoading] = useState(false);

  const handleUpdatePhoto = async () => {
    try {
      const options = [
        { text: 'Scatta foto', onPress: () => handleTakePhoto() },
        { text: 'Scegli dalla galleria', onPress: () => handlePickPhoto() },
        { text: 'Annulla', style: 'cancel' },
      ];

      Alert.alert('Aggiorna foto profilo', 'Scegli un\'opzione', options);
    } catch (error) {
      Alert.alert('Errore', 'Impossibile aggiornare la foto profilo');
    }
  };

  const handleTakePhoto = async () => {
    try {
      setIsLoading(true);
      // Implementazione per scattare una foto
      Alert.alert('Info', 'Funzionalità in arrivo');
    } catch (error) {
      Alert.alert('Errore', 'Impossibile scattare la foto');
    } finally {
      setIsLoading(false);
    }
  };

  const handlePickPhoto = async () => {
    try {
      setIsLoading(true);

      // Seleziona l'immagine dalla galleria
      const imageUri = await pickImage();
      if (!imageUri) {
        setIsLoading(false);
        return;
      }

      // Non mostriamo un alert che blocca l'interfaccia
      // Alert.alert('Caricamento', 'Caricamento dell\'immagine in corso...');

      // Invece, l'indicatore di caricamento è già visibile grazie a setIsLoading(true)

      // Carica l'immagine sul server cloud
      // La compressione avviene automaticamente nel mediaStore
      const { uploadImageToCloud } = useMediaStore.getState();
      const result = await uploadImageToCloud(imageUri, 'profile');

      if (!result) {
        throw new Error('Errore durante il caricamento dell\'immagine');
      }

      console.log('Immagine caricata sul server:', result);

      // Assicurati che il risultato contenga un URL valido
      if (!result.url && result.localUri) {
        console.log('Nessun URL remoto disponibile, utilizzo URI locale');
        result.url = result.localUri;
      }

      // Elimina la vecchia immagine dal server se esiste
      if (user?.avatar && user.avatar.includes('192.168.1.66:3001')) {
        try {
          console.log('Eliminazione della vecchia immagine dal server:', user.avatar);
          await cloudService.deleteFileByUrl(user.avatar);
          console.log('Vecchia immagine eliminata con successo');
        } catch (deleteError) {
          console.error('Errore durante l\'eliminazione della vecchia immagine:', deleteError);
          // Continuiamo comunque anche se l'eliminazione fallisce
        }
      }

      // Aggiorna il profilo con l'URL remoto dell'immagine
      await updateUserProfile({
        avatar: result.url,
        photoURL: result.url, // Manteniamo anche photoURL per compatibilità
        localAvatar: imageUri // Salviamo anche l'URI locale per la cache
      });

      Alert.alert('Successo', 'Foto profilo aggiornata con successo');
    } catch (error) {
      console.error('Errore durante l\'aggiornamento della foto profilo:', error);

      // Mostra un messaggio di errore più dettagliato
      let errorMessage = 'Impossibile aggiornare la foto profilo';

      if (error.message && error.message.includes('Network request failed')) {
        errorMessage = 'Errore di connessione al server. Verifica la tua connessione di rete e riprova.';
      } else if (error.message) {
        errorMessage = `Errore: ${error.message}`;
      }

      Alert.alert(
        'Errore',
        errorMessage,
        [
          {
            text: 'Riprova',
            onPress: () => handlePickPhoto()
          },
          {
            text: 'Annulla',
            style: 'cancel'
          }
        ]
      );
    } finally {
      setIsLoading(false);
    }
  };

  const handleLogout = async () => {
    Alert.alert(
      'Logout',
      'Sei sicuro di voler uscire?',
      [
        {
          text: 'Annulla',
          style: 'cancel'
        },
        {
          text: 'Logout',
          style: 'destructive',
          onPress: async () => {
            try {
              setIsLoading(true);
              await logout();
              navigation.reset({
                index: 0,
                routes: [{ name: 'Login' }],
              });
            } catch (error) {
              console.error('Errore durante il logout:', error);
              Alert.alert('Errore', 'Impossibile effettuare il logout');
            } finally {
              setIsLoading(false);
            }
          }
        }
      ]
    );
  };

  if (loading || isLoading || mediaLoading) {
    return (
      <View style={styles.loadingContainer}>
        <LinearGradient
          colors={['#1E88E5', '#D81B60']}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 0 }}
          style={styles.loadingGradient}
        >
          <ActivityIndicator size="large" color="#FFFFFF" />
          <Text style={styles.loadingText}>
            {isLoading ? 'Caricamento immagine in corso...' : 'Caricamento...'}
          </Text>
        </LinearGradient>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent />

      {/* Header con gradiente */}
      <LinearGradient
        colors={['#1E88E5', '#D81B60']}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 0 }}
        style={styles.header}
      >
        <TouchableOpacity
          onPress={() => navigation.goBack()}
          style={styles.backButton}
        >
          <Ionicons name="arrow-back" size={24} color="#FFFFFF" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Profilo</Text>
        <View style={{ width: 24 }} />
      </LinearGradient>

      <ScrollView style={styles.scrollContainer}>
        {/* Sezione foto profilo */}
        <View style={styles.profileSection}>
          <TouchableOpacity
            style={styles.photoContainer}
            onPress={handleUpdatePhoto}
          >
            <LinearGradient
              colors={['#1E88E5', '#D81B60']}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
              style={styles.photoGradient}
            >
              <View style={styles.photoInner}>
                {/* Log per debug dell'URL dell'immagine */}
                {console.log('DEBUG - URL immagine profilo:', {
                  localAvatar: user?.localAvatar,
                  avatar: user?.avatar,
                  photoURL: user?.photoURL
                })}
                <Image
                  source={{
                    uri: user?.localAvatar || user?.avatar || user?.photoURL || 'https://via.placeholder.com/150',
                    cache: 'reload' // Forza il ricaricamento dell'immagine
                  }}
                  style={styles.photo}
                  onError={(e) => {
                    // Log dell'errore di caricamento
                    console.log('Errore nel caricamento dell\'immagine:', e.nativeEvent.error);
                    console.log('URI che ha causato l\'errore:', user?.localAvatar || user?.avatar || user?.photoURL);

                    // Se l'immagine locale non è disponibile, prova a caricare l'immagine remota
                    if (user?.localAvatar && user?.avatar) {
                      console.log('Errore nel caricamento dell\'immagine locale, uso l\'URL remoto');
                      // Qui potremmo aggiornare lo stato per usare l'URL remoto
                    }
                  }}
                />
              </View>
            </LinearGradient>
            <View style={styles.editPhotoButton}>
              <LinearGradient
                colors={['#1E88E5', '#D81B60']}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 0 }}
                style={styles.editPhotoGradient}
              >
                <Ionicons name="camera" size={16} color="#FFFFFF" />
              </LinearGradient>
            </View>
          </TouchableOpacity>
          <Text style={styles.name}>{user?.displayName || 'Utente TrendyChat'}</Text>
          <Text style={styles.status}>{user?.status || 'Hey, sto usando TrendyChat!'}</Text>
          <TouchableOpacity
            style={styles.editProfileButton}
            onPress={() => navigation.navigate('EditProfile')}
          >
            <LinearGradient
              colors={['#1E88E5', '#D81B60']}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 0 }}
              style={styles.editProfileGradient}
            >
              <Text style={styles.editProfileButtonText}>Modifica profilo</Text>
            </LinearGradient>
          </TouchableOpacity>
        </View>

        {/* Sezione informazioni utente */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Informazioni utente</Text>
          <View style={styles.infoItem}>
            <Ionicons name="call-outline" size={22} color="#1E88E5" style={styles.infoIcon} />
            <View style={styles.infoContent}>
              <Text style={styles.infoLabel}>Telefono</Text>
              <Text style={styles.infoValue}>{user?.phoneNumber || '+39 XXX XXXXXXX'}</Text>
            </View>
          </View>
          <View style={styles.infoItem}>
            <Ionicons name="information-circle-outline" size={22} color="#1E88E5" style={styles.infoIcon} />
            <View style={styles.infoContent}>
              <Text style={styles.infoLabel}>Info</Text>
              <Text style={styles.infoValue}>{user?.bio || 'Hey, sto usando TrendyChat!'}</Text>
            </View>
          </View>
        </View>

        {/* Sezione azioni */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Azioni</Text>
          <TouchableOpacity
            style={styles.actionItem}
            onPress={() => navigation.navigate('SettingsList')}
          >
            <View style={styles.actionIconContainer}>
              <LinearGradient
                colors={['#1E88E5', '#D81B60']}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 0 }}
                style={styles.actionIconGradient}
              >
                <Ionicons name="settings-outline" size={22} color="#FFFFFF" />
              </LinearGradient>
            </View>
            <View style={styles.actionContent}>
              <Text style={styles.actionLabel}>Impostazioni</Text>
              <Ionicons name="chevron-forward" size={20} color="#AAAAAA" />
            </View>
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.actionItem}
            onPress={() => navigation.navigate('Privacy')}
          >
            <View style={styles.actionIconContainer}>
              <LinearGradient
                colors={['#1E88E5', '#D81B60']}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 0 }}
                style={styles.actionIconGradient}
              >
                <Ionicons name="lock-closed-outline" size={22} color="#FFFFFF" />
              </LinearGradient>
            </View>
            <View style={styles.actionContent}>
              <Text style={styles.actionLabel}>Privacy</Text>
              <Ionicons name="chevron-forward" size={20} color="#AAAAAA" />
            </View>
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.actionItem}
            onPress={() => navigation.navigate('Help')}
          >
            <View style={styles.actionIconContainer}>
              <LinearGradient
                colors={['#1E88E5', '#D81B60']}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 0 }}
                style={styles.actionIconGradient}
              >
                <Ionicons name="help-circle-outline" size={22} color="#FFFFFF" />
              </LinearGradient>
            </View>
            <View style={styles.actionContent}>
              <Text style={styles.actionLabel}>Aiuto</Text>
              <Ionicons name="chevron-forward" size={20} color="#AAAAAA" />
            </View>
          </TouchableOpacity>
        </View>

        {/* Pulsante logout */}
        <TouchableOpacity
          style={styles.logoutButton}
          onPress={handleLogout}
        >
          <View style={styles.logoutIconContainer}>
            <LinearGradient
              colors={['#D81B60', '#D81B60']}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 0 }}
              style={styles.logoutIconGradient}
            >
              <Ionicons name="log-out-outline" size={22} color="#FFFFFF" />
            </LinearGradient>
          </View>
          <View style={styles.logoutContent}>
            <Text style={styles.logoutLabel}>Esci</Text>
            <Ionicons name="chevron-forward" size={20} color="#AAAAAA" />
          </View>
        </TouchableOpacity>

        {/* Informazioni app */}
        <View style={styles.appInfoContainer}>
          <Text style={styles.appName}>TrendyChat</Text>
          <Text style={styles.appVersion}>Versione 1.0.0</Text>
          <Text style={styles.copyright}>© 2023 TrendyChat. Tutti i diritti riservati.</Text>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#121212',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#121212',
  },
  loadingGradient: {
    padding: 20,
    borderRadius: 10,
    alignItems: 'center',
    justifyContent: 'center',
    width: '80%',
    maxWidth: 300,
  },
  loadingText: {
    color: '#FFFFFF',
    marginTop: 10,
    fontSize: 16,
    fontWeight: '500',
    textAlign: 'center',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: Platform.OS === 'ios' ? 50 : 40,
    paddingBottom: 16,
    paddingHorizontal: 16,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  backButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  scrollContainer: {
    flex: 1,
  },
  profileSection: {
    alignItems: 'center',
    padding: 20,
    paddingTop: 30,
  },
  photoContainer: {
    position: 'relative',
    marginBottom: 16,
  },
  photoGradient: {
    width: 150,
    height: 150,
    borderRadius: 75,
    padding: 3,
    justifyContent: 'center',
    alignItems: 'center',
  },
  photoInner: {
    width: '100%',
    height: '100%',
    borderRadius: 75,
    backgroundColor: '#121212',
    overflow: 'hidden',
  },
  photo: {
    width: '100%',
    height: '100%',
    borderRadius: 75,
  },
  editPhotoButton: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    width: 40,
    height: 40,
    borderRadius: 20,
    overflow: 'hidden',
  },
  editPhotoGradient: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  name: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 8,
  },
  status: {
    fontSize: 16,
    color: '#AAAAAA',
    marginBottom: 16,
    fontStyle: 'italic',
  },
  editProfileButton: {
    borderRadius: 25,
    overflow: 'hidden',
    marginTop: 8,
  },
  editProfileGradient: {
    paddingHorizontal: 20,
    paddingVertical: 10,
  },
  editProfileButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '500',
  },
  section: {
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#2A2A2A',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 16,
  },
  infoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  infoIcon: {
    marginRight: 16,
  },
  infoContent: {
    flex: 1,
  },
  infoLabel: {
    fontSize: 14,
    color: '#AAAAAA',
    marginBottom: 4,
  },
  infoValue: {
    fontSize: 16,
    color: '#FFFFFF',
  },
  actionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  actionIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    overflow: 'hidden',
    marginRight: 16,
  },
  actionIconGradient: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  actionContent: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#2A2A2A',
  },
  actionLabel: {
    fontSize: 16,
    color: '#FFFFFF',
  },
  logoutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#2A2A2A',
  },
  logoutIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    overflow: 'hidden',
    marginRight: 16,
  },
  logoutIconGradient: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  logoutContent: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  logoutLabel: {
    fontSize: 16,
    color: '#D81B60',
    fontWeight: '500',
  },
  appInfoContainer: {
    padding: 20,
    alignItems: 'center',
  },
  appName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 8,
  },
  appVersion: {
    fontSize: 14,
    color: '#AAAAAA',
    marginBottom: 8,
  },
  copyright: {
    fontSize: 12,
    color: '#666666',
    textAlign: 'center',
  },
});

export default ProfileScreenTrendy;
