import React, { useEffect, useRef, useState } from 'react';
import {
  View,
  Text,
  Image,
  TouchableOpacity,
  StyleSheet,
  Animated,
  Dimensions,
  Platform
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import LottieView from 'lottie-react-native';
import { theme } from '../theme';
import formatDate from '../utils/formatDate';
import VoiceMessagePlayer from './chat/VoiceMessagePlayer';
import VideoMessage from './chat/VideoMessage';
import DocumentMessage from './chat/DocumentMessage';
import ReplyMessage from './chat/ReplyMessage';
import MessageReactions from './chat/MessageReactions';
import ReactionSelector from './chat/ReactionSelector';

const { width } = Dimensions.get('window');

const ChatBubble = ({
  message,
  isOwn,
  onLongPress,
  onPress,
  onReplyPress,
  onReactionPress,
  onImagePress,
  showTime = true,
  showStatus = true,
  status = 'sent'
}) => {
  const scaleAnim = useRef(new Animated.Value(0)).current;
  const opacityAnim = useRef(new Animated.Value(0)).current;
  const [showReactionSelector, setShowReactionSelector] = useState(false);
  const [selectorPosition, setSelectorPosition] = useState({ x: 0, y: 0 });

  useEffect(() => {
    Animated.parallel([
      Animated.spring(scaleAnim, {
        toValue: 1,
        useNativeDriver: true,
        tension: 50,
        friction: 7
      }),
      Animated.timing(opacityAnim, {
        toValue: 1,
        duration: 200,
        useNativeDriver: true
      })
    ]).start();
  }, []);

  const renderMessageContent = () => {

    // Se il tipo non è definito ma c'è del testo, mostra il testo
    if (!message.type && message.text) {
      return (
        <Text
          style={[styles.messageText, isOwn && styles.ownMessageText]}
          allowFontScaling={true}
        >
          {message.text}
        </Text>
      );
    }

    // Se il tipo non è definito ma c'è un'immagine, mostra l'immagine
    if (!message.type && message.image) {
      return (
        <Image
          source={{ uri: message.image }}
          style={styles.image}
          resizeMode="cover"
        />
      );
    }

    // Se il tipo non è definito ma c'è un audio, mostra l'audio
    if (!message.type && message.audio) {
      return (
        <VoiceMessagePlayer
          audioUri={message.audio}
          duration={message.duration || 0}
          isOwnMessage={isOwn}
        />
      );
    }

    switch (message.type) {
      case 'text':
        return (
          <Text
            style={[styles.messageText, isOwn && styles.ownMessageText]}
            allowFontScaling={true}
          >
            {message.text}
          </Text>
        );
      case 'image':
        const imageUri = message.imageUrl || message.uri || message.image;
        return (
          <TouchableOpacity
            onPress={() => {
              console.log('🖼️ Immagine cliccata:', imageUri);
              // Naviga al MediaViewer per visualizzare l'immagine fullscreen
              if (onImagePress) {
                onImagePress(imageUri);
              }
            }}
            onLongPress={onLongPress}
            activeOpacity={0.8}
          >
            <Image
              source={{ uri: imageUri }}
              style={styles.image}
              resizeMode="cover"
            />
          </TouchableOpacity>
        );
      case 'video':
        return (
          <VideoMessage
            videoUri={message.videoUrl || message.uri}
            thumbnail={message.thumbnail}
            isOwnMessage={isOwn}
          />
        );
      case 'audio':
        return (
          <VoiceMessagePlayer
            audioUri={message.audioUrl || message.uri || message.audio}
            duration={message.duration || 0}
            isOwnMessage={isOwn}
          />
        );
      case 'document':
        return (
          <DocumentMessage
            document={{
              uri: message.documentUrl || message.uri,
              name: message.fileName || message.name,
              size: message.size,
              type: message.fileType
            }}
            isOwnMessage={isOwn}
          />
        );
      default:
        // Fallback: se c'è del testo, mostralo
        if (message.text) {
          return (
            <Text
              style={[styles.messageText, isOwn && styles.ownMessageText]}
              allowFontScaling={true}
            >
              {message.text}
            </Text>
          );
        }
        return null;
    }
  };

  const renderStatus = () => {
    if (!showStatus) return null;

    switch (status) {
      case 'sent':
        return (
          <Text style={styles.statusText}>✓</Text>
        );
      case 'delivered':
        return (
          <Text style={styles.statusText}>✓✓</Text>
        );
      case 'read':
        return (
          <Text style={[styles.statusText, styles.statusTextRead]}>✓✓</Text>
        );
      default:
        return null;
    }
  };

  const handleDoubleTap = (event) => {
    // Mostra il selettore di reazioni
    const { pageX, pageY } = event.nativeEvent;
    setSelectorPosition({ x: pageX, y: pageY });
    setShowReactionSelector(true);
  };

  const handleReaction = (reactionType) => {
    if (onReactionPress) {
      onReactionPress(message.id, reactionType);
    }
  };

  return (
    <Animated.View
      style={[
        styles.container,
        isOwn && styles.ownContainer,
        {
          transform: [{ scale: scaleAnim }],
          opacity: opacityAnim
        }
      ]}
    >
      <TouchableOpacity
        onPress={(event) => {
          // Gestisci il doppio tap per le reazioni
          const now = new Date().getTime();
          const DOUBLE_PRESS_DELAY = 300;

          if (this.lastTap && (now - this.lastTap) < DOUBLE_PRESS_DELAY) {
            // Doppio tap rilevato
            handleDoubleTap(event);
            this.lastTap = null;
          } else {
            this.lastTap = now;
            // Gestisci il tap singolo dopo un breve ritardo
            this.singleTapTimer = setTimeout(() => {
              if (this.lastTap) {
                if (onPress) onPress();
                this.lastTap = null;
              }
            }, DOUBLE_PRESS_DELAY);
          }
        }}
        onLongPress={onLongPress}
        activeOpacity={0.7}
        style={styles.touchable}
      >
        {isOwn ? (
          <LinearGradient
            colors={['#1A5CFF', '#00B8FF']}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 0 }}
            style={[styles.bubble, styles.ownBubble]}
          >
            {message.replyTo && (
              <ReplyMessage
                replyTo={message.replyTo}
                isOwnMessage={true}
                onPress={() => onReplyPress && onReplyPress(message.replyTo.id)}
              />
            )}
            {renderMessageContent()}
          </LinearGradient>
        ) : (
          <View style={[styles.bubble]}>
            {message.replyTo && (
              <ReplyMessage
                replyTo={message.replyTo}
                isOwnMessage={false}
                onPress={() => onReplyPress && onReplyPress(message.replyTo.id)}
              />
            )}
            {renderMessageContent()}
          </View>
        )}
        <View style={[styles.footer, isOwn && styles.ownFooter]}>
          {showTime && (
            <Text style={[styles.time, isOwn && styles.ownTime]}>
              {formatDate(message.timestamp)}
            </Text>
          )}
          {isOwn && renderStatus()}
        </View>
      </TouchableOpacity>

      {message.reactions && (
        <MessageReactions
          reactions={message.reactions}
          isOwnMessage={isOwn}
          onReactionPress={(reactionType) => handleReaction(reactionType)}
        />
      )}

      <ReactionSelector
        isVisible={showReactionSelector}
        onSelect={handleReaction}
        onClose={() => setShowReactionSelector(false)}
        position={selectorPosition}
      />
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    maxWidth: width * 0.75,
    marginVertical: 4,
    marginHorizontal: 8,
    alignSelf: 'flex-start'
  },
  ownContainer: {
    alignSelf: 'flex-end'
  },
  touchable: {
    maxWidth: '100%',
  },
  bubble: {
    backgroundColor: '#1E3A5F',
    borderRadius: theme.borderRadius.lg,
    padding: theme.spacing.md,
    ...theme.shadows.small,
    borderTopLeftRadius: 0,
  },
  ownBubble: {
    borderTopRightRadius: 0,
    borderTopLeftRadius: theme.borderRadius.lg,
  },
  messageText: {
    ...theme.typography.body,
    color: '#E0E7FF',
    fontSize: 16,
    lineHeight: 22,
    includeFontPadding: false,
    textAlignVertical: 'center'
  },
  ownMessageText: {
    color: '#FFFFFF'
  },
  image: {
    width: width * 0.6,
    height: width * 0.6,
    borderRadius: theme.borderRadius.md
  },
  videoContainer: {
    width: width * 0.6,
    height: width * 0.6,
    borderRadius: theme.borderRadius.md,
    overflow: 'hidden'
  },
  videoThumbnail: {
    width: '100%',
    height: '100%'
  },
  playButton: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center'
  },
  playIcon: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center'
  },
  playIconText: {
    color: '#FFFFFF',
    fontSize: 24
  },
  audioContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    minWidth: 150
  },
  waveform: {
    width: 100,
    height: 30,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.1)',
    borderRadius: 15
  },
  waveformText: {
    fontSize: 24,
    color: theme.colors.primary
  },
  audioDuration: {
    marginLeft: theme.spacing.sm,
    color: theme.colors.textSecondary
  },
  footer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
    marginHorizontal: 8
  },
  ownFooter: {
    justifyContent: 'flex-end',
  },
  time: {
    ...theme.typography.caption,
    color: '#A5B4FC'
  },
  ownTime: {
    color: 'rgba(255, 255, 255, 0.8)'
  },
  statusText: {
    fontSize: 14,
    marginLeft: 4,
    color: theme.colors.textSecondary
  },
  doubleCheck: {
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: 4,
  },
  checkOverlap: {
    marginRight: -8,
  },
  statusTextRead: {
    color: '#34B7F1'
  }
});

export default ChatBubble;