import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  SafeAreaView,
  StatusBar,
  Platform,
  Dimensions,
  Animated,
} from 'react-native';
import { Ionicons, MaterialCommunityIcons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { useNavigation } from '@react-navigation/native';
import useAuthStore from '../store/authStore';
import useTrendyCoinStore from '../store/trendyCoinStore';

const { width, height } = Dimensions.get('window');

const EarnScreen = () => {
  const navigation = useNavigation();
  const { user } = useAuthStore();
  const {
    availableCoins,
    pendingCoins,
    todayEarned,
    weeklyEarned,
    nextPayout,
    loading,
    loadTrendyCoins,
    checkWeeklyPayout,
    processWeeklyPayout
  } = useTrendyCoinStore();

  const [watchedToday, setWatchedToday] = useState(0);
  const [coinAnimation] = useState(new Animated.Value(0));

  useEffect(() => {
    loadUserStats();
    startCoinAnimation();

    // Auto-refresh rimosso per evitare lampeggiamenti
  }, []);

  const loadUserStats = async () => {
    try {
      console.log('💰 EarnScreen: Caricamento statistiche TrendyCoin...');
      console.log(`👤 User ID: ${user?.id}`);
      console.log(`📱 Display Name: ${user?.displayName}`);
      console.log(`🌐 API URL: ${process.env.EXPO_PUBLIC_API_URL || 'http://192.168.1.66:3001/api'}`);
      console.log(`🔗 Server HP: http://192.168.1.66:3001`);

      await loadTrendyCoins();
      await checkWeeklyPayout();
      setWatchedToday(15); // TODO: Carica dal server

      console.log('✅ EarnScreen: Statistiche caricate con successo');
    } catch (error) {
      console.error('❌ EarnScreen: Errore caricamento statistiche:', error);
    }
  };

  const startCoinAnimation = () => {
    Animated.loop(
      Animated.sequence([
        Animated.timing(coinAnimation, {
          toValue: 1,
          duration: 2000,
          useNativeDriver: true,
        }),
        Animated.timing(coinAnimation, {
          toValue: 0,
          duration: 2000,
          useNativeDriver: true,
        }),
      ])
    ).start();
  };

  const handleWatchAds = () => {
    Alert.alert(
      '🎬 Guarda Video',
      'Guarda video sponsorizzati per guadagnare TrendyCoin!\n\n💰 Ricompensa: 10 TrendyCoin per 50 minuti',
      [
        { text: 'Annulla', style: 'cancel' },
        { text: 'Inizia', onPress: startWatchingAds },
      ]
    );
  };

  const startWatchingAds = () => {
    console.log('🎬 Avvio video ads...');
    navigation.navigate('VideoPlayer');
  };

  const handlePayout = () => {
    if (weeklyEarned < 10) {
      Alert.alert(
        '💰 TrendyCoin insufficienti',
        `Hai bisogno di almeno 10 TrendyCoin per effettuare un prelievo.\n\nAttualmente hai: ${weeklyEarned} TrendyCoin\nMancano: ${10 - weeklyEarned} TrendyCoin`,
        [{ text: 'OK' }]
      );
      return;
    }

    Alert.alert(
      '💰 Trasferisci TrendyCoin',
      `Vuoi trasferire ${weeklyEarned} TrendyCoin guadagnati questa settimana al TrendyWallet?\n\nSaranno disponibili nella sezione "Guadagni Pubblicità".`,
      [
        { text: 'Annulla', style: 'cancel' },
        { text: 'Trasferisci', onPress: processPayout },
      ]
    );
  };

  const processPayout = async () => {
    try {
      console.log('💰 Processando payout...');
      const payoutAmount = await processWeeklyPayout();

      Alert.alert(
        '🎉 Trasferimento Completato!',
        `${payoutAmount} TrendyCoin sono stati trasferiti nel TrendyWallet!\n\n💰 Vai in "Guadagni Pubblicità" per vederli.`,
        [{ text: 'Fantastico!' }]
      );

      // Ricarica i dati aggiornati
      await loadUserStats();
    } catch (error) {
      console.error('❌ Errore payout:', error);
      Alert.alert(
        '❌ Errore Payout',
        'Si è verificato un errore durante il prelievo. Riprova più tardi.',
        [{ text: 'OK' }]
      );
    }
  };

  const handleWeeklyPayout = () => {
    const nextPayoutDate = nextPayout ? nextPayout.toLocaleDateString('it-IT') : 'Domenica';

    Alert.alert(
      '💰 Pagamento Settimanale',
      `💰 Saldo Disponibile: ${availableCoins} TrendyCoin\n⏳ Saldo Pendente: ${pendingCoins} TrendyCoin\n\n📅 Prossimo pagamento: ${nextPayoutDate}\n\nI TrendyCoin pendenti saranno trasferiti nel saldo disponibile ogni domenica.`,
      [{ text: 'OK' }]
    );
  };

  const renderStatsCard = (title, value, icon, color, subtitle) => (
    <View style={[styles.statsCard, { borderLeftColor: color }]}>
      <View style={styles.statsHeader}>
        <Ionicons name={icon} size={24} color={color} />
        <Text style={styles.statsTitle}>{title}</Text>
      </View>
      <Text style={[styles.statsValue, { color }]}>{value}</Text>
      {subtitle && <Text style={styles.statsSubtitle}>{subtitle}</Text>}
    </View>
  );

  const coinRotation = coinAnimation.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg'],
  });

  const coinScale = coinAnimation.interpolate({
    inputRange: [0, 0.5, 1],
    outputRange: [1, 1.2, 1],
  });

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#1E88E5" />
        <Text style={styles.loadingText}>Caricamento...</Text>
      </View>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent={true} />

      {/* Header con stile TrendyChat ESATTO */}
      <LinearGradient
        colors={['#1E88E5', '#D81B60']}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 0 }}
        style={styles.header}
      >
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color="#FFFFFF" />
        </TouchableOpacity>

        <Text style={styles.headerTitle}>💰 Guadagna</Text>

        <TouchableOpacity style={styles.infoButton}>
          <Ionicons name="information-circle-outline" size={24} color="#FFFFFF" />
        </TouchableOpacity>
      </LinearGradient>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Saldo TrendyCoin */}
        <LinearGradient
          colors={['#1E88E5', '#D81B60']}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={styles.balanceCard}
        >
          <View style={styles.balanceHeader}>
            <Text style={styles.balanceTitle}>I tuoi TrendyCoin</Text>
            <Animated.View
              style={[
                styles.coinIcon,
                {
                  transform: [
                    { rotate: coinRotation },
                    { scale: coinScale },
                  ],
                },
              ]}
            >
              <Ionicons name="ellipse" size={32} color="#FFD700" />
            </Animated.View>
          </View>

          <View style={styles.balanceContainer}>
            <View style={styles.balanceItem}>
              <Text style={styles.balanceAmount}>{availableCoins.toLocaleString()}</Text>
              <Text style={styles.balanceSubtitle}>TrendyCoin Disponibili</Text>
            </View>
          </View>
        </LinearGradient>

        {/* Statistiche */}
        <View style={styles.statsContainer}>
          {renderStatsCard(
            'Oggi',
            `+${todayEarned}`,
            'today',
            '#4CAF50',
            'TrendyCoin guadagnati'
          )}
          {renderStatsCard(
            'Questa settimana',
            `+${weeklyEarned}`,
            'calendar',
            '#FF9800',
            'TrendyCoin totali'
          )}
          {renderStatsCard(
            'Video guardati',
            `${watchedToday} min`,
            'play-circle',
            '#9C27B0',
            'Oggi'
          )}
        </View>

        {/* Azioni principali */}
        <View style={styles.actionsContainer}>
          <TouchableOpacity style={styles.primaryAction} onPress={handleWatchAds}>
            <LinearGradient
              colors={['#4CAF50', '#8BC34A']}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 0 }}
              style={styles.actionGradient}
            >
              <Ionicons name="play-circle" size={32} color="#FFFFFF" />
              <Text style={styles.actionTitle}>Guarda Video</Text>
              <Text style={styles.actionSubtitle}>Guadagna TrendyCoin</Text>
            </LinearGradient>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.secondaryAction,
              weeklyEarned >= 10 ? styles.payoutAvailable : styles.payoutDisabled
            ]}
            onPress={handlePayout}
            disabled={weeklyEarned < 10}
          >
            <View style={styles.actionContent}>
              <Ionicons
                name="wallet"
                size={28}
                color={weeklyEarned >= 10 ? "#4CAF50" : "#666666"}
              />
              <Text style={[
                styles.secondaryActionTitle,
                { color: weeklyEarned >= 10 ? "#FFFFFF" : "#666666" }
              ]}>
                Trasferisci al TrendyWallet
              </Text>
              <Text style={[
                styles.secondaryActionSubtitle,
                { color: weeklyEarned >= 10 ? "#4CAF50" : "#666666" }
              ]}>
                {weeklyEarned >= 10 ? `${weeklyEarned} disponibili` : `Minimo 10 TrendyCoin (hai ${weeklyEarned})`}
              </Text>
            </View>
          </TouchableOpacity>
        </View>

        {/* Come funziona */}
        <View style={styles.howItWorksContainer}>
          <Text style={styles.sectionTitle}>💡 Come funziona</Text>

          <View style={styles.stepContainer}>
            <View style={styles.step}>
              <View style={styles.stepNumber}>
                <Text style={styles.stepNumberText}>1</Text>
              </View>
              <View style={styles.stepContent}>
                <Text style={styles.stepTitle}>Guarda i video</Text>
                <Text style={styles.stepDescription}>
                  Guarda video sponsorizzati per 30 secondi
                </Text>
              </View>
            </View>

            <View style={styles.step}>
              <View style={styles.stepNumber}>
                <Text style={styles.stepNumberText}>2</Text>
              </View>
              <View style={styles.stepContent}>
                <Text style={styles.stepTitle}>Guadagna TrendyCoin</Text>
                <Text style={styles.stepDescription}>
                  Ricevi 0.1 TrendyCoin ogni 30 secondi
                </Text>
              </View>
            </View>

            <View style={styles.step}>
              <View style={styles.stepNumber}>
                <Text style={styles.stepNumberText}>3</Text>
              </View>
              <View style={styles.stepContent}>
                <Text style={styles.stepTitle}>Trasferisci quando vuoi</Text>
                <Text style={styles.stepDescription}>
                  Clicca "Trasferisci" per spostare i TrendyCoin nel TrendyWallet
                </Text>
              </View>
            </View>

            <View style={styles.step}>
              <View style={styles.stepNumber}>
                <Text style={styles.stepNumberText}>4</Text>
              </View>
              <View style={styles.stepContent}>
                <Text style={styles.stepTitle}>Usa nelle Live</Text>
                <Text style={styles.stepDescription}>
                  Spendi TrendyCoin per regali e super chat
                </Text>
              </View>
            </View>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#121212', // Sfondo scuro TrendyChat
    paddingTop: StatusBar.currentHeight || 0,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#121212',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
    height: 60,
  },
  backButton: {
    padding: 8,
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 18, // Dimensione identica a MyGroupsScreen
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  infoButton: {
    padding: 8,
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
    backgroundColor: '#121212', // Sfondo scuro TrendyChat
  },
  balanceCard: {
    borderRadius: 16,
    padding: 24,
    marginVertical: 20,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
  },
  balanceHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  balanceTitle: {
    fontSize: 18,
    color: '#FFFFFF',
    marginRight: 10,
  },
  coinIcon: {
    marginLeft: 5,
  },
  balanceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 10,
  },
  balanceItem: {
    alignItems: 'center',
    flex: 1,
  },
  balanceDivider: {
    width: 1,
    height: 40,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    marginHorizontal: 20,
  },
  balanceAmount: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 5,
  },
  balanceAmountPending: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#FFD700',
    marginBottom: 5,
  },
  balanceSubtitle: {
    fontSize: 14,
    color: '#FFFFFF',
    opacity: 0.9,
  },
  statsContainer: {
    marginBottom: 20,
  },
  statsCard: {
    backgroundColor: '#1E1E1E', // Card scura TrendyChat
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderLeftWidth: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 3,
  },
  statsHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  statsTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF', // Testo bianco TrendyChat
    marginLeft: 8,
  },
  statsValue: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  statsSubtitle: {
    fontSize: 14,
    color: '#B0B0B0', // Testo secondario TrendyChat
  },
  actionsContainer: {
    marginBottom: 30,
  },
  primaryAction: {
    marginBottom: 16,
    borderRadius: 16,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 5,
  },
  actionGradient: {
    padding: 24,
    alignItems: 'center',
  },
  actionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginTop: 8,
  },
  actionSubtitle: {
    fontSize: 14,
    color: '#FFFFFF',
    opacity: 0.9,
    marginTop: 4,
  },
  secondaryAction: {
    backgroundColor: '#1E1E1E', // Card scura TrendyChat
    borderRadius: 12,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 3,
  },
  actionContent: {
    alignItems: 'center',
  },
  secondaryActionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF', // Testo bianco TrendyChat
    marginTop: 8,
  },
  secondaryActionSubtitle: {
    fontSize: 14,
    color: '#B0B0B0', // Testo secondario TrendyChat
    marginTop: 4,
  },
  payoutAvailable: {
    borderWidth: 2,
    borderColor: '#4CAF50',
    backgroundColor: '#1E1E1E',
  },
  payoutDisabled: {
    backgroundColor: '#2A2A2A',
    opacity: 0.6,
  },
  howItWorksContainer: {
    marginBottom: 30,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFFFFF', // Testo bianco TrendyChat
    marginBottom: 20,
  },
  stepContainer: {
    backgroundColor: '#1E1E1E', // Card scura TrendyChat
    borderRadius: 12,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 3,
  },
  step: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  stepNumber: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#1E88E5',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  stepNumberText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  stepContent: {
    flex: 1,
  },
  stepTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF', // Testo bianco TrendyChat
    marginBottom: 4,
  },
  stepDescription: {
    fontSize: 14,
    color: '#B0B0B0', // Testo secondario TrendyChat
    lineHeight: 20,
  },
});

export default EarnScreen;
