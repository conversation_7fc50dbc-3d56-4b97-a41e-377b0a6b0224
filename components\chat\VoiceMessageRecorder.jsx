import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Animated,
  Easing,
  Platform,
  PermissionsAndroid,
  Alert,
} from 'react-native';
import { Audio } from 'expo-av';
import { Ionicons } from '@expo/vector-icons';
import * as Haptics from 'expo-haptics';
import { useTheme } from '../../hooks/useTheme';
import { LinearGradient } from 'expo-linear-gradient';
import RecordingManager from '../../utils/RecordingManager';

const VoiceMessageRecorder = ({ onRecordingComplete, onCancel }) => {
  const { theme } = useTheme();
  const [recording, setRecording] = useState(null);
  const [isRecording, setIsRecording] = useState(false);
  const [recordingDuration, setRecordingDuration] = useState(0);
  const [recordingPermission, setRecordingPermission] = useState(false);
  const [audioUri, setAudioUri] = useState(null);
  const [isSlideToCancel, setIsSlideToCancel] = useState(false);

  const animatedWidth = useRef(new Animated.Value(0)).current;
  const animatedOpacity = useRef(new Animated.Value(0)).current;
  const animatedMicScale = useRef(new Animated.Value(1)).current;
  const animatedWaveOpacity = useRef(new Animated.Value(0)).current;
  const timerRef = useRef(null);
  const touchStartXRef = useRef(0);

  useEffect(() => {
    requestPermission();
    return () => {
      stopRecording();
      if (timerRef.current) clearInterval(timerRef.current);
    };
  }, []);

  const requestPermission = async () => {
    if (Platform.OS === 'android') {
      try {
        const grants = await PermissionsAndroid.requestMultiple([
          PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE,
          PermissionsAndroid.PERMISSIONS.READ_EXTERNAL_STORAGE,
          PermissionsAndroid.PERMISSIONS.RECORD_AUDIO,
        ]);

        if (
          grants['android.permission.WRITE_EXTERNAL_STORAGE'] === PermissionsAndroid.RESULTS.GRANTED &&
          grants['android.permission.READ_EXTERNAL_STORAGE'] === PermissionsAndroid.RESULTS.GRANTED &&
          grants['android.permission.RECORD_AUDIO'] === PermissionsAndroid.RESULTS.GRANTED
        ) {
          setRecordingPermission(true);
        } else {
          Alert.alert('Permessi mancanti', 'Per registrare messaggi vocali, concedi i permessi necessari.');
        }
      } catch (err) {
        console.warn(err);
        Alert.alert('Errore permessi', 'Errore durante la richiesta dei permessi.');
      }
    } else {
      const { status } = await Audio.requestPermissionsAsync();
      setRecordingPermission(status === 'granted');

      if (status !== 'granted') {
        Alert.alert('Permessi mancanti', 'Per registrare messaggi vocali, concedi i permessi necessari.');
      }
    }
  };

  const startRecording = async () => {
    if (!recordingPermission) {
      await requestPermission();
      if (!recordingPermission) return;
    }

    try {
      // Feedback aptico
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);

      // Usa RecordingManager centralizzato
      console.log('🎤 VoiceMessageRecorder: Avvio tramite RecordingManager');
      const recording = await RecordingManager.startRecording();
      setRecording(recording);
      setIsRecording(true);
      setRecordingDuration(0);

      // Avvia il timer
      timerRef.current = setInterval(() => {
        setRecordingDuration(prev => prev + 1);
      }, 1000);

      // Animazioni
      Animated.parallel([
        Animated.timing(animatedWidth, {
          toValue: 1,
          duration: 300,
          useNativeDriver: false,
        }),
        Animated.timing(animatedOpacity, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.loop(
          Animated.sequence([
            Animated.timing(animatedMicScale, {
              toValue: 1.2,
              duration: 500,
              easing: Easing.inOut(Easing.ease),
              useNativeDriver: true,
            }),
            Animated.timing(animatedMicScale, {
              toValue: 1,
              duration: 500,
              easing: Easing.inOut(Easing.ease),
              useNativeDriver: true,
            }),
          ])
        ),
        Animated.timing(animatedWaveOpacity, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start();

    } catch (error) {
      console.error('Errore durante l\'avvio della registrazione:', error);
      Alert.alert('Errore', 'Impossibile avviare la registrazione audio.');
    }
  };

  const stopRecording = async () => {
    if (!recording) return;

    try {
      // Feedback aptico
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);

      // Ferma il timer
      if (timerRef.current) {
        clearInterval(timerRef.current);
        timerRef.current = null;
      }

      // Usa RecordingManager centralizzato
      console.log('🛑 VoiceMessageRecorder: Stop tramite RecordingManager');
      const uri = await RecordingManager.stopRecording();
      setAudioUri(uri);
      setRecording(null);
      setIsRecording(false);

      // Animazioni
      Animated.parallel([
        Animated.timing(animatedWidth, {
          toValue: 0,
          duration: 300,
          useNativeDriver: false,
        }),
        Animated.timing(animatedOpacity, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(animatedWaveOpacity, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start();

      // Callback con l'URI della registrazione
      if (uri && recordingDuration > 1 && !isSlideToCancel) {
        onRecordingComplete({
          uri,
          duration: recordingDuration,
          type: 'audio/aac', // Cambiato da audio/m4a a audio/aac per compatibilità server
        });
      } else if (isSlideToCancel || recordingDuration <= 1) {
        onCancel();
      }

    } catch (error) {
      console.error('Errore durante l\'arresto della registrazione:', error);
      Alert.alert('Errore', 'Impossibile completare la registrazione audio.');
      onCancel();
    }
  };

  const cancelRecording = async () => {
    setIsSlideToCancel(true);
    await stopRecording();
    setIsSlideToCancel(false);
  };

  const formatDuration = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const handleTouchStart = (event) => {
    touchStartXRef.current = event.nativeEvent.pageX;
    startRecording();
  };

  const handleTouchMove = (event) => {
    if (!isRecording) return;

    const currentX = event.nativeEvent.pageX;
    const diff = touchStartXRef.current - currentX;

    // Se l'utente scorre a sinistra di più di 100px, annulla la registrazione
    if (diff > 100) {
      cancelRecording();
    }
  };

  const handleTouchEnd = () => {
    if (isRecording) {
      stopRecording();
    }
  };

  return (
    <View style={styles.container}>
      <TouchableOpacity
        style={[styles.recordButton, { backgroundColor: theme.colors.primary }]}
        onPressIn={handleTouchStart}
        onPressOut={handleTouchEnd}
        onTouchMove={handleTouchMove}
        activeOpacity={0.7}
      >
        <Animated.View style={{ transform: [{ scale: animatedMicScale }] }}>
          <Ionicons name="mic" size={24} color="#FFFFFF" />
        </Animated.View>
      </TouchableOpacity>

      <Animated.View
        style={[
          styles.recordingContainer,
          {
            width: animatedWidth.interpolate({
              inputRange: [0, 1],
              outputRange: ['0%', '80%'],
            }),
            opacity: animatedOpacity,
            backgroundColor: theme.colors.card,
          },
        ]}
      >
        <Animated.View style={[styles.waveContainer, { opacity: animatedWaveOpacity }]}>
          {[...Array(5)].map((_, index) => (
            <Animated.View
              key={index}
              style={[
                styles.wave,
                {
                  height: 10 + Math.random() * 15,
                  backgroundColor: theme.colors.primary,
                  opacity: 0.6 + Math.random() * 0.4,
                },
              ]}
            />
          ))}
        </Animated.View>

        <Text style={[styles.timerText, { color: theme.colors.text }]}>
          {formatDuration(recordingDuration)}
        </Text>

        <Text style={[styles.slideToCancelText, { color: theme.colors.textSecondary }]}>
          ← Scorri per annullare
        </Text>
      </Animated.View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
    paddingHorizontal: 8,
    paddingVertical: 4,
  },
  recordButton: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  recordingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderRadius: 24,
    paddingHorizontal: 16,
    paddingVertical: 8,
    height: 48,
  },
  waveContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    width: 60,
  },
  wave: {
    width: 3,
    marginHorizontal: 2,
    borderRadius: 1,
  },
  timerText: {
    fontSize: 16,
    fontWeight: '500',
  },
  slideToCancelText: {
    fontSize: 12,
    marginLeft: 8,
  },
});

export default VoiceMessageRecorder;
