// Configurazione WebRTC per TrendyChat - Architettura Ibrida con Failover
module.exports = {
  // 🌐 ARCHITETTURA IBRIDA WEBRTC CON FAILOVER
  // Render (Primario) + SuperMicron (Fallback): Signaling | SuperMicron: SFU/mediasoup | VPS: TURN

  // Server STUN/TURN per la connettività WebRTC
  iceServers: [
    // Server STUN Google (pubblici)
    { urls: 'stun:stun.l.google.com:19302' },
    { urls: 'stun:stun1.l.google.com:19302' },
    { urls: 'stun:stun2.l.google.com:19302' },

    // Server TURN TrendyChat (VPS - da configurare)
    {
      urls: 'turn:turn.trendychat.it:3478',
      credential: 'TrendyChat2025!',
      username: 'trendychat'
    },

    // Fallback TURN server
    {
      urls: 'turn:numb.viagenie.ca',
      credential: 'muazkh',
      username: '<EMAIL>'
    }
  ],

  // 🎯 SIGNALING SERVERS CON FAILOVER AUTOMATICO
  signalingServers: [
    // Server Primario: Render (alta disponibilità rete)
    {
      url: 'wss://trendychat-signaling.onrender.com',
      type: 'render',
      priority: 1,
      timeout: 5000,
      maxRetries: 3,
      purpose: 'primary signaling server'
    },

    // Server Fallback: SuperMicron (backup affidabile)
    {
      url: 'wss://signaling.trendychat.it',
      type: 'supermicron',
      priority: 2,
      timeout: 5000,
      maxRetries: 3,
      purpose: 'fallback signaling server'
    }
  ],

  // 🎯 SERVER CONFIGURATION - ARCHITETTURA IBRIDA
  servers: {
    // SFU SuperMicron: Gestisce chiamate di gruppo con mediasoup
    sfu: {
      url: 'wss://sfu.trendychat.it',
      type: 'supermicron',
      purpose: 'group calls with mediasoup',
      maxUsers: 50,
      workers: 24
    },

    // VPS: Gestisce traffico TURN (da configurare)
    turn: {
      url: 'turn.trendychat.it',
      type: 'vps',
      purpose: 'network traffic relay',
      port: 3478
    }
  },

  // 🔄 CONFIGURAZIONE FAILOVER
  failover: {
    // Timeout per tentativo di connessione
    connectionTimeout: 5000,

    // Numero massimo di tentativi per server
    maxRetries: 3,

    // Delay tra i tentativi (ms)
    retryDelay: 2000,

    // Tempo di attesa prima di provare il fallback
    fallbackDelay: 1000,

    // Intervallo per health check (ms)
    healthCheckInterval: 30000,

    // Timeout per health check
    healthCheckTimeout: 3000
  },

  // 📞 CALL ROUTING - Logica di instradamento
  callRouting: {
    // Chiamate 1-a-1: usa Render signaling
    oneToOne: {
      server: 'signaling',
      maxParticipants: 2,
      useP2P: true,
      useSFU: false
    },

    // Chiamate di gruppo: usa SuperMicron SFU
    group: {
      server: 'sfu',
      maxParticipants: 50,
      useP2P: false,
      useSFU: true,
      minParticipantsForSFU: 3
    }
  },

  // Configurazione per le chiamate audio
  audioConstraints: {
    echoCancellation: true,
    noiseSuppression: true,
    autoGainControl: true,
    sampleRate: 48000,
    channels: 1
  },

  // Configurazione per le chiamate video
  videoConstraints: {
    facingMode: 'user',
    width: { ideal: 640, max: 1280 },
    height: { ideal: 480, max: 720 },
    frameRate: { ideal: 24, max: 30 }
  },

  // URL del server di signaling (backward compatibility)
  signalingServer: 'wss://trendychat-signaling.onrender.com',

  // Timeout per le chiamate (in millisecondi)
  callTimeout: 30000, // 30 secondi

  // 🎥 CONFIGURAZIONE SFU MEDIASOUP
  sfuConfig: {
    url: 'wss://sfu.trendychat.it',
    transports: ['websocket'],
    reconnectionAttempts: 5,
    reconnectionDelay: 2000,
    timeout: 10000,

    // Configurazione mediasoup
    mediasoup: {
      worker: {
        rtcMinPort: 40000,
        rtcMaxPort: 49999,
        logLevel: 'warn'
      },
      router: {
        mediaCodecs: [
          {
            kind: 'audio',
            mimeType: 'audio/opus',
            clockRate: 48000,
            channels: 2
          },
          {
            kind: 'video',
            mimeType: 'video/VP8',
            clockRate: 90000
          },
          {
            kind: 'video',
            mimeType: 'video/h264',
            clockRate: 90000,
            parameters: {
              'packetization-mode': 1,
              'profile-level-id': '42e01f'
            }
          }
        ]
      }
    }
  },

  // Configurazione per le chiamate di gruppo
  groupCallConfig: {
    maxParticipants: 50, // Aumentato grazie al SFU
    layout: 'grid',
    useSFU: true,
    sfuUrl: 'wss://sfu.trendychat.it'
  }
};
