import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  StatusBar,
  Dimensions,
  Platform,
  TextInput,
  Alert,
  SafeAreaView,
  ActivityIndicator,
  PermissionsAndroid,
  Modal,
  ScrollView,
  Switch,
  Image,
  Animated,
  KeyboardAvoidingView,
  Keyboard,
  Easing,
  Pressable,
} from 'react-native';
import { Camera, useCameraDevices } from 'react-native-vision-camera';
import Icon from 'react-native-vector-icons/Ionicons';
import { useNavigation } from '@react-navigation/native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { Linking } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Slider from '@react-native-community/slider';
import { Gift, ALL_GIFTS } from '../../constants/gifts';

const { width, height } = Dimensions.get('window');

const LivePreviewScreen = () => {
  const navigation = useNavigation();
  const insets = useSafeAreaInsets();
  const camera = useRef(null);
  const [hasPermission, setHasPermission] = useState(false);
  const [currentDevice, setCurrentDevice] = useState(null);
  const devices = useCameraDevices();
  const device = currentDevice || (devices ? devices.find(d => d.position === 'front') : undefined);
  const scaleValue = useRef(new Animated.Value(1)).current;
  
  const [topicModalVisible, setTopicModalVisible] = useState(false);
  const [topicText, setTopicText] = useState('');
  const [savedTopic, setSavedTopic] = useState(null);
  
  const [isMuted, setIsMuted] = useState(false);
  const [settingsModalVisible, setSettingsModalVisible] = useState(false);
  const [videoQuality, setVideoQuality] = useState('HD');
  const [flashMode, setFlashMode] = useState('auto');
  const [microphoneEnabled, setMicrophoneEnabled] = useState(true);
  const [volume, setVolume] = useState(100);
  const [privacy, setPrivacy] = useState('all');
  const [comments, setComments] = useState('all');
  const [giftsEnabled, setGiftsEnabled] = useState(true);
  const [notificationsEnabled, setNotificationsEnabled] = useState(true);

  const [shareModalVisible, setShareModalVisible] = useState(false);
  const [effectsModalVisible, setEffectsModalVisible] = useState(false);
  const [selectedEffect, setSelectedEffect] = useState(null);

  const [goalModalVisible, setGoalModalVisible] = useState(false);
  const [selectedGift, setSelectedGift] = useState(null);
  const [goalAmount, setGoalAmount] = useState('');
  const [goalDescription, setGoalDescription] = useState('');
  const [savedGoal, setSavedGoal] = useState(null);
  const [goalCount, setGoalCount] = useState(0);
  const [goalTarget, setGoalTarget] = useState(100);
  const [goalQuantity, setGoalQuantity] = useState('100');

  const effects = [
    { id: 'none', name: 'Nessuno', icon: 'close-circle-outline' },
    { id: 'blur', name: 'Sfocato', icon: 'eye-outline' },
    { id: 'sepia', name: 'Seppia', icon: 'color-filter-outline' },
    { id: 'grayscale', name: 'Bianco e nero', icon: 'contrast-outline' },
    { id: 'vintage', name: 'Vintage', icon: 'film-outline' },
    { id: 'inverted', name: 'Invertito', icon: 'invert-mode-outline' }
  ];

  const [monetizationModalVisible, setMonetizationModalVisible] = useState(false);
  const [monetizationEnabled, setMonetizationEnabled] = useState(false);
  const [giftGoal, setGiftGoal] = useState(1000);
  const [giftGoalProgress, setGiftGoalProgress] = useState(0);
  const [goalProgress, setGoalProgress] = useState(0);

  const [pollModalVisible, setPollModalVisible] = useState(false);
  const [pollQuestion, setPollQuestion] = useState('');

  const [showSettingsIcons, setShowSettingsIcons] = useState(false);

  const GIFTS = [
    { id: 'rose', name: 'Rosa', icon: 'rose', amount: 100, animated: true },
    { id: 'heart', name: 'Cuore', icon: 'heart', amount: 50, animated: true },
    { id: 'star', name: 'Stella', icon: 'star', amount: 200, animated: true },
    { id: 'diamond', name: 'Diamante', icon: 'diamond', amount: 500, animated: true },
    { id: 'crown', name: 'Corona', icon: 'crown', amount: 1000, animated: true },
    { id: 'rocket', name: 'Razzo', icon: 'rocket', amount: 300, animated: true },
    { id: 'gift', name: 'Regalo', icon: 'gift', amount: 150, animated: false },
    { id: 'balloon', name: 'Palloncino', icon: 'balloon', amount: 75, animated: true },
    { id: 'confetti', name: 'Coriandoli', icon: 'sparkles', amount: 125, animated: true },
    { id: 'trophy', name: 'Trofeo', icon: 'trophy', amount: 400, animated: true }
  ];

  const animateScale = () => {
    Animated.sequence([
      Animated.timing(scaleValue, {
        toValue: 1.1,
        duration: 100,
        useNativeDriver: true,
      }),
      Animated.timing(scaleValue, {
        toValue: 1,
        duration: 100,
        useNativeDriver: true,
      }),
    ]).start();
  };

  const toggleCamera = () => {
    const newPosition = device?.position === 'front' ? 'back' : 'front';
    const newDevice = devices?.find(d => d.position === newPosition);
    if (newDevice) {
      setCurrentDevice(newDevice);
    }
  };

  const showEffectsModal = () => {
    setEffectsModalVisible(true);
  };

  const showSettingsModal = () => {
    setSettingsModalVisible(true);
  };

  const startLive = () => {
    if (!savedTopic) {
      Alert.alert('Errore', 'Inserisci un argomento per la tua live');
      return;
    }
    // Qui implementeremo la logica per iniziare la live
    console.log('Inizio live con:', {
      topic: savedTopic,
      settings: {
        videoQuality,
        flashMode,
        microphoneEnabled,
        privacy,
        comments,
        giftsEnabled,
        notificationsEnabled
      }
    });
  };

  useEffect(() => {
    requestPermissions();
  }, []);

  const requestPermissions = async () => {
    const cameraPermission = await Camera.requestCameraPermission();
    const microphonePermission = await Camera.requestMicrophonePermission();
    setHasPermission(cameraPermission === 'granted' && microphonePermission === 'granted');
  };

  const handleSetGoal = () => {
    if (selectedGift) {
      const quantity = parseInt(goalQuantity) || 100;
      const newTarget = selectedGift.value * quantity;
      
      // Aggiorno tutti gli stati contemporaneamente
      setGoalTarget(newTarget);
      setGoalCount(0);
      setGoalProgress(0);
      setGiftGoalProgress(0);
      
      // Salvo l'obiettivo in AsyncStorage
      AsyncStorage.setItem('liveGoal', JSON.stringify({
        target: newTarget,
        count: 0,
        giftId: selectedGift.id,
        quantity: quantity,
        giftValue: selectedGift.value,
        giftName: selectedGift.name,
        giftIcon: selectedGift.icon,
        giftCategory: selectedGift.category,
        giftCount: 0,
        giftTarget: quantity
      }));
      
      setGoalModalVisible(false);
    }
  };

  // Aggiungo un useEffect per caricare l'obiettivo salvato
  useEffect(() => {
    const loadSavedGoal = async () => {
      try {
        const savedGoal = await AsyncStorage.getItem('liveGoal');
        if (savedGoal) {
          const goal = JSON.parse(savedGoal);
          setGoalTarget(goal.target);
          setGoalCount(goal.count);
          setGoalProgress((goal.count / goal.target) * 100);
          setGiftGoalProgress((goal.count / goal.target) * 100);
          
          // Trovo il regalo corrispondente
          const gift = ALL_GIFTS.find(g => g.id === goal.giftId);
          if (gift) {
            setSelectedGift(gift);
          }
        }
      } catch (error) {
        console.error('Errore nel caricamento dell\'obiettivo:', error);
      }
    };

    loadSavedGoal();
  }, []);

  const handleSettingsSave = async () => {
    try {
      // Salva le impostazioni in AsyncStorage
      await AsyncStorage.setItem('liveSettings', JSON.stringify({
        videoQuality,
        flashMode,
        isMuted,
        volume,
        privacy,
        comments,
        giftsEnabled,
        notificationsEnabled
      }));

      // Applica le impostazioni alla camera
      if (camera.current) {
        // Imposta la qualità video
        if (videoQuality === 'SD') {
          camera.current.setVideoQuality('480p');
        } else if (videoQuality === 'HD') {
          camera.current.setVideoQuality('720p');
        } else if (videoQuality === 'FHD') {
          camera.current.setVideoQuality('1080p');
        }

        // Imposta il flash
        camera.current.setFlashMode(flashMode);

        // Imposta il muto
        camera.current.setMuted(isMuted);

        // Imposta il volume
        camera.current.setVolume(volume);
      }

      // Chiudi la modale
      setSettingsModalVisible(false);
      
      // Mostra un feedback all'utente
      // Toast.show({
      //   type: 'success',
      //   text1: 'Impostazioni salvate',
      //   position: 'bottom',
      // });
    } catch (error) {
      console.error('Errore nel salvataggio delle impostazioni:', error);
      // Toast.show({
      //   type: 'error',
      //   text1: 'Errore nel salvataggio',
      //   position: 'bottom',
      // });
    }
  };

  useEffect(() => {
    const loadSettings = async () => {
      try {
        const savedSettings = await AsyncStorage.getItem('liveSettings');
        if (savedSettings) {
          const settings = JSON.parse(savedSettings);
          setVideoQuality(settings.videoQuality);
          setFlashMode(settings.flashMode);
          setIsMuted(settings.isMuted);
          setVolume(settings.volume);
          setPrivacy(settings.privacy);
          setComments(settings.comments);
          setGiftsEnabled(settings.giftsEnabled);
          setNotificationsEnabled(settings.notificationsEnabled);
        }
      } catch (error) {
        console.error('Errore nel caricamento delle impostazioni:', error);
      }
    };

    loadSettings();
  }, []);

  if (!hasPermission) {
    return (
      <View style={styles.container}>
        <Text style={styles.permissionText}>
          Per favore concedi i permessi per la fotocamera e il microfono
        </Text>
        <TouchableOpacity style={styles.permissionButton} onPress={requestPermissions}>
          <Text style={styles.permissionButtonText}>Concedi Permessi</Text>
        </TouchableOpacity>
      </View>
    );
  }

  if (!device) {
    return (
      <View style={styles.container}>
        <ActivityIndicator size="large" color="#FF3B7C" />
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Camera
        ref={camera}
        style={StyleSheet.absoluteFill}
        device={device}
        isActive={true}
        video={true}
        audio={true}
        enableZoomGesture
      />

      {/* Titolo con foto profilo */}
      <View style={[styles.titleContainer, { 
        top: Platform.OS === 'ios' ? insets.top + 30 : 30,
        left: 15,
        right: width * 0.25,
      }]}>
        <View style={styles.profileContainer}>
          <Image 
            source={{ uri: 'file:///data/user/0/com.trendychat.app/cache/ImagePicker/9119d281-8528-4dab-8676-29775ee9337d.jpeg' }}
            style={styles.profileImage}
          />
          <View style={styles.titleTextContainer}>
            <Text style={styles.titleText}>
              {savedTopic ? savedTopic : 'live d\'intrattenimento di'}
            </Text>
            <Text style={styles.usernameText}>Tony</Text>
          </View>
        </View>
        <TouchableOpacity 
          style={styles.editButton}
          onPress={() => setTopicModalVisible(true)}
        >
          <Text style={styles.editText}>Modifica</Text>
        </TouchableOpacity>
      </View>

      {/* Obiettivo Live Cliccabile */}
      <TouchableOpacity 
        style={[styles.goalContainer, {
          top: Platform.OS === 'ios' ? insets.top + 140 : 140,
          left: 15,
          right: width * 0.25,
        }]}
        onPress={() => setGoalModalVisible(true)}
      >
        <View style={styles.goalContent}>
          <Icon name="trophy" size={20} color="#FF3B7C" />
          <Text style={styles.goalText}>Obiettivo Live</Text>
          {selectedGift && (
            <>
              <Text style={styles.giftIcon}>{selectedGift.icon}</Text>
              <Text style={styles.goalCounter}>
                {Math.floor(goalCount / selectedGift.value)}/{goalQuantity}
              </Text>
            </>
          )}
        </View>
        <View style={styles.goalProgressBar}>
          <View 
            style={[
              styles.goalProgress, 
              { width: `${Math.min((goalCount / goalTarget) * 100, 100)}%` }
            ]} 
          />
        </View>
      </TouchableOpacity>

      {/* Pulsanti argomento e obiettivo */}
      <View style={[styles.iconGrid, { top: Platform.OS === 'ios' ? insets.top + 100 : 100 }]}>
        <TouchableOpacity 
          style={styles.iconButton}
          onPress={() => {
            setShowSettingsIcons(!showSettingsIcons);
            animateScale();
          }}
        >
          <View style={styles.iconBackground}>
            <Icon name={showSettingsIcons ? "chevron-down" : "chevron-up"} size={32} color="#fff" />
          </View>
          <Text style={styles.iconText}>{showSettingsIcons ? "Meno" : "Più"}</Text>
        </TouchableOpacity>
        <View style={[styles.iconRow, !showSettingsIcons && styles.iconRowHidden]}>
          <TouchableOpacity style={styles.iconButton} onPress={toggleCamera}>
            <View style={styles.iconBackground}>
              <Icon name="sync-outline" size={32} color="#fff" />
            </View>
            <Text style={styles.iconText}>Capovolgi</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.iconButton}>
            <View style={styles.iconBackground}>
              <Icon name="color-wand" size={32} color="#fff" />
            </View>
            <Text style={styles.iconText}>Migliora</Text>
          </TouchableOpacity>
          <TouchableOpacity 
            style={styles.iconButton}
            onPress={() => {
              console.log('Effetti button pressed');
              showEffectsModal();
            }}
          >
            <View style={styles.iconBackground}>
              <Icon name="sparkles-outline" size={32} color="#fff" />
            </View>
            <Text style={styles.iconText}>Effetti</Text>
          </TouchableOpacity>
          <TouchableOpacity 
            style={styles.iconButton}
            onPress={showSettingsModal}
          >
            <View style={styles.iconBackground}>
              <Icon name="settings-outline" size={32} color="#fff" />
            </View>
            <Text style={styles.iconText}>Impost.</Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Pulsante Inizia Live */}
      <TouchableOpacity 
        style={[styles.startLiveButton, { bottom: insets.bottom + 30 }]}
        onPress={startLive}
      >
        <Text style={styles.startLiveText}>Vai in live</Text>
      </TouchableOpacity>

      {/* Modal Argomento */}
      <Modal
        visible={topicModalVisible}
        transparent
        animationType="slide"
        onRequestClose={() => setTopicModalVisible(false)}
      >
        <View style={[styles.modalContent, { 
          top: '35%',
          maxHeight: '65%',
          height: 'auto',
          minHeight: 300
        }]}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Argomento Live</Text>
            <TouchableOpacity onPress={() => setTopicModalVisible(false)}>
              <Icon name="close" size={24} color="#fff" />
            </TouchableOpacity>
          </View>

          <ScrollView 
            style={[styles.modalBody, { 
              maxHeight: '100%'
            }]}
            contentContainerStyle={{ paddingBottom: 20 }}
          >
            <View style={styles.inputContainer}>
              <Icon name="pricetag-outline" size={22} color="#fff" style={styles.inputIcon} />
              <TextInput
                style={styles.topicInput}
                placeholder="Inserisci un argomento per la tua live..."
                placeholderTextColor="rgba(255,255,255,0.6)"
                value={topicText}
                onChangeText={setTopicText}
                maxLength={50}
                autoFocus
              />
            </View>
            
            <Text style={styles.characterCount}>{topicText.length}/50</Text>

            <TouchableOpacity 
              style={[styles.confirmButton, !topicText.trim() && styles.confirmButtonDisabled]}
              onPress={() => {
                if (topicText.trim()) {
                  setSavedTopic(topicText.trim());
                  setTopicModalVisible(false);
                  // Salva l'argomento in AsyncStorage
                  AsyncStorage.setItem('savedTopic', topicText.trim());
                }
              }}
              disabled={!topicText.trim()}
            >
              <Text style={styles.confirmButtonText}>Conferma</Text>
            </TouchableOpacity>
          </ScrollView>
        </View>
      </Modal>

      {/* Modal Impostazioni */}
      <Modal
        visible={settingsModalVisible}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setSettingsModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Impostazioni Live</Text>
              <TouchableOpacity onPress={() => setSettingsModalVisible(false)}>
                <Icon name="close" size={24} color="#fff" />
              </TouchableOpacity>
            </View>

            <ScrollView style={styles.settingsContainer}>
              {/* Qualità Video */}
              <View style={styles.settingItem}>
                <Text style={styles.settingLabel}>Qualità Video</Text>
                <View style={styles.qualityButtons}>
                  {['SD', 'HD', 'FHD'].map((quality) => (
                    <TouchableOpacity
                      key={quality}
                      style={[
                        styles.qualityButton,
                        videoQuality === quality && styles.qualityButtonSelected
                      ]}
                      onPress={() => setVideoQuality(quality)}
                    >
                      <Text style={[
                        styles.qualityButtonText,
                        videoQuality === quality && styles.qualityButtonTextSelected
                      ]}>
                        {quality}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>
              </View>

              {/* Flash */}
              <View style={styles.settingItem}>
                <Text style={styles.settingLabel}>Flash</Text>
                <View style={styles.qualityButtons}>
                  {['auto', 'on', 'off'].map((mode) => (
                    <TouchableOpacity
                      key={mode}
                      style={[
                        styles.qualityButton,
                        flashMode === mode && styles.qualityButtonSelected
                      ]}
                      onPress={() => setFlashMode(mode)}
                    >
                      <Text style={[
                        styles.qualityButtonText,
                        flashMode === mode && styles.qualityButtonTextSelected
                      ]}>
                        {mode === 'auto' ? 'Auto' : mode === 'on' ? 'On' : 'Off'}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>
              </View>

              {/* Microfono */}
              <View style={styles.settingItem}>
                <Text style={styles.settingLabel}>Microfono</Text>
                <Switch
                  value={!isMuted}
                  onValueChange={(value) => setIsMuted(!value)}
                  trackColor={{ false: '#666', true: '#FF5C8D' }}
                  thumbColor={isMuted ? '#fff' : '#fff'}
                />
              </View>

              {/* Volume */}
              <View style={styles.settingItem}>
                <Text style={styles.settingLabel}>Volume</Text>
                <Slider
                  style={styles.volumeSlider}
                  minimumValue={0}
                  maximumValue={1}
                  value={volume}
                  onValueChange={setVolume}
                  minimumTrackTintColor="#FF5C8D"
                  maximumTrackTintColor="#666"
                  thumbTintColor="#fff"
                />
              </View>

              {/* Privacy */}
              <View style={styles.settingItem}>
                <Text style={styles.settingLabel}>Privacy</Text>
                <View style={styles.qualityButtons}>
                  {['all', 'followers', 'friends'].map((mode) => (
                    <TouchableOpacity
                      key={mode}
                      style={[
                        styles.qualityButton,
                        privacy === mode && styles.qualityButtonSelected
                      ]}
                      onPress={() => setPrivacy(mode)}
                    >
                      <Text style={[
                        styles.qualityButtonText,
                        privacy === mode && styles.qualityButtonTextSelected
                      ]}>
                        {mode === 'all' ? 'Tutti' : mode === 'followers' ? 'Seguaci' : 'Amici'}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>
              </View>

              {/* Commenti */}
              <View style={styles.settingItem}>
                <Text style={styles.settingLabel}>Commenti</Text>
                <View style={styles.qualityButtons}>
                  {['all', 'followers', 'off'].map((mode) => (
                    <TouchableOpacity
                      key={mode}
                      style={[
                        styles.qualityButton,
                        comments === mode && styles.qualityButtonSelected
                      ]}
                      onPress={() => setComments(mode)}
                    >
                      <Text style={[
                        styles.qualityButtonText,
                        comments === mode && styles.qualityButtonTextSelected
                      ]}>
                        {mode === 'all' ? 'Tutti' : mode === 'followers' ? 'Seguaci' : 'Off'}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>
              </View>

              {/* Regali */}
              <View style={styles.settingItem}>
                <Text style={styles.settingLabel}>Regali</Text>
                <Switch
                  value={giftsEnabled}
                  onValueChange={setGiftsEnabled}
                  trackColor={{ false: '#666', true: '#FF5C8D' }}
                  thumbColor={giftsEnabled ? '#fff' : '#fff'}
                />
              </View>

              {/* Notifiche */}
              <View style={styles.settingItem}>
                <Text style={styles.settingLabel}>Notifiche</Text>
                <Switch
                  value={notificationsEnabled}
                  onValueChange={setNotificationsEnabled}
                  trackColor={{ false: '#666', true: '#FF5C8D' }}
                  thumbColor={notificationsEnabled ? '#fff' : '#fff'}
                />
              </View>
            </ScrollView>

            <TouchableOpacity 
              style={styles.setGoalButton}
              onPress={handleSettingsSave}
            >
              <Text style={styles.setGoalButtonText}>Salva Impostazioni</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>

      {/* Goal Modal */}
      <Modal
        visible={goalModalVisible}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setGoalModalVisible(false)}
      >
        <View style={[styles.modalOverlay, { backgroundColor: 'transparent' }]}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Imposta Obiettivo</Text>
              <TouchableOpacity
                style={styles.closeButton}
                onPress={() => setGoalModalVisible(false)}
              >
                <Text style={styles.closeButtonText}>✕</Text>
              </TouchableOpacity>
            </View>

            {selectedGift && (
              <View style={styles.goalProgressContainer}>
                <Text style={styles.goalProgressText}>
                  {goalCount} / {goalTarget} TC
                </Text>
                <View style={styles.progressBar}>
                  <View
                    style={[
                      styles.progressFill,
                      { width: `${(goalCount / goalTarget) * 100}%` }
                    ]}
                  />
                </View>
              </View>
            )}

            <View style={styles.quantityInputContainer}>
              <Text style={styles.quantityLabel}>Quantità:</Text>
              <TextInput
                style={styles.quantityInput}
                value={goalQuantity}
                onChangeText={setGoalQuantity}
                keyboardType="numeric"
                placeholder="100"
              />
            </View>

            <ScrollView 
              style={styles.giftsGrid}
              contentContainerStyle={styles.giftsGridContent}
            >
              {ALL_GIFTS.map((gift) => (
                <TouchableOpacity
                  key={gift.id}
                  style={[
                    styles.giftItem,
                    selectedGift?.id === gift.id && styles.selectedGiftItem
                  ]}
                  onPress={() => setSelectedGift(gift)}
                >
                  <Text style={styles.giftIcon}>{gift.icon}</Text>
                  <Text style={styles.giftName}>{gift.name}</Text>
                  <Text style={styles.giftValue}>{gift.value} TC</Text>
                </TouchableOpacity>
              ))}
            </ScrollView>

            <TouchableOpacity
              style={[
                styles.setGoalButton,
                !selectedGift && styles.setGoalButtonDisabled
              ]}
              onPress={handleSetGoal}
              disabled={!selectedGift}
            >
              <Text style={styles.setGoalButtonText}>Imposta Obiettivo</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>

      {/* Modal Effetti */}
      <Modal
        visible={effectsModalVisible}
        transparent
        animationType="slide"
        onRequestClose={() => setEffectsModalVisible(false)}
      >
        <View style={[styles.modalContent, { 
          top: '45%',
          maxHeight: '55%',
          height: 'auto',
          minHeight: 300
        }]}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Effetti</Text>
            <TouchableOpacity onPress={() => setEffectsModalVisible(false)}>
              <Icon name="close" size={24} color="#fff" />
            </TouchableOpacity>
          </View>

          <ScrollView 
            style={[styles.modalBody, { 
              maxHeight: '100%'
            }]}
            contentContainerStyle={{ paddingBottom: 20 }}
          >
            <View style={styles.effectsGrid}>
              {effects.map((effect) => (
                <TouchableOpacity
                  key={effect.id}
                  style={[
                    styles.effectItem,
                    selectedEffect?.id === effect.id && styles.effectItemActive
                  ]}
                  onPress={() => setSelectedEffect(effect)}
                >
                  <View style={styles.effectIconContainer}>
                    <Icon 
                      name={effect.icon} 
                      size={24} 
                      color={selectedEffect?.id === effect.id ? '#FF3B7C' : '#fff'} 
                    />
                  </View>
                  <Text style={[
                    styles.effectText,
                    selectedEffect?.id === effect.id && styles.effectTextActive
                  ]}>
                    {effect.name}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </ScrollView>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
  permissionText: {
    color: '#fff',
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 20,
  },
  permissionButton: {
    backgroundColor: '#FF3B7C',
    padding: 15,
    borderRadius: 25,
    alignItems: 'center',
  },
  permissionButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  titleContainer: {
    position: 'absolute',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.5)',
    padding: 12,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.1)',
  },
  profileContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  profileImage: {
    width: 44,
    height: 44,
    borderRadius: 22,
    marginRight: 12,
    borderWidth: 2,
    borderColor: '#FF3B7C',
  },
  titleTextContainer: {
    flex: 1,
  },
  titleText: {
    color: '#fff',
    fontSize: 14,
    opacity: 0.8,
    marginBottom: 2,
  },
  usernameText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  editButton: {
    backgroundColor: 'rgba(255,255,255,0.1)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 15,
    marginLeft: 10,
  },
  editText: {
    color: '#fff',
    fontSize: 12,
  },
  iconGrid: {
    position: 'absolute',
    right: 20,
    gap: 15,
  },
  iconRow: {
    gap: 15,
    opacity: 1,
    transform: [{ translateY: 0 }],
    transition: 'all 0.3s ease',
  },
  iconRowHidden: {
    opacity: 0,
    transform: [{ translateY: -20 }],
    height: 0,
  },
  iconButton: {
    alignItems: 'center',
  },
  iconBackground: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 5,
  },
  iconText: {
    color: '#fff',
    fontSize: 12,
    marginTop: 5,
  },
  startLiveButton: {
    position: 'absolute',
    left: width * 0.2,
    right: width * 0.2,
    backgroundColor: '#FF3B7C',
    padding: 12,
    borderRadius: 25,
    alignItems: 'center',
    shadowColor: '#FF3B7C',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 4.65,
    elevation: 8,
  },
  startLiveText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
    textTransform: 'uppercase',
  },
  modalContent: {
    backgroundColor: '#000',
    borderRadius: 20,
    padding: 20,
    width: '90%',
    maxHeight: '80%',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  modalTitle: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
  },
  modalBody: {
    flex: 1,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255,255,255,0.1)',
    borderRadius: 10,
    padding: 10,
    marginBottom: 10,
  },
  inputIcon: {
    marginRight: 10,
  },
  topicInput: {
    flex: 1,
    color: '#fff',
    fontSize: 16,
  },
  characterCount: {
    color: 'rgba(255,255,255,0.5)',
    fontSize: 12,
    textAlign: 'right',
    marginBottom: 20,
  },
  goalContainer: {
    position: 'absolute',
    backgroundColor: 'rgba(0,0,0,0.5)',
    borderRadius: 12,
    padding: 12,
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.1)',
  },
  goalContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 8,
  },
  goalText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: 'bold',
  },
  goalCounter: {
    color: '#FF3B7C',
    fontSize: 14,
    fontWeight: 'bold',
  },
  goalProgressBar: {
    height: 4,
    backgroundColor: 'rgba(255,255,255,0.2)',
    borderRadius: 2,
    overflow: 'hidden',
  },
  goalProgress: {
    height: '100%',
    backgroundColor: '#FF3B7C',
    borderRadius: 2,
  },
  settingsContainer: {
    maxHeight: 400,
  },
  settingItem: {
    marginBottom: 20,
  },
  settingLabel: {
    color: '#fff',
    fontSize: 16,
    marginBottom: 10,
  },
  qualityButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 10,
  },
  qualityButton: {
    flex: 1,
    padding: 10,
    borderRadius: 5,
    borderWidth: 1,
    borderColor: '#333',
    alignItems: 'center',
  },
  qualityButtonSelected: {
    backgroundColor: '#FF5C8D',
    borderColor: '#FF5C8D',
  },
  qualityButtonText: {
    color: '#fff',
  },
  qualityButtonTextSelected: {
    color: '#fff',
    fontWeight: 'bold',
  },
  volumeSlider: {
    width: '100%',
    height: 40,
  },
  confirmButton: {
    backgroundColor: '#FF3B7C',
    padding: 15,
    borderRadius: 25,
    alignItems: 'center',
    marginTop: 20,
  },
  confirmButtonDisabled: {
    backgroundColor: 'rgba(255,59,124,0.5)',
  },
  confirmButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  giftsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 15,
    marginBottom: 20,
  },
  giftButton: {
    alignItems: 'center',
    width: 80,
  },
  giftButtonActive: {
    transform: [{ scale: 1.1 }],
  },
  giftIconContainer: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: 'rgba(0,0,0,0.3)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  giftIcon: {
    fontSize: 24,
    marginRight: 4,
  },
  animatedBadge: {
    position: 'absolute',
    top: -5,
    right: -5,
    backgroundColor: '#FF3B7C',
    borderRadius: 10,
    width: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  giftName: {
    color: '#FF3B7C',
    fontSize: 14,
    fontWeight: 'bold',
    marginRight: 4,
  },
  giftNameActive: {
    color: '#FF3B7C',
    fontWeight: 'bold',
  },
  giftValue: {
    color: 'rgba(255,255,255,0.7)',
    fontSize: 12,
  },
  giftValueActive: {
    color: '#FF3B7C',
  },
  goalSectionTitle: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 15,
    paddingHorizontal: 10,
  },
  saveButton: {
    backgroundColor: '#FF3B7C',
    borderRadius: 10,
    padding: 15,
    alignItems: 'center',
    marginTop: 20,
    marginHorizontal: 10,
  },
  saveButtonDisabled: {
    opacity: 0.5,
  },
  saveButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  giftsGrid: {
    maxHeight: 300,
    marginBottom: 20,
  },
  giftsGridContent: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    gap: 10,
  },
  giftItem: {
    width: '30%',
    backgroundColor: '#000',
    borderRadius: 10,
    padding: 15,
    alignItems: 'center',
    marginBottom: 10,
    borderWidth: 1,
    borderColor: '#333',
  },
  selectedGiftItem: {
    backgroundColor: '#FF3B7C',
    borderColor: '#FF3B7C',
  },
  effectItem: {
    alignItems: 'center',
    width: 80,
  },
  effectItemActive: {
    transform: [{ scale: 1.1 }],
  },
  effectIconContainer: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: 'rgba(255,255,255,0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 5,
  },
  effectText: {
    color: '#fff',
    fontSize: 12,
    textAlign: 'center',
  },
  effectTextActive: {
    color: '#FF3B7C',
    fontWeight: 'bold',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'transparent',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: '#000',
    borderRadius: 20,
    padding: 20,
    width: '90%',
    maxHeight: '80%',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  closeButton: {
    padding: 5,
  },
  closeButtonText: {
    color: '#fff',
    fontSize: 20,
  },
  goalProgressContainer: {
    marginBottom: 20,
  },
  goalProgressText: {
    color: '#fff',
    fontSize: 16,
    marginBottom: 5,
  },
  progressBar: {
    height: 10,
    backgroundColor: '#333',
    borderRadius: 5,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#FF3B7C',
  },
  quantityInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  quantityLabel: {
    color: '#fff',
    fontSize: 16,
    marginRight: 10,
  },
  quantityInput: {
    backgroundColor: '#000',
    color: '#fff',
    padding: 10,
    borderRadius: 5,
    width: 100,
    borderWidth: 1,
    borderColor: '#FF3B7C',
  },
  setGoalButton: {
    backgroundColor: '#FF5C8D',
    padding: 15,
    borderRadius: 10,
    alignItems: 'center',
    shadowColor: '#FF5C8D',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 4.65,
    elevation: 8,
    opacity: 1,
  },
  setGoalButtonDisabled: {
    backgroundColor: '#FF5C8D',
    opacity: 0.5,
  },
  setGoalButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  effectsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    gap: 15,
    marginBottom: 20,
    paddingHorizontal: 10,
  },
  toastContainer: {
    backgroundColor: '#FF5C8D',
    padding: 15,
    borderRadius: 10,
    marginHorizontal: 20,
    marginBottom: 100,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  toastError: {
    backgroundColor: '#ff4d4d',
  },
  toastText: {
    color: '#fff',
    fontSize: 16,
    textAlign: 'center',
  },
});

export default LivePreviewScreen;
