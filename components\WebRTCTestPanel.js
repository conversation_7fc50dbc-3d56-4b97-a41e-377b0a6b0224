// 🧪 Pannello Test WebRTC per TrendyChat
// Componente per testare il failover signaling in tempo reale

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  StyleSheet,
  Alert
} from 'react-native';
import webrtcTest from '../utils/webrtcTest';
import SignalingStatus from './SignalingStatus';

const WebRTCTestPanel = ({ visible, onClose, userId }) => {
  const [testResults, setTestResults] = useState(null);
  const [isRunning, setIsRunning] = useState(false);
  const [currentTest, setCurrentTest] = useState('');

  useEffect(() => {
    if (visible) {
      // Auto-run basic test when panel opens
      runBasicTest();
    }
  }, [visible]);

  const runBasicTest = async () => {
    setIsRunning(true);
    setCurrentTest('Test di base...');
    
    try {
      const results = await webrtcTest.runAllTests(userId || 'test_user');
      setTestResults(results);
    } catch (error) {
      Alert.alert('Errore Test', error.message);
    } finally {
      setIsRunning(false);
      setCurrentTest('');
    }
  };

  const runRenderTest = async () => {
    setIsRunning(true);
    setCurrentTest('Test Render...');
    
    try {
      await webrtcTest.testRenderConnection();
      const results = webrtcTest.getTestSummary();
      setTestResults(results);
    } catch (error) {
      Alert.alert('Errore Test Render', error.message);
    } finally {
      setIsRunning(false);
      setCurrentTest('');
    }
  };

  const runSupermicronTest = async () => {
    setIsRunning(true);
    setCurrentTest('Test SuperMicron...');
    
    try {
      await webrtcTest.testSupermicronConnection();
      const results = webrtcTest.getTestSummary();
      setTestResults(results);
    } catch (error) {
      Alert.alert('Errore Test SuperMicron', error.message);
    } finally {
      setIsRunning(false);
      setCurrentTest('');
    }
  };

  const cleanup = () => {
    webrtcTest.cleanup();
    setTestResults(null);
  };

  if (!visible) return null;

  return (
    <View style={styles.overlay}>
      <View style={styles.panel}>
        <View style={styles.header}>
          <Text style={styles.title}>🧪 Test WebRTC Failover</Text>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <Text style={styles.closeText}>✕</Text>
          </TouchableOpacity>
        </View>

        <SignalingStatus style={styles.status} />

        <ScrollView style={styles.content}>
          {/* Pulsanti Test */}
          <View style={styles.buttonContainer}>
            <TouchableOpacity
              style={[styles.button, styles.primaryButton]}
              onPress={runBasicTest}
              disabled={isRunning}
            >
              <Text style={styles.buttonText}>
                {isRunning && currentTest === 'Test di base...' ? '⏳' : '🧪'} Test Completo
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.button, styles.renderButton]}
              onPress={runRenderTest}
              disabled={isRunning}
            >
              <Text style={styles.buttonText}>
                {isRunning && currentTest === 'Test Render...' ? '⏳' : '🌐'} Test Render
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.button, styles.supermicronButton]}
              onPress={runSupermicronTest}
              disabled={isRunning}
            >
              <Text style={styles.buttonText}>
                {isRunning && currentTest === 'Test SuperMicron...' ? '⏳' : '🏠'} Test SuperMicron
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.button, styles.cleanupButton]}
              onPress={cleanup}
              disabled={isRunning}
            >
              <Text style={styles.buttonText}>🧹 Cleanup</Text>
            </TouchableOpacity>
          </View>

          {/* Stato Test Corrente */}
          {isRunning && (
            <View style={styles.currentTest}>
              <Text style={styles.currentTestText}>{currentTest}</Text>
            </View>
          )}

          {/* Risultati Test */}
          {testResults && (
            <View style={styles.results}>
              <Text style={styles.resultsTitle}>📊 Risultati Test</Text>
              
              <View style={styles.summary}>
                <Text style={styles.summaryText}>
                  Totale: {testResults.total} | 
                  Passati: {testResults.passed} | 
                  Falliti: {testResults.failed} | 
                  Successo: {testResults.successRate}%
                </Text>
              </View>

              {testResults.results.map((result, index) => (
                <View key={index} style={styles.resultItem}>
                  <Text style={[
                    styles.resultText,
                    result.success ? styles.successText : styles.errorText
                  ]}>
                    {result.success ? '✅' : '❌'} {result.name}
                  </Text>
                  <Text style={styles.resultMessage}>{result.message}</Text>
                </View>
              ))}
            </View>
          )}
        </ScrollView>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
  },
  panel: {
    backgroundColor: 'white',
    borderRadius: 12,
    margin: 20,
    maxHeight: '80%',
    width: '90%',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  closeButton: {
    padding: 4,
  },
  closeText: {
    fontSize: 18,
    color: '#666',
  },
  status: {
    margin: 12,
    alignSelf: 'center',
  },
  content: {
    maxHeight: 400,
  },
  buttonContainer: {
    padding: 16,
    gap: 8,
  },
  button: {
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
    marginBottom: 8,
  },
  primaryButton: {
    backgroundColor: '#007AFF',
  },
  renderButton: {
    backgroundColor: '#00D4AA',
  },
  supermicronButton: {
    backgroundColor: '#FF6B6B',
  },
  cleanupButton: {
    backgroundColor: '#666',
  },
  buttonText: {
    color: 'white',
    fontWeight: '600',
  },
  currentTest: {
    padding: 12,
    backgroundColor: '#f0f0f0',
    margin: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  currentTestText: {
    fontSize: 14,
    color: '#666',
  },
  results: {
    padding: 16,
  },
  resultsTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
    color: '#333',
  },
  summary: {
    backgroundColor: '#f8f9fa',
    padding: 8,
    borderRadius: 6,
    marginBottom: 12,
  },
  summaryText: {
    fontSize: 12,
    color: '#666',
    textAlign: 'center',
  },
  resultItem: {
    marginBottom: 8,
    padding: 8,
    backgroundColor: '#f9f9f9',
    borderRadius: 6,
  },
  resultText: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 2,
  },
  successText: {
    color: '#28a745',
  },
  errorText: {
    color: '#dc3545',
  },
  resultMessage: {
    fontSize: 12,
    color: '#666',
  },
});

export default WebRTCTestPanel;
