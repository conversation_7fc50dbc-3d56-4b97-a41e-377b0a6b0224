// Utility ottimizzate per gestire le immagini
import { Image, Platform } from 'react-native';
import { Asset } from 'expo-asset';
import * as FileSystem from 'expo-file-system';
import * as ImageManipulator from 'expo-image-manipulator';
import { PERFORMANCE_CONFIG } from '../config/constants';

// Cache in memoria per le immagini
const imageCache = new Map();

// Funzione per precaricare le immagini in modo ottimizzato
export const preloadImages = async (images) => {
  try {
    // Filtra le immagini già caricate
    const uncachedImages = images.filter(image => {
      const cacheKey = typeof image === 'string' ? image : image.toString();
      return !imageCache.has(cacheKey);
    });

    if (uncachedImages.length === 0) {
      return true; // Tutte le immagini sono già in cache
    }

    // Carica le immagini in batch per migliorare le prestazioni
    const batchSize = 5;
    const batches = [];
    
    for (let i = 0; i < uncachedImages.length; i += batchSize) {
      batches.push(uncachedImages.slice(i, i + batchSize));
    }

    for (const batch of batches) {
      const imageAssets = batch.map((image) => {
        const cacheKey = typeof image === 'string' ? image : image.toString();
        
        if (typeof image === 'string') {
          return Image.prefetch(image).then(() => {
            imageCache.set(cacheKey, true);
            return true;
          });
        } else {
          return Asset.fromModule(image).downloadAsync().then(() => {
            imageCache.set(cacheKey, true);
            return true;
          });
        }
      });

      await Promise.all(imageAssets);
    }

    return true;
  } catch (error) {
    console.error('Errore nel precaricare le immagini:', error);
    return false;
  }
};

// Funzione per comprimere un'immagine in modo ottimizzato
export const compressImage = async (uri, options = {}) => {
  try {
    const {
      maxWidth = 1920,
      maxHeight = 1080,
      quality = 0.8,
      format = ImageManipulator.SaveFormat.JPEG,
      type = 'general'
    } = options;

    // Ottieni informazioni sul file originale
    const fileInfo = await FileSystem.getInfoAsync(uri);
    
    if (!fileInfo.exists) {
      throw new Error('File non trovato');
    }

    // Applica diverse strategie di compressione in base al tipo
    let compressionOptions;
    
    if (type === 'profile') {
      // Compressione aggressiva per immagini profilo
      compressionOptions = {
        width: 400,
        height: 400,
        mode: 'cover',
        quality: 0.6
      };
    } else if (type === 'thumbnail') {
      // Compressione per miniature
      compressionOptions = {
        width: 200,
        height: 200,
        mode: 'cover',
        quality: 0.5
      };
    } else if (type === 'chat') {
      // Compressione per immagini chat
      compressionOptions = {
        width: 800,
        height: 800,
        mode: 'inside',
        quality: 0.7
      };
    } else {
      // Compressione generale
      compressionOptions = {
        width: maxWidth,
        height: maxHeight,
        mode: 'inside',
        quality: quality
      };
    }

    // Esegui la compressione
    const result = await ImageManipulator.manipulateAsync(
      uri,
      [{ resize: compressionOptions }],
      { compress: compressionOptions.quality, format: format }
    );

    // Ottieni informazioni sul file compresso
    const compressedFileInfo = await FileSystem.getInfoAsync(result.uri);
    
    // Calcola la percentuale di riduzione
    const reduction = Math.round((1 - compressedFileInfo.size / fileInfo.size) * 100);
    
    return {
      uri: result.uri,
      width: result.width,
      height: result.height,
      size: compressedFileInfo.size,
      originalSize: fileInfo.size,
      reduction: reduction
    };
  } catch (error) {
    console.error('Errore nella compressione dell\'immagine:', error);
    // In caso di errore, restituisci l'URI originale
    return { uri };
  }
};

// Funzione per gestire le immagini in modo sicuro e ottimizzato
export const getImageSource = (source, fallback) => {
  try {
    // Se è un URL
    if (typeof source === 'string') {
      if (source.startsWith('http') || source.startsWith('https')) {
        return { uri: source };
      } else if (source.startsWith('file://') || source.startsWith('/')) {
        return { uri: source };
      }
    }

    // Se è un modulo locale
    return source || fallback;
  } catch (error) {
    console.error('Errore nel recupero dell\'immagine:', error);
    return fallback;
  }
};

// Funzione per pulire la cache delle immagini
export const clearImageCache = async () => {
  try {
    // Pulisci la cache in memoria
    imageCache.clear();
    
    // Pulisci la cache del filesystem
    if (FileSystem.cacheDirectory) {
      const cacheDir = `${FileSystem.cacheDirectory}images/`;
      
      try {
        const info = await FileSystem.getInfoAsync(cacheDir);
        if (info.exists) {
          await FileSystem.deleteAsync(cacheDir, { idempotent: true });
          await FileSystem.makeDirectoryAsync(cacheDir, { intermediates: true });
        }
      } catch (e) {
        console.warn('Errore nella pulizia della directory cache:', e);
      }
    }
    
    return true;
  } catch (error) {
    console.error('Errore nella pulizia della cache delle immagini:', error);
    return false;
  }
};

// Immagini di fallback
export const DEFAULT_IMAGES = {
  AVATAR: require('../assets/default-avatar.png'),
  LOGO: require('../assets/logo.png'),
  PLACEHOLDER: require('../assets/placeholder.png'),
};

export default {
  preloadImages,
  compressImage,
  getImageSource,
  clearImageCache,
  DEFAULT_IMAGES,
};
